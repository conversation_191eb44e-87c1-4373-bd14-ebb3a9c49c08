-- 市场营销部数据库表

-- 客户信息表
CREATE TABLE IF NOT EXISTS `marketing_customer` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `customer_code` varchar(50) NOT NULL COMMENT '客户代码',
  `customer_name` varchar(200) NOT NULL COMMENT '客户名称',
  `customer_phone` varchar(20) NOT NULL COMMENT '客户电话',
  `contact_person` varchar(50) NOT NULL COMMENT '客户联系人',
  `customer_address` text COMMENT '客户地址',
  `credit_code` varchar(100) DEFAULT NULL COMMENT '客户信用代码',
  `invoice_info` text COMMENT '客户开票信息',
  `status` varchar(20) DEFAULT '正常' COMMENT '客户状态',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_customer_code` (`customer_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='客户信息表';

-- 订单管理表
CREATE TABLE IF NOT EXISTS `marketing_order` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `order_date` date NOT NULL COMMENT '订单日期',
  `order_contract` varchar(100) DEFAULT NULL COMMENT '订单合同',
  `order_no` varchar(100) NOT NULL COMMENT '订单号',
  `customer_code` varchar(50) DEFAULT NULL COMMENT '客户代码',
  `customer_name` varchar(200) DEFAULT NULL COMMENT '客户名称',
  `order_status` varchar(20) DEFAULT '待生产' COMMENT '订单状态',
  `product_name` varchar(200) NOT NULL COMMENT '产品名称',
  `product_model` varchar(100) NOT NULL COMMENT '产品型号',
  `quantity` int(11) NOT NULL COMMENT '数量',
  `unit_price` decimal(10,2) NOT NULL COMMENT '单价',
  `total_amount` decimal(10,2) NOT NULL COMMENT '总金额',
  `delivery_date` date DEFAULT NULL COMMENT '交货日期',
  `ship_date` date DEFAULT NULL COMMENT '发货日期',
  `ship_address` text COMMENT '发货地址',
  `ship_quantity` int(11) DEFAULT 0 COMMENT '发货数量',
  `stock_quantity` int(11) DEFAULT 0 COMMENT '库存数量',
  `logistics_info` text COMMENT '物流信息',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_order_no` (`order_no`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='订单管理表';

-- 客户财务表
CREATE TABLE IF NOT EXISTS `marketing_finance` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `customer_id` int(11) NOT NULL COMMENT '客户ID',
  `customer_name` varchar(200) NOT NULL COMMENT '客户名称',
  `customer_code` varchar(50) NOT NULL COMMENT '客户代码',
  `total_order_count` int(11) DEFAULT 0 COMMENT '订单总数量',
  `total_order_amount` decimal(10,2) DEFAULT 0.00 COMMENT '客户总订单金额',
  `payment_info` text COMMENT '客户付款信息',
  `debt_info` text COMMENT '客户欠款信息',
  `invoice_time_info` text COMMENT '客户开票时间信息',
  `invoiced_amount` decimal(10,2) DEFAULT 0.00 COMMENT '已开发票金额',
  `invoice_numbers` text COMMENT '发票号',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_customer_id` (`customer_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='客户财务表';

-- 客户投诉表
CREATE TABLE IF NOT EXISTS `marketing_complaint` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `customer_code` varchar(50) NOT NULL COMMENT '客户代码',
  `customer_name` varchar(200) NOT NULL COMMENT '客户名称',
  `complaint_date` date NOT NULL COMMENT '投诉日期',
  `order_date` date DEFAULT NULL COMMENT '订单日期',
  `order_no` varchar(100) NOT NULL COMMENT '订单号',
  `complaint_batch_no` varchar(100) NOT NULL COMMENT '投诉批号',
  `complaint_description` text NOT NULL COMMENT '投诉描述',
  `complaint_feedback` text COMMENT '投诉反馈',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_complaint_batch_no` (`complaint_batch_no`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='客户投诉表';

-- 插入测试数据
INSERT INTO `marketing_customer` (`customer_code`, `customer_name`, `customer_phone`, `contact_person`, `customer_address`, `credit_code`, `invoice_info`, `status`) VALUES
('KH001', '北京科技有限公司', '010-12345678', '张三', '北京市朝阳区xxx街道xxx号', '91110000123456789X', '增值税专用发票', '正常'),
('KH002', '上海贸易有限公司', '021-87654321', '李四', '上海市浦东新区xxx路xxx号', '91310000123456789Y', '增值税普通发票', '冻结');

INSERT INTO `marketing_order` (`order_date`, `order_contract`, `order_no`, `customer_code`, `customer_name`, `order_status`, `product_name`, `product_model`, `quantity`, `unit_price`, `total_amount`, `delivery_date`, `ship_date`, `ship_address`, `ship_quantity`, `stock_quantity`, `logistics_info`) VALUES
('2024-01-15', 'HT20240115001', 'DD20240115001', 'KH001', '北京科技有限公司', '已发货', '工业传感器', 'GS-001', 100, 150.00, 15000.00, '2024-02-15', '2024-02-10', '北京市朝阳区xxx街道xxx号', 100, 50, '顺丰快递 SF1234567890'),
('2024-01-20', 'HT20240120001', 'DD20240120001', 'KH002', '上海贸易有限公司', '生产中', '控制面板', 'CP-002', 50, 300.00, 15000.00, '2024-03-20', NULL, '上海市浦东新区xxx路xxx号', 0, 20, NULL);

INSERT INTO `marketing_finance` (`customer_id`, `customer_name`, `customer_code`, `total_order_count`, `total_order_amount`, `payment_info`, `debt_info`, `invoice_time_info`, `invoiced_amount`, `invoice_numbers`) VALUES
(1, '北京科技有限公司', 'KH001', 15, 150000.00, '已付款120000元，未付款30000元', '欠款30000元', '2024-01-15,2024-02-15,2024-03-15', 120000.00, 'FP20240115001,FP20240215001,FP20240315001'),
(2, '上海贸易有限公司', 'KH002', 8, 80000.00, '已付款60000元，未付款20000元', '欠款20000元', '2024-01-20,2024-02-20', 60000.00, 'FP20240120001,FP20240220001');

INSERT INTO `marketing_complaint` (`customer_code`, `customer_name`, `complaint_date`, `order_date`, `order_no`, `complaint_batch_no`, `complaint_description`, `complaint_feedback`) VALUES
('KH001', '北京科技有限公司', '2024-01-15', '2024-01-10', 'DD20240110001', 'TS20240115001', '产品质量问题，传感器精度不达标，影响设备正常运行', '已安排技术人员现场检查，确认问题后立即更换产品'),
('KH002', '上海贸易有限公司', '2024-01-20', '2024-01-15', 'DD20240115001', 'TS20240120001', '发货延迟，影响项目进度，要求加快发货速度', '已协调生产部门优先生产，预计本周内发货'); 