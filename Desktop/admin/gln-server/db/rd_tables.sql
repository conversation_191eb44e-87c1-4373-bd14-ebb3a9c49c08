-- 技术研发部数据库表结构

-- 技术资料表
CREATE TABLE IF NOT EXISTS `rd_technical_data` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `data_name` varchar(200) NOT NULL COMMENT '资料名称',
  `order_number` varchar(100) NOT NULL COMMENT '订单号',
  `product_model` varchar(100) NOT NULL COMMENT '产品型号',
  `use_time` date NOT NULL COMMENT '使用时间',
  `file_name` varchar(200) DEFAULT NULL COMMENT '文件名称',
  `file_url` varchar(500) DEFAULT NULL COMMENT '文件URL',
  `submitter` varchar(50) NOT NULL COMMENT '提交人',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_order_number` (`order_number`),
  KEY `idx_product_model` (`product_model`),
  KEY `idx_use_time` (`use_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='技术资料表';

-- 产品表
CREATE TABLE IF NOT EXISTS `rd_product` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `product_name` varchar(200) NOT NULL COMMENT '产品名称',
  `product_model` varchar(100) NOT NULL COMMENT '产品型号',
  `bom_status` varchar(20) NOT NULL COMMENT 'BOM状态',
  `weight` decimal(10,2) NOT NULL COMMENT '产品重量',
  `unit` varchar(20) NOT NULL COMMENT '单位',
  `drawing` varchar(500) DEFAULT NULL COMMENT '图纸URL',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_product_model` (`product_model`),
  KEY `idx_product_name` (`product_name`),
  KEY `idx_bom_status` (`bom_status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='产品表';

-- BOM子件表
CREATE TABLE IF NOT EXISTS `rd_bom_sub_item` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `material_attribute` varchar(50) NOT NULL COMMENT '物料属性',
  `material_category` varchar(50) NOT NULL COMMENT '物料类别',
  `sub_item_number` varchar(100) NOT NULL COMMENT '子件编号',
  `product_name` varchar(200) NOT NULL COMMENT '产品名称',
  `specification` varchar(200) DEFAULT NULL COMMENT '产品规格',
  `other_info` varchar(200) DEFAULT NULL COMMENT '其他/箱号',
  `unit` varchar(20) NOT NULL COMMENT '单位',
  `supplier` varchar(100) NOT NULL COMMENT '供应商',
  `standard_price` decimal(10,2) NOT NULL COMMENT '标准进价',
  `incremental_base` decimal(10,2) NOT NULL COMMENT '增购基数',
  `minimum_purchase` decimal(10,2) NOT NULL COMMENT '最低采购量',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_sub_item_number` (`sub_item_number`),
  KEY `idx_material_attribute` (`material_attribute`),
  KEY `idx_material_category` (`material_category`),
  KEY `idx_product_name` (`product_name`),
  KEY `idx_supplier` (`supplier`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='BOM子件表';

-- 产品配件关系表
CREATE TABLE IF NOT EXISTS `rd_product_accessory` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `product_id` int(11) NOT NULL COMMENT '产品ID',
  `sub_item_id` int(11) NOT NULL COMMENT '子件ID',
  `is_substitute` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否替代料件',
  `standard_batch` decimal(10,3) NOT NULL DEFAULT 1.000 COMMENT '标准批量',
  `batch_usage` decimal(10,3) NOT NULL DEFAULT 1.000 COMMENT '批量用量',
  `net_usage` decimal(10,3) NOT NULL DEFAULT 1.000 COMMENT '净用量',
  `basic_unit` varchar(20) NOT NULL COMMENT '基本单位',
  `material_source` varchar(20) NOT NULL COMMENT '材料来源',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_product_sub_item` (`product_id`, `sub_item_id`),
  KEY `idx_product_id` (`product_id`),
  KEY `idx_sub_item_id` (`sub_item_id`),
  CONSTRAINT `fk_product_accessory_product` FOREIGN KEY (`product_id`) REFERENCES `rd_product` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_product_accessory_sub_item` FOREIGN KEY (`sub_item_id`) REFERENCES `rd_bom_sub_item` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='产品配件关系表';

-- 插入示例数据
INSERT INTO `rd_technical_data` (`data_name`, `order_number`, `product_model`, `use_time`, `file_name`, `file_url`, `submitter`) VALUES
('产品设计图纸', 'ORDER001', 'PRODUCT_A', '2024-01-15', 'design_drawing.pdf', '/uploads/design_drawing.pdf', 'admin'),
('技术规格书', 'ORDER002', 'PRODUCT_B', '2024-01-20', 'specification.pdf', '/uploads/specification.pdf', 'admin'),
('测试报告', 'ORDER003', 'PRODUCT_C', '2024-01-25', 'test_report.pdf', '/uploads/test_report.pdf', 'admin');

INSERT INTO `rd_product` (`product_name`, `product_model`, `bom_status`, `weight`, `unit`, `drawing`) VALUES
('产品A', 'PRODUCT_A', '正式品', 2.50, '套', '/uploads/product_a_drawing.jpg'),
('产品B', 'PRODUCT_B', '试制品', 1.80, '个', '/uploads/product_b_drawing.jpg'),
('产品C', 'PRODUCT_C', '样品', 3.20, '套', '/uploads/product_c_drawing.jpg');

INSERT INTO `rd_bom_sub_item` (`material_attribute`, `material_category`, `sub_item_number`, `product_name`, `specification`, `other_info`, `unit`, `supplier`, `standard_price`, `incremental_base`, `minimum_purchase`) VALUES
('原材料', '塑料类-单材料', '2401300010001004', '杯桶', '/', '/', '只', 'SUPPLIER_A', 15.50, 100.00, 50.00),
('原材料', '塑料类-混合料', '2401300040001004', 'T型三通', '3-Φ3', '/', '只', 'SUPPLIER_B', 8.20, 200.00, 100.00),
('自制零部件', '自制零部件', '3023001000800010', '阻水式空气过滤器膜', 'Φ6', '/', '只', 'SUPPLIER_C', 25.00, 50.00, 20.00),
('包装材料', '包装材料', '5200400900007000', '全塑白袋', '800*900', '/', '只', 'SUPPLIER_D', 2.50, 1000.00, 500.00); 