-- 为订单管理表添加客户代码和客户名称字段
ALTER TABLE `marketing_order` 
ADD COLUMN `customer_code` varchar(50) DEFAULT NULL COMMENT '客户代码' AFTER `order_no`,
ADD COLUMN `customer_name` varchar(200) DEFAULT NULL COMMENT '客户名称' AFTER `customer_code`;

-- 更新现有订单数据的客户信息
UPDATE `marketing_order` SET 
`customer_code` = 'KH001', 
`customer_name` = '北京科技有限公司' 
WHERE `order_no` = 'DD20240115001';

UPDATE `marketing_order` SET 
`customer_code` = 'KH002', 
`customer_name` = '上海贸易有限公司' 
WHERE `order_no` = 'DD20240120001'; 