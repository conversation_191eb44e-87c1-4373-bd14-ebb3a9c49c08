-- 技术研发部菜单配置

-- 添加技术资料子菜单
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `del_flag`) 
VALUES (2033, '技术资料', 2006, 1, 'technical-data', 'rd/technical-data/index', '', '1', '0', 'C', '0', '0', 'rd:technical-data:list', 'documentation', 'admin', NOW(), '', NULL, '技术资料管理菜单', '0');

-- 添加产品列表子菜单
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `del_flag`) 
VALUES (2034, '产品列表', 2006, 2, 'product-list', 'rd/product-list/index', '', '1', '0', 'C', '0', '0', 'rd:product-list:list', 'list', 'admin', NOW(), '', NULL, '产品列表管理菜单', '0');

-- 添加BOM子件子菜单
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `del_flag`) 
VALUES (2035, 'BOM子件', 2006, 3, 'bom-sub-item', 'rd/bom-sub-item/index', '', '1', '0', 'C', '0', '0', 'rd:bom-sub-item:list', 'tree', 'admin', NOW(), '', NULL, 'BOM子件管理菜单', '0');

-- 技术资料权限按钮
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `del_flag`) 
VALUES (2036, '技术资料查询', 2033, 1, '#', '', '', '1', '0', 'F', '0', '0', 'rd:technical-data:query', '#', 'admin', NOW(), '', NULL, '', '0');

INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `del_flag`) 
VALUES (2037, '技术资料新增', 2033, 2, '#', '', '', '1', '0', 'F', '0', '0', 'rd:technical-data:add', '#', 'admin', NOW(), '', NULL, '', '0');

INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `del_flag`) 
VALUES (2038, '技术资料修改', 2033, 3, '#', '', '', '1', '0', 'F', '0', '0', 'rd:technical-data:edit', '#', 'admin', NOW(), '', NULL, '', '0');

INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `del_flag`) 
VALUES (2039, '技术资料删除', 2033, 4, '#', '', '', '1', '0', 'F', '0', '0', 'rd:technical-data:remove', '#', 'admin', NOW(), '', NULL, '', '0');

INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `del_flag`) 
VALUES (2040, '技术资料导出', 2033, 5, '#', '', '', '1', '0', 'F', '0', '0', 'rd:technical-data:export', '#', 'admin', NOW(), '', NULL, '', '0');

-- 产品列表权限按钮
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `del_flag`) 
VALUES (2041, '产品列表查询', 2034, 1, '#', '', '', '1', '0', 'F', '0', '0', 'rd:product-list:query', '#', 'admin', NOW(), '', NULL, '', '0');

INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `del_flag`) 
VALUES (2042, '产品列表新增', 2034, 2, '#', '', '', '1', '0', 'F', '0', '0', 'rd:product-list:add', '#', 'admin', NOW(), '', NULL, '', '0');

INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `del_flag`) 
VALUES (2043, '产品列表修改', 2034, 3, '#', '', '', '1', '0', 'F', '0', '0', 'rd:product-list:edit', '#', 'admin', NOW(), '', NULL, '', '0');

INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `del_flag`) 
VALUES (2044, '产品列表删除', 2034, 4, '#', '', '', '1', '0', 'F', '0', '0', 'rd:product-list:remove', '#', 'admin', NOW(), '', NULL, '', '0');

INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `del_flag`) 
VALUES (2045, '产品列表导出', 2034, 5, '#', '', '', '1', '0', 'F', '0', '0', 'rd:product-list:export', '#', 'admin', NOW(), '', NULL, '', '0');

-- BOM子件权限按钮
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `del_flag`) 
VALUES (2046, 'BOM子件查询', 2035, 1, '#', '', '', '1', '0', 'F', '0', '0', 'rd:bom-sub-item:query', '#', 'admin', NOW(), '', NULL, '', '0');

INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `del_flag`) 
VALUES (2047, 'BOM子件新增', 2035, 2, '#', '', '', '1', '0', 'F', '0', '0', 'rd:bom-sub-item:add', '#', 'admin', NOW(), '', NULL, '', '0');

INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `del_flag`) 
VALUES (2048, 'BOM子件修改', 2035, 3, '#', '', '', '1', '0', 'F', '0', '0', 'rd:bom-sub-item:edit', '#', 'admin', NOW(), '', NULL, '', '0');

INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `del_flag`) 
VALUES (2049, 'BOM子件删除', 2035, 4, '#', '', '', '1', '0', 'F', '0', '0', 'rd:bom-sub-item:remove', '#', 'admin', NOW(), '', NULL, '', '0');

INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `del_flag`) 
VALUES (2050, 'BOM子件导出', 2035, 5, '#', '', '', '1', '0', 'F', '0', '0', 'rd:bom-sub-item:export', '#', 'admin', NOW(), '', NULL, '', '0'); 