import { Controller, Get, Post, Put, Delete, Body, Param, Query, UseInterceptors, UploadedFile, Res, StreamableFile, Header } from '@nestjs/common';
import { EmployeeService } from './employee.service';
import { CreateEmployeeDto } from './dto/create-employee.dto';
import { UpdateEmployeeDto } from './dto/update-employee.dto';
import { QueryEmployeeDto } from './dto/query-employee.dto';
import { FileInterceptor } from '@nestjs/platform-express';
import { Response } from 'express';
import { RequirePermission } from 'src/common/decorators/require-premission.decorator';

@Controller('employee')
export class EmployeeController {
  constructor(private readonly employeeService: EmployeeService) {}

  @Post()
  @RequirePermission('admin:employee:add')
  async create(@Body() createEmployeeDto: CreateEmployeeDto) {
    return await this.employeeService.create(createEmployeeDto);
  }

  @Get('list')
  @RequirePermission('admin:employee:list')
  async findAll(@Query() query: QueryEmployeeDto) {
    return await this.employeeService.findAll(query);
  }

  @Get(':id')
  @RequirePermission('admin:employee:query')
  async findOne(@Param('id') id: number) {
    return await this.employeeService.findOne(id);
  }

  @Put()
  @RequirePermission('admin:employee:edit')
  async update(@Body() updateEmployeeDto: UpdateEmployeeDto) {
    return await this.employeeService.update(updateEmployeeDto);
  }

  @Delete(':id')
  @RequirePermission('admin:employee:remove')
  async remove(@Param('id') id: number) {
    return await this.employeeService.remove(id);
  }

  @Post('importData')
  @RequirePermission('admin:employee:import')
  @UseInterceptors(FileInterceptor('file'))
  async importData(@UploadedFile() file: Express.Multer.File, @Query('updateSupport') updateSupport: string) {
    return await this.employeeService.importData(file, updateSupport === '1');
  }

  @Post('/importTemplate')
  @RequirePermission('admin:employee:import')
  async importTemplate(@Res() res: Response): Promise<void> {
    return this.employeeService.importTemplate(res);
  }

  @Put('leave/:id')
  @RequirePermission('admin:employee:edit')
  async leave(@Param('id') id: number, @Body('leave_date') leave_date: string) {
    return await this.employeeService.leave(id, leave_date);
  }
} 