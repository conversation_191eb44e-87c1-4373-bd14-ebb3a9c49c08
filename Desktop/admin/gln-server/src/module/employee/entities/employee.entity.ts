import { Entity, PrimaryGeneratedColumn, Column } from 'typeorm';

@Entity('employee', { comment: '员工信息表' })
export class EmployeeEntity {
  @PrimaryGeneratedColumn({ type: 'bigint', comment: '主键' })
  id: number;

  @Column({ type: 'varchar', length: 32, comment: '员工编号' })
  emp_no: string;

  @Column({ type: 'varchar', length: 32, comment: '姓名' })
  name: string;

  @Column({ type: 'date', nullable: true, comment: '入职日期' })
  entry_date: Date;

  @Column({ type: 'varchar', length: 64, nullable: true, comment: '部门' })
  dept: string;

  @Column({ type: 'varchar', length: 64, nullable: true, comment: '职位' })
  position: string;

  @Column({ type: 'char', length: 1, nullable: true, comment: '性别' })
  gender: string;

  @Column({ type: 'varchar', length: 32, nullable: true, comment: '政治面貌' })
  political: string;

  @Column({ type: 'int', nullable: true, comment: '年龄' })
  age: number;

  @Column({ type: 'varchar', length: 32, nullable: true, comment: '民族' })
  nation: string;

  @Column({ type: 'varchar', length: 32, nullable: true, comment: '学历' })
  education: string;

  @Column({ type: 'varchar', length: 64, nullable: true, comment: '毕业院校' })
  graduate_school: string;

  @Column({ type: 'varchar', length: 128, nullable: true, comment: '户籍地址' })
  address: string;

  @Column({ type: 'varchar', length: 32, nullable: true, comment: '身份证号' })
  id_card: string;

  @Column({ type: 'varchar', length: 20, nullable: true, comment: '手机号' })
  phone: string;

  @Column({ type: 'varchar', length: 64, nullable: true, comment: '银行信息' })
  bank_info: string;

  @Column({ type: 'varchar', length: 32, nullable: true, comment: '银行卡号' })
  bank_card: string;

  @Column({ type: 'varchar', length: 20, nullable: true, comment: '紧急联系人电话' })
  emergency_phone: string;

  @Column({ type: 'varchar', length: 128, nullable: true, comment: '现居住地址' })
  current_addr: string;

  @Column({ type: 'varchar', length: 32, nullable: true, comment: '用工性质' })
  emp_type: string;

  @Column({ type: 'tinyint', width: 1, default: 0, comment: '是否缴纳社保' })
  social_security: number;

  @Column({ type: 'tinyint', width: 1, default: 0, comment: '是否签劳动合同' })
  labor_contract: number;

  @Column({ type: 'varchar', length: 16, nullable: true, comment: '员工状态' })
  emp_status: string;

  @Column({ type: 'varchar', length: 64, nullable: true, comment: '招聘信息渠道' })
  recruit_source: string;

  @Column({ type: 'date', nullable: true, comment: '离职日期' })
  leave_date: Date;

  @Column({ type: 'varchar', length: 255, nullable: true, comment: '备注' })
  remark: string;

  @Column({ type: 'datetime', default: () => 'CURRENT_TIMESTAMP', comment: '创建时间' })
  create_time: Date;

  @Column({ type: 'datetime', default: () => 'CURRENT_TIMESTAMP', onUpdate: 'CURRENT_TIMESTAMP', comment: '更新时间' })
  update_time: Date;

  @Column({ type: 'tinyint', width: 1, default: 0, comment: '是否办理健康证' })
  health_cert: number;

  @Column({ type: 'varchar', length: 255, nullable: true, comment: '身份证图片' })
  id_card_img: string;

  @Column({ type: 'varchar', length: 255, nullable: true, comment: '健康证图片' })
  health_cert_img: string;
} 