import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, Like, Between } from 'typeorm';
import { EmployeeEntity } from './entities/employee.entity';
import { CreateEmployeeDto } from './dto/create-employee.dto';
import { UpdateEmployeeDto } from './dto/update-employee.dto';
import { QueryEmployeeDto } from './dto/query-employee.dto';
import * as xlsx from 'xlsx';
import { Response } from 'express';
import { validate } from 'class-validator';

@Injectable()
export class EmployeeService {
  constructor(
    @InjectRepository(EmployeeEntity)
    private readonly employeeRepo: Repository<EmployeeEntity>,
  ) {}

  async create(dto: CreateEmployeeDto) {
    const entity = this.employeeRepo.create(dto);
    return await this.employeeRepo.save(entity);
  }

  async update(dto: UpdateEmployeeDto) {
    await this.employeeRepo.update(dto.id, dto);
    return this.employeeRepo.findOne({ where: { id: dto.id } });
  }

  async remove(id: number) {
    return await this.employeeRepo.delete(id);
  }

  async leave(id: number, leave_date: string) {
    return await this.employeeRepo.update(id, { emp_status: '离职', leave_date });
  }

  async findOne(id: number) {
    return await this.employeeRepo.findOne({ where: { id } });
  }

  async findAll(query: QueryEmployeeDto) {
    const qb = this.employeeRepo.createQueryBuilder('emp');
    if (query.name) {
      qb.andWhere('emp.name LIKE :name', { name: `%${query.name}%` });
    }
    if (query.dept) {
      qb.andWhere('emp.dept = :dept', { dept: query.dept });
    }
    if (query.entryDateStart && query.entryDateEnd) {
      qb.andWhere('emp.entry_date BETWEEN :start AND :end', { start: query.entryDateStart, end: query.entryDateEnd });
    }
    qb.orderBy('emp.create_time', 'DESC');
    const pageNum = query.pageNum || 1;
    const pageSize = query.pageSize || 10;
    qb.skip((pageNum - 1) * pageSize).take(pageSize);
    const [list, total] = await qb.getManyAndCount();
    return { list, total };
  }

  async importTemplate(res: Response): Promise<void> {
    const data = [
      [
        '员工编号*', '姓名*', '入职日期*', '部门*', '职位*', '性别*', '政治面貌*', '年龄*', '民族*', '学历*', '毕业院校*',
        '户籍地址*', '身份证号*', '手机号*', '银行信息*', '银行卡号*', '紧急联系人电话*', '现居住地址*', '用工性质*',
        '是否缴纳社保(是/否)*', '是否签劳动合同(是/否)*', '员工状态*', '招聘信息渠道*', '离职日期*',
        '是否办理健康证(是/否)*', '备注*'
      ],
    ];
    const sheet = xlsx.utils.aoa_to_sheet(data);
    const wb = xlsx.utils.book_new();
    xlsx.utils.book_append_sheet(wb, sheet, '员工数据');
    const buffer = xlsx.write(wb, { bookType: 'xlsx', type: 'buffer' });
    res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    res.setHeader('Content-Disposition', 'attachment; filename=EmployeeImportTemplate.xlsx');
    res.end(buffer);
  }

  async importData(file: Express.Multer.File, updateSupport: boolean) {
    const workbook = xlsx.read(file.buffer);
    const sheet = workbook.Sheets[workbook.SheetNames[0]];
    const rows = xlsx.utils.sheet_to_json(sheet, { header: 1 });

    if (rows.length < 2) {
      return { msg: '导入文件不能为空！', failures: [] };
    }

    const jsonData = xlsx.utils.sheet_to_json(sheet);

    let successCount = 0;
    let failureCount = 0;
    const failureDetails = [];

    for (const [index, row] of jsonData.entries()) {
      const employeeDto = new CreateEmployeeDto();

      employeeDto.emp_no = row['员工编号*'];
      employeeDto.name = row['姓名*'];
      if (typeof row['入职日期*'] === 'number') {
        const jsDate = new Date(Date.UTC(1900, 0, row['入职日期*'] - 1));
        employeeDto.entry_date = jsDate.toISOString().split('T')[0];
      } else {
        employeeDto.entry_date = row['入职日期*'];
      }
      employeeDto.dept = row['部门*'];
      employeeDto.position = row['职位*'];
      employeeDto.gender = row['性别*'];
      employeeDto.political = row['政治面貌*'];
      employeeDto.age = Number(row['年龄*']);
      employeeDto.nation = row['民族*'];
      employeeDto.education = row['学历*'];
      employeeDto.graduate_school = row['毕业院校*'];
      employeeDto.address = row['户籍地址*'];
      employeeDto.id_card = row['身份证号*'];
      employeeDto.phone = String(row['手机号*']);
      employeeDto.bank_info = row['银行信息*'];
      employeeDto.bank_card = String(row['银行卡号*']);
      employeeDto.emergency_phone = String(row['紧急联系人电话*']);
      employeeDto.current_addr = row['现居住地址*'];
      employeeDto.emp_type = row['用工性质*'];
      employeeDto.social_security = row['是否缴纳社保(是/否)*'] === '是' ? 1 : 0;
      employeeDto.labor_contract = row['是否签劳动合同(是/否)*'] === '是' ? 1 : 0;
      employeeDto.emp_status = row['员工状态*'];
      employeeDto.recruit_source = row['招聘信息渠道*'];
      if (typeof row['离职日期*'] === 'number') {
        const jsDate = new Date(Date.UTC(1900, 0, row['离职日期*'] - 1));
        employeeDto.leave_date = jsDate.toISOString().split('T')[0];
      } else {
        employeeDto.leave_date = row['离职日期*'];
      }
      employeeDto.health_cert = row['是否办理健康证(是/否)*'] === '是' ? 1 : 0;
      employeeDto.remark = row['备注*'];
      employeeDto.id_card_img = '';
      employeeDto.health_cert_img = '';

      const errors = await validate(employeeDto);
      if (errors.length > 0) {
          failureCount++;
          const errorMsg = errors.map(e => Object.values(e.constraints)).join('; ');
          failureDetails.push(`第 ${index + 2} 行 ${employeeDto.name || ''}: ${errorMsg}`);
      } else {
          try {
              const existingEmployee = await this.employeeRepo.findOne({ where: { emp_no: employeeDto.emp_no } });
              if (existingEmployee) {
                  if (updateSupport) {
                      await this.employeeRepo.update(existingEmployee.id, employeeDto);
                      successCount++;
                  } else {
                      failureCount++;
                      failureDetails.push(`第 ${index + 2} 行: 员工编号 ${employeeDto.emp_no} 已存在`);
                  }
              } else {
                  await this.employeeRepo.save(this.employeeRepo.create(employeeDto as any));
                  successCount++;
              }
          } catch (error) {
              failureCount++;
              failureDetails.push(`第 ${index + 2} 行 ${employeeDto.name || ''}: 数据库保存失败 - ${error.message}`);
          }
      }
    }

    let msg = `成功导入 ${successCount} 条数据。`;
    if (failureCount > 0) {
      msg += `失败 ${failureCount} 条。<br/>失败详情：<br/>${failureDetails.join('<br/>')}`;
    }
    return { msg };
  }
} 