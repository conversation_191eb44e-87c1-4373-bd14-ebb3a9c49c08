import { ApiProperty, PartialType } from '@nestjs/swagger';
import { CreateEmployeeDto } from './create-employee.dto';
import { IsNumber, IsOptional, IsString, IsNotEmpty } from 'class-validator';

export class UpdateEmployeeDto extends PartialType(CreateEmployeeDto) {
  @ApiProperty({ description: '主键id' })
  @IsNumber()
  id: number;

  @ApiProperty({ description: '银行卡号', required: false })
  @IsOptional()
  @IsString()
  bank_card?: string;

  @ApiProperty({ description: '是否办理健康证', required: false })
  @IsOptional()
  @IsNumber()
  health_cert?: number;

  @ApiProperty({ description: '身份证图片', required: false })
  @IsOptional()
  @IsString()
  id_card_img?: string;

  @ApiProperty({ description: '健康证图片', required: false })
  @IsOptional()
  @IsString()
  health_cert_img?: string;
} 