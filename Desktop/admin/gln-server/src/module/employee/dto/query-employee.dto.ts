import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsString, IsInt, IsDateString } from 'class-validator';
import { Type } from 'class-transformer';

export class QueryEmployeeDto {
  @ApiProperty({ description: '页码', required: false })
  @IsOptional()
  @IsInt()
  @Type(() => Number)
  pageNum?: number;

  @ApiProperty({ description: '每页数量', required: false })
  @IsOptional()
  @IsInt()
  @Type(() => Number)
  pageSize?: number;

  @ApiProperty({ description: '姓名', required: false })
  @IsOptional()
  @IsString()
  name?: string;

  @ApiProperty({ description: '部门', required: false })
  @IsOptional()
  @IsString()
  dept?: string;

  @ApiProperty({ description: '入职日期-开始', required: false })
  @IsOptional()
  @IsDateString()
  entryDateStart?: string;

  @ApiProperty({ description: '入职日期-结束', required: false })
  @IsOptional()
  @IsDateString()
  entryDateEnd?: string;
} 