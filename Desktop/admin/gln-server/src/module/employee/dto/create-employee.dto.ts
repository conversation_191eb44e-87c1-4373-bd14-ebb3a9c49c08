import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsOptional, IsDateString, IsInt, IsNumber, IsBoolean, IsNotEmpty } from 'class-validator';

export class CreateEmployeeDto {
  @ApiProperty({ description: '员工编号' })
  @IsString()
  @IsNotEmpty({ message: '员工编号不能为空' })
  emp_no: string;

  @ApiProperty({ description: '姓名' })
  @IsString()
  @IsNotEmpty({ message: '姓名不能为空' })
  name: string;

  @ApiProperty({ description: '入职日期' })
  @IsDateString()
  @IsNotEmpty({ message: '入职日期不能为空' })
  entry_date: string;

  @ApiProperty({ description: '部门' })
  @IsString()
  @IsNotEmpty({ message: '部门不能为空' })
  dept: string;

  @ApiProperty({ description: '职位' })
  @IsString()
  @IsNotEmpty({ message: '职位不能为空' })
  position: string;

  @ApiProperty({ description: '性别' })
  @IsString()
  @IsNotEmpty({ message: '性别不能为空' })
  gender: string;

  @ApiProperty({ description: '政治面貌' })
  @IsString()
  @IsNotEmpty({ message: '政治面貌不能为空' })
  political: string;

  @ApiProperty({ description: '年龄' })
  @IsInt()
  @IsNotEmpty({ message: '年龄不能为空' })
  age: number;

  @ApiProperty({ description: '民族' })
  @IsString()
  @IsNotEmpty({ message: '民族不能为空' })
  nation: string;

  @ApiProperty({ description: '学历' })
  @IsString()
  @IsNotEmpty({ message: '学历不能为空' })
  education: string;

  @ApiProperty({ description: '毕业院校' })
  @IsString()
  @IsNotEmpty({ message: '毕业院校不能为空' })
  graduate_school: string;

  @ApiProperty({ description: '户籍地址' })
  @IsString()
  @IsNotEmpty({ message: '户籍地址不能为空' })
  address: string;

  @ApiProperty({ description: '身份证号' })
  @IsString()
  @IsNotEmpty({ message: '身份证号不能为空' })
  id_card: string;

  @ApiProperty({ description: '手机号' })
  @IsString()
  @IsNotEmpty({ message: '手机号不能为空' })
  phone: string;

  @ApiProperty({ description: '银行信息' })
  @IsString()
  @IsNotEmpty({ message: '银行信息不能为空' })
  bank_info: string;

  @ApiProperty({ description: '银行卡号' })
  @IsString()
  @IsNotEmpty({ message: '银行卡号不能为空' })
  bank_card: string;

  @ApiProperty({ description: '紧急联系人电话' })
  @IsString()
  @IsNotEmpty({ message: '紧急联系人电话不能为空' })
  emergency_phone: string;

  @ApiProperty({ description: '现居住地址' })
  @IsString()
  @IsNotEmpty({ message: '现居住地址不能为空' })
  current_addr: string;

  @ApiProperty({ description: '用工性质' })
  @IsString()
  @IsNotEmpty({ message: '用工性质不能为空' })
  emp_type: string;

  @ApiProperty({ description: '是否缴纳社保' })
  @IsInt()
  @IsNotEmpty({ message: '是否缴纳社保不能为空' })
  social_security: number;

  @ApiProperty({ description: '是否签劳动合同' })
  @IsInt()
  @IsNotEmpty({ message: '是否签劳动合同不能为空' })
  labor_contract: number;

  @ApiProperty({ description: '员工状态' })
  @IsString()
  @IsNotEmpty({ message: '员工状态不能为空' })
  emp_status: string;

  @ApiProperty({ description: '招聘信息渠道' })
  @IsString()
  @IsNotEmpty({ message: '招聘信息渠道不能为空' })
  recruit_source: string;

  @ApiProperty({ description: '离职日期', required: false })
  @IsDateString()
  @IsOptional()
  leave_date?: string;

  @ApiProperty({ description: '备注' })
  @IsString()
  @IsNotEmpty({ message: '备注不能为空' })
  remark: string;

  @ApiProperty({ description: '是否办理健康证' })
  @IsInt()
  @IsNotEmpty({ message: '是否办理健康证不能为空' })
  health_cert: number;

  @ApiProperty({ description: '身份证图片' })
  @IsString()
  @IsNotEmpty({ message: '身份证图片不能为空' })
  id_card_img: string;

  @ApiProperty({ description: '健康证图片' })
  @IsString()
  @IsNotEmpty({ message: '健康证图片不能为空' })
  health_cert_img: string;
} 