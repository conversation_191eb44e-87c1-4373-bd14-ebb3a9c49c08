import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { FinanceEntity } from './finance.entity';
import { QueryFinanceDto } from './dto/query-finance.dto';

@Injectable()
export class FinanceService {
  constructor(
    @InjectRepository(FinanceEntity)
    private readonly financeRepo: Repository<FinanceEntity>,
  ) {}

  async findOne(id: number) {
    return await this.financeRepo.findOne({ where: { id } });
  }

  async findAll(query: QueryFinanceDto) {
    const qb = this.financeRepo.createQueryBuilder('finance');
    
    if (query.customerName) {
      qb.andWhere('finance.customerName LIKE :customerName', { customerName: `%${query.customerName}%` });
    }
    if (query.customerCode) {
      qb.andWhere('finance.customerCode LIKE :customerCode', { customerCode: `%${query.customerCode}%` });
    }
    if (query.timeRange) {
      const [start, end] = query.timeRange.split(',');
      if (start && end) {
        qb.andWhere('finance.createTime BETWEEN :start AND :end', { start, end });
      }
    }
    qb.orderBy('finance.createTime', 'DESC');
    const pageNum = query.pageNum || 1;
    const pageSize = query.pageSize || 10;
    qb.skip((pageNum - 1) * pageSize).take(pageSize);
    const [rows, total] = await qb.getManyAndCount();
    return { rows, total };
  }

  async getPaymentRecords(customerId: number, query: any) {
    // 这里应该查询付款记录表，暂时返回模拟数据
    return {
      rows: [
        {
          paymentDate: '2024-01-15',
          paymentAmount: 50000.00,
          paymentMethod: '银行转账',
          paymentRemark: '首期付款',
          operator: '张三'
        },
        {
          paymentDate: '2024-02-15',
          paymentAmount: 40000.00,
          paymentMethod: '银行转账',
          paymentRemark: '二期付款',
          operator: '李四'
        }
      ]
    };
  }

  async getInvoiceRecords(customerId: number, query: any) {
    // 这里应该查询发票记录表，暂时返回模拟数据
    return {
      rows: [
        {
          invoiceDate: '2024-01-15',
          invoiceNumber: 'FP20240115001',
          invoiceAmount: 50000.00,
          invoiceType: '增值税专用发票',
          invoiceRemark: '首期开票'
        },
        {
          invoiceDate: '2024-02-15',
          invoiceNumber: 'FP20240215001',
          invoiceAmount: 40000.00,
          invoiceType: '增值税专用发票',
          invoiceRemark: '二期开票'
        }
      ]
    };
  }

  async getOrderRecords(customerId: number, query: any) {
    // 这里应该查询订单记录表，暂时返回模拟数据
    return {
      rows: [
        {
          orderDate: '2024-01-10',
          orderNo: 'DD20240110001',
          orderAmount: 30000.00,
          orderStatus: '已签收',
          productName: '工业传感器',
          quantity: 200,
          deliveryDate: '2024-02-10'
        },
        {
          orderDate: '2024-01-15',
          orderNo: 'DD20240115001',
          orderAmount: 40000.00,
          orderStatus: '已发货',
          productName: '控制面板',
          quantity: 100,
          deliveryDate: '2024-02-15'
        }
      ]
    };
  }
} 