import { Controller, Get, Param, Query } from '@nestjs/common';
import { FinanceService } from './finance.service';
import { QueryFinanceDto } from './dto/query-finance.dto';
import { RequirePermission } from 'src/common/decorators/require-premission.decorator';

@Controller('marketing/finance')
export class FinanceController {
  constructor(private readonly financeService: FinanceService) {}

  @Get('list')
  @RequirePermission('marketing:finance:list')
  async findAll(@Query() query: QueryFinanceDto) {
    return await this.financeService.findAll(query);
  }

  @Get(':id')
  @RequirePermission('marketing:finance:query')
  async findOne(@Param('id') id: number) {
    return await this.financeService.findOne(id);
  }

  @Get('payment/:customerId')
  @RequirePermission('marketing:finance:query')
  async getPaymentRecords(@Param('customerId') customerId: number, @Query() query: any) {
    return await this.financeService.getPaymentRecords(customerId, query);
  }

  @Get('invoice/:customerId')
  @RequirePermission('marketing:finance:query')
  async getInvoiceRecords(@Param('customerId') customerId: number, @Query() query: any) {
    return await this.financeService.getInvoiceRecords(customerId, query);
  }

  @Get('order/:customerId')
  @RequirePermission('marketing:finance:query')
  async getOrderRecords(@Param('customerId') customerId: number, @Query() query: any) {
    return await this.financeService.getOrderRecords(customerId, query);
  }
} 