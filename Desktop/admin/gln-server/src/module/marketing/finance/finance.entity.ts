import { Entity, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn } from 'typeorm';

@Entity('marketing_finance')
export class FinanceEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ name: 'customer_id', type: 'int', comment: '客户ID' })
  customerId: number;

  @Column({ name: 'customer_name', length: 200, comment: '客户名称' })
  customerName: string;

  @Column({ name: 'customer_code', length: 50, comment: '客户代码' })
  customerCode: string;

  @Column({ name: 'total_order_count', type: 'int', default: 0, comment: '订单总数量' })
  totalOrderCount: number;

  @Column({ name: 'total_order_amount', type: 'decimal', precision: 10, scale: 2, default: 0, comment: '客户总订单金额' })
  totalOrderAmount: number;

  @Column({ name: 'payment_info', type: 'text', comment: '客户付款信息' })
  paymentInfo: string;

  @Column({ name: 'debt_info', type: 'text', comment: '客户欠款信息' })
  debtInfo: string;

  @Column({ name: 'invoice_time_info', type: 'text', comment: '客户开票时间信息' })
  invoiceTimeInfo: string;

  @Column({ name: 'invoiced_amount', type: 'decimal', precision: 10, scale: 2, default: 0, comment: '已开发票金额' })
  invoicedAmount: number;

  @Column({ name: 'invoice_numbers', type: 'text', comment: '发票号' })
  invoiceNumbers: string;

  @CreateDateColumn({ name: 'create_time', comment: '创建时间' })
  createTime: Date;

  @UpdateDateColumn({ name: 'update_time', comment: '更新时间' })
  updateTime: Date;
} 