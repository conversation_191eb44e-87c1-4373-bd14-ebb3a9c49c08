import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, Between } from 'typeorm';
import { OrderEntity } from './order.entity';
import { CreateOrderDto } from './dto/create-order.dto';
import { UpdateOrderDto } from './dto/update-order.dto';
import { QueryOrderDto } from './dto/query-order.dto';

@Injectable()
export class OrderService {
  constructor(
    @InjectRepository(OrderEntity)
    private readonly orderRepo: Repository<OrderEntity>,
  ) {}

  async create(dto: CreateOrderDto) {
    // 计算总金额
    if (!dto.totalAmount) {
      dto.totalAmount = dto.quantity * dto.unitPrice;
    }
    const entity = this.orderRepo.create(dto);
    return await this.orderRepo.save(entity);
  }

  async update(dto: UpdateOrderDto) {
    // 计算总金额
    if (dto.quantity && dto.unitPrice && !dto.totalAmount) {
      dto.totalAmount = dto.quantity * dto.unitPrice;
    }
    await this.orderRepo.update(dto.id, dto);
    return this.orderRepo.findOne({ where: { id: dto.id } });
  }

  async remove(id: number) {
    return await this.orderRepo.delete(id);
  }

  async findOne(id: number) {
    return await this.orderRepo.findOne({ where: { id } });
  }

  async findAll(query: QueryOrderDto) {
    const qb = this.orderRepo.createQueryBuilder('order');
    
    if (query.orderNo) {
      qb.andWhere('order.orderNo LIKE :orderNo', { orderNo: `%${query.orderNo}%` });
    }
    if (query.customerCode) {
      qb.andWhere('order.customerCode LIKE :customerCode', { customerCode: `%${query.customerCode}%` });
    }
    if (query.customerName) {
      qb.andWhere('order.customerName LIKE :customerName', { customerName: `%${query.customerName}%` });
    }
    if (query.productName) {
      qb.andWhere('order.productName LIKE :productName', { productName: `%${query.productName}%` });
    }
    if (query.productModel) {
      qb.andWhere('order.productModel LIKE :productModel', { productModel: `%${query.productModel}%` });
    }
    if (query.orderDateRange) {
      const [start, end] = query.orderDateRange.split(',');
      if (start && end) {
        qb.andWhere('order.orderDate BETWEEN :start AND :end', { start, end });
      }
    }
    if (query.deliveryDateRange) {
      const [start, end] = query.deliveryDateRange.split(',');
      if (start && end) {
        qb.andWhere('order.deliveryDate BETWEEN :start AND :end', { start, end });
      }
    }
    if (query.shipDateRange) {
      const [start, end] = query.shipDateRange.split(',');
      if (start && end) {
        qb.andWhere('order.shipDate BETWEEN :start AND :end', { start, end });
      }
    }
    
    qb.orderBy('order.createTime', 'DESC');
    
    const pageNum = query.pageNum || 1;
    const pageSize = query.pageSize || 10;
    qb.skip((pageNum - 1) * pageSize).take(pageSize);
    
    const [rows, total] = await qb.getManyAndCount();
    return { rows, total };
  }

  async updateStatus(id: number, status: string) {
    await this.orderRepo.update(id, { orderStatus: status });
    return this.orderRepo.findOne({ where: { id } });
  }
} 