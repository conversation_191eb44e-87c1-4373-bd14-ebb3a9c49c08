import { Controller, Get, Post, Put, Delete, Body, Param, Query } from '@nestjs/common';
import { OrderService } from './order.service';
import { CreateOrderDto } from './dto/create-order.dto';
import { UpdateOrderDto } from './dto/update-order.dto';
import { QueryOrderDto } from './dto/query-order.dto';
import { RequirePermission } from 'src/common/decorators/require-premission.decorator';

@Controller('marketing/order')
export class OrderController {
  constructor(private readonly orderService: OrderService) {}

  @Post()
  @RequirePermission('marketing:order:add')
  async create(@Body() createOrderDto: CreateOrderDto) {
    return await this.orderService.create(createOrderDto);
  }

  @Get('list')
  @RequirePermission('marketing:order:list')
  async findAll(@Query() query: QueryOrderDto) {
    return await this.orderService.findAll(query);
  }

  @Get(':id')
  @RequirePermission('marketing:order:query')
  async findOne(@Param('id') id: number) {
    return await this.orderService.findOne(id);
  }

  @Put()
  @RequirePermission('marketing:order:edit')
  async update(@Body() updateOrderDto: UpdateOrderDto) {
    return await this.orderService.update(updateOrderDto);
  }

  @Delete(':id')
  @RequirePermission('marketing:order:remove')
  async remove(@Param('id') id: number) {
    return await this.orderService.remove(id);
  }

  @Put('status/:id')
  @RequirePermission('marketing:order:edit')
  async updateStatus(@Param('id') id: number, @Body('status') status: string) {
    return await this.orderService.updateStatus(id, status);
  }
} 