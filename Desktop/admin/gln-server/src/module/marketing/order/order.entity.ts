import { Entity, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn } from 'typeorm';

@Entity('marketing_order')
export class OrderEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ name: 'order_date', type: 'date', comment: '订单日期' })
  orderDate: Date;

  @Column({ name: 'order_contract', length: 100, comment: '订单合同' })
  orderContract: string;

  @Column({ name: 'order_no', length: 100, comment: '订单号' })
  orderNo: string;

  @Column({ name: 'customer_code', length: 50, comment: '客户代码' })
  customerCode: string;

  @Column({ name: 'customer_name', length: 200, comment: '客户名称' })
  customerName: string;

  @Column({ name: 'order_status', length: 20, default: '待生产', comment: '订单状态' })
  orderStatus: string;

  @Column({ name: 'product_name', length: 200, comment: '产品名称' })
  productName: string;

  @Column({ name: 'product_model', length: 100, comment: '产品型号' })
  productModel: string;

  @Column({ name: 'quantity', type: 'int', comment: '数量' })
  quantity: number;

  @Column({ name: 'unit_price', type: 'decimal', precision: 10, scale: 2, comment: '单价' })
  unitPrice: number;

  @Column({ name: 'total_amount', type: 'decimal', precision: 10, scale: 2, comment: '总金额' })
  totalAmount: number;

  @Column({ name: 'delivery_date', type: 'date', comment: '交货日期' })
  deliveryDate: Date;

  @Column({ name: 'ship_date', type: 'date', nullable: true, comment: '发货日期' })
  shipDate: Date;

  @Column({ name: 'ship_address', type: 'text', comment: '发货地址' })
  shipAddress: string;

  @Column({ name: 'ship_quantity', type: 'int', default: 0, nullable: true, comment: '发货数量' })
  shipQuantity?: number;

  @Column({ name: 'stock_quantity', type: 'int', default: 0, nullable: true, comment: '库存数量' })
  stockQuantity?: number;

  @Column({ name: 'logistics_info', type: 'text', nullable: true, comment: '物流信息' })
  logisticsInfo?: string;

  @CreateDateColumn({ name: 'create_time', comment: '创建时间' })
  createTime: Date;

  @UpdateDateColumn({ name: 'update_time', comment: '更新时间' })
  updateTime: Date;
} 