import { IsNotEmpty, IsString, <PERSON>N<PERSON>ber, IsOptional, IsDateString } from 'class-validator';
import { Type } from 'class-transformer';

export class CreateOrderDto {
  @IsNotEmpty({ message: '订单日期不能为空' })
  @IsDateString()
  orderDate: string;

  @IsOptional()
  @IsString()
  orderContract?: string;

  @IsNotEmpty({ message: '订单号不能为空' })
  @IsString()
  orderNo: string;

  @IsOptional()
  @IsString()
  customerCode?: string;

  @IsOptional()
  @IsString()
  customerName?: string;

  @IsOptional()
  @IsString()
  orderStatus?: string;

  @IsNotEmpty({ message: '产品名称不能为空' })
  @IsString()
  productName: string;

  @IsNotEmpty({ message: '产品型号不能为空' })
  @IsString()
  productModel: string;

  @IsNotEmpty({ message: '数量不能为空' })
  @Type(() => Number)
  @IsNumber()
  quantity: number;

  @IsNotEmpty({ message: '单价不能为空' })
  @Type(() => Number)
  @IsNumber()
  unitPrice: number;

  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  totalAmount?: number;

  @IsOptional()
  @IsDateString()
  deliveryDate?: string;

  @IsOptional()
  @IsDateString()
  shipDate?: string;

  @IsOptional()
  @IsString()
  shipAddress?: string;

  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  shipQuantity?: number;

  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  stockQuantity?: number;

  @IsOptional()
  @IsString()
  logisticsInfo?: string;
} 