import { IsNotEmpty, IsString, IsOptional, IsDateString } from 'class-validator';

export class CreateComplaintDto {
  @IsNotEmpty({ message: '客户代码不能为空' })
  @IsString()
  customerCode: string;

  @IsNotEmpty({ message: '客户名称不能为空' })
  @IsString()
  customerName: string;

  @IsNotEmpty({ message: '投诉日期不能为空' })
  @IsDateString()
  complaintDate: string;

  @IsOptional()
  @IsDateString()
  orderDate?: string;

  @IsNotEmpty({ message: '订单号不能为空' })
  @IsString()
  orderNo: string;

  @IsNotEmpty({ message: '投诉批号不能为空' })
  @IsString()
  complaintBatchNo: string;

  @IsNotEmpty({ message: '投诉描述不能为空' })
  @IsString()
  complaintDescription: string;

  @IsOptional()
  @IsString()
  complaintFeedback?: string;
} 