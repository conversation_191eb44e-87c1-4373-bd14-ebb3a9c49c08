import { Entity, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn } from 'typeorm';

@Entity('marketing_complaint')
export class ComplaintEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ name: 'customer_code', length: 50, comment: '客户代码' })
  customerCode: string;

  @Column({ name: 'customer_name', length: 200, comment: '客户名称' })
  customerName: string;

  @Column({ name: 'complaint_date', type: 'date', comment: '投诉日期' })
  complaintDate: Date;

  @Column({ name: 'order_date', type: 'date', comment: '订单日期' })
  orderDate: Date;

  @Column({ name: 'order_no', length: 100, comment: '订单号' })
  orderNo: string;

  @Column({ name: 'complaint_batch_no', length: 100, comment: '投诉批号' })
  complaintBatchNo: string;

  @Column({ name: 'complaint_description', type: 'text', comment: '投诉描述' })
  complaintDescription: string;

  @Column({ name: 'complaint_feedback', type: 'text', nullable: true, comment: '投诉反馈' })
  complaintFeedback: string;

  @CreateDateColumn({ name: 'create_time', comment: '创建时间' })
  createTime: Date;

  @UpdateDateColumn({ name: 'update_time', comment: '更新时间' })
  updateTime: Date;
} 