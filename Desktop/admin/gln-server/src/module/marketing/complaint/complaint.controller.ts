import { Controller, Get, Post, Put, Delete, Body, Param, Query } from '@nestjs/common';
import { ComplaintService } from './complaint.service';
import { CreateComplaintDto } from './dto/create-complaint.dto';
import { UpdateComplaintDto } from './dto/update-complaint.dto';
import { QueryComplaintDto } from './dto/query-complaint.dto';
import { RequirePermission } from 'src/common/decorators/require-premission.decorator';

@Controller('marketing/complaint')
export class ComplaintController {
  constructor(private readonly complaintService: ComplaintService) {}

  @Post()
  @RequirePermission('marketing:complaint:add')
  async create(@Body() createComplaintDto: CreateComplaintDto) {
    return await this.complaintService.create(createComplaintDto);
  }

  @Get('list')
  @RequirePermission('marketing:complaint:list')
  async findAll(@Query() query: QueryComplaintDto) {
    return await this.complaintService.findAll(query);
  }

  @Get(':id')
  @RequirePermission('marketing:complaint:query')
  async findOne(@Param('id') id: number) {
    return await this.complaintService.findOne(id);
  }

  @Put()
  @RequirePermission('marketing:complaint:edit')
  async update(@Body() updateComplaintDto: UpdateComplaintDto) {
    return await this.complaintService.update(updateComplaintDto);
  }

  @Delete(':id')
  @RequirePermission('marketing:complaint:remove')
  async remove(@Param('id') id: number) {
    return await this.complaintService.remove(id);
  }

  @Put('feedback/:id')
  @RequirePermission('marketing:complaint:feedback')
  async submitFeedback(@Param('id') id: number, @Body('complaintFeedback') feedback: string) {
    return await this.complaintService.submitFeedback(id, feedback);
  }
} 