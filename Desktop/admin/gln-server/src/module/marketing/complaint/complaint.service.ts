import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ComplaintEntity } from './complaint.entity';
import { CreateComplaintDto } from './dto/create-complaint.dto';
import { UpdateComplaintDto } from './dto/update-complaint.dto';
import { QueryComplaintDto } from './dto/query-complaint.dto';

@Injectable()
export class ComplaintService {
  constructor(
    @InjectRepository(ComplaintEntity)
    private readonly complaintRepo: Repository<ComplaintEntity>,
  ) {}

  async create(dto: CreateComplaintDto) {
    const entity = this.complaintRepo.create(dto);
    return await this.complaintRepo.save(entity);
  }

  async update(dto: UpdateComplaintDto) {
    await this.complaintRepo.update(dto.id, dto);
    return this.complaintRepo.findOne({ where: { id: dto.id } });
  }

  async remove(id: number) {
    return await this.complaintRepo.delete(id);
  }

  async findOne(id: number) {
    return await this.complaintRepo.findOne({ where: { id } });
  }

  async findAll(query: QueryComplaintDto) {
    const qb = this.complaintRepo.createQueryBuilder('complaint');
    
    if (query.customerName) {
      qb.andWhere('complaint.customerName LIKE :customerName', { customerName: `%${query.customerName}%` });
    }
    if (query.customerCode) {
      qb.andWhere('complaint.customerCode LIKE :customerCode', { customerCode: `%${query.customerCode}%` });
    }
    if (query.orderNo) {
      qb.andWhere('complaint.orderNo LIKE :orderNo', { orderNo: `%${query.orderNo}%` });
    }
    if (query.complaintBatchNo) {
      qb.andWhere('complaint.complaintBatchNo LIKE :complaintBatchNo', { complaintBatchNo: `%${query.complaintBatchNo}%` });
    }
    
    qb.orderBy('complaint.createTime', 'DESC');
    
    const pageNum = query.pageNum || 1;
    const pageSize = query.pageSize || 10;
    qb.skip((pageNum - 1) * pageSize).take(pageSize);
    
    const [rows, total] = await qb.getManyAndCount();
    return { rows, total };
  }

  async submitFeedback(id: number, feedback: string) {
    await this.complaintRepo.update(id, { complaintFeedback: feedback });
    return this.complaintRepo.findOne({ where: { id } });
  }
} 