import { IsNotEmpty, IsString, IsOptional } from 'class-validator';

export class CreateCustomerDto {
  @IsNotEmpty({ message: '客户代码不能为空' })
  @IsString()
  customerCode: string;

  @IsNotEmpty({ message: '客户名称不能为空' })
  @IsString()
  customerName: string;

  @IsNotEmpty({ message: '客户电话不能为空' })
  @IsString()
  customerPhone: string;

  @IsNotEmpty({ message: '客户联系人不能为空' })
  @IsString()
  contactPerson: string;

  @IsOptional()
  @IsString()
  customerAddress?: string;

  @IsOptional()
  @IsString()
  creditCode?: string;

  @IsOptional()
  @IsString()
  invoiceInfo?: string;

  @IsOptional()
  @IsString()
  status?: string;
} 