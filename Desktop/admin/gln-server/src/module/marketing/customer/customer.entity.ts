import { Entity, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn } from 'typeorm';

@Entity('marketing_customer')
export class CustomerEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ name: 'customer_code', length: 50, comment: '客户代码' })
  customerCode: string;

  @Column({ name: 'customer_name', length: 200, comment: '客户名称' })
  customerName: string;

  @Column({ name: 'customer_phone', length: 20, comment: '客户电话' })
  customerPhone: string;

  @Column({ name: 'contact_person', length: 50, comment: '客户联系人' })
  contactPerson: string;

  @Column({ name: 'customer_address', type: 'text', comment: '客户地址' })
  customerAddress: string;

  @Column({ name: 'credit_code', length: 100, comment: '客户信用代码' })
  creditCode: string;

  @Column({ name: 'invoice_info', type: 'text', comment: '客户开票信息' })
  invoiceInfo: string;

  @Column({ name: 'status', length: 20, default: '正常', comment: '客户状态' })
  status: string;

  @CreateDateColumn({ name: 'create_time', comment: '创建时间' })
  createTime: Date;

  @UpdateDateColumn({ name: 'update_time', comment: '更新时间' })
  updateTime: Date;
} 