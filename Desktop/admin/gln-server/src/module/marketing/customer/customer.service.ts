import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, Like } from 'typeorm';
import { CustomerEntity } from './customer.entity';
import { CreateCustomerDto } from './dto/create-customer.dto';
import { UpdateCustomerDto } from './dto/update-customer.dto';
import { QueryCustomerDto } from './dto/query-customer.dto';

@Injectable()
export class CustomerService {
  constructor(
    @InjectRepository(CustomerEntity)
    private readonly customerRepo: Repository<CustomerEntity>,
  ) {}

  async create(dto: CreateCustomerDto) {
    const entity = this.customerRepo.create(dto);
    return await this.customerRepo.save(entity);
  }

  async update(dto: UpdateCustomerDto) {
    await this.customerRepo.update(dto.id, dto);
    return this.customerRepo.findOne({ where: { id: dto.id } });
  }

  async remove(id: number) {
    return await this.customerRepo.delete(id);
  }

  async findOne(id: number) {
    return await this.customerRepo.findOne({ where: { id } });
  }

  async findAll(query: QueryCustomerDto) {
    const qb = this.customerRepo.createQueryBuilder('customer');
    
    if (query.customerName) {
      qb.andWhere('customer.customerName LIKE :customerName', { customerName: `%${query.customerName}%` });
    }
    if (query.customerCode) {
      qb.andWhere('customer.customerCode LIKE :customerCode', { customerCode: `%${query.customerCode}%` });
    }
    if (query.contactPerson) {
      qb.andWhere('customer.contactPerson LIKE :contactPerson', { contactPerson: `%${query.contactPerson}%` });
    }
    if (query.status) {
      qb.andWhere('customer.status = :status', { status: query.status });
    }
    
    qb.orderBy('customer.createTime', 'DESC');
    
    const pageNum = query.pageNum || 1;
    const pageSize = query.pageSize || 10;
    qb.skip((pageNum - 1) * pageSize).take(pageSize);
    
    const [rows, total] = await qb.getManyAndCount();
    return { rows, total };
  }

  async toggleStatus(id: number, status: string) {
    await this.customerRepo.update(id, { status });
    return this.customerRepo.findOne({ where: { id } });
  }
} 