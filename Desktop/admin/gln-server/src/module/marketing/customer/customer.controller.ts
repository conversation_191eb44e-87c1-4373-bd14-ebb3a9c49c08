import { Controller, Get, Post, Put, Delete, Body, Param, Query } from '@nestjs/common';
import { CustomerService } from './customer.service';
import { CreateCustomerDto } from './dto/create-customer.dto';
import { UpdateCustomerDto } from './dto/update-customer.dto';
import { QueryCustomerDto } from './dto/query-customer.dto';
import { RequirePermission } from 'src/common/decorators/require-premission.decorator';

@Controller('marketing/customer')
export class CustomerController {
  constructor(private readonly customerService: CustomerService) {}

  @Post()
  @RequirePermission('marketing:customer:add')
  async create(@Body() createCustomerDto: CreateCustomerDto) {
    return await this.customerService.create(createCustomerDto);
  }

  @Get('list')
  @RequirePermission('marketing:customer:list')
  async findAll(@Query() query: QueryCustomerDto) {
    return await this.customerService.findAll(query);
  }

  @Get(':id')
  @RequirePermission('marketing:customer:query')
  async findOne(@Param('id') id: number) {
    return await this.customerService.findOne(id);
  }

  @Put()
  @RequirePermission('marketing:customer:edit')
  async update(@Body() updateCustomerDto: UpdateCustomerDto) {
    return await this.customerService.update(updateCustomerDto);
  }

  @Delete(':id')
  @RequirePermission('marketing:customer:remove')
  async remove(@Param('id') id: number) {
    return await this.customerService.remove(id);
  }

  @Put('status/:id')
  @RequirePermission('marketing:customer:edit')
  async toggleStatus(@Param('id') id: number, @Body('status') status: string) {
    return await this.customerService.toggleStatus(id, status);
  }
} 