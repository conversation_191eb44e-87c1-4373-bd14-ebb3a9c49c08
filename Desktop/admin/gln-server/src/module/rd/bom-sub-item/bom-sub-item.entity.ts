import { Entity, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn } from 'typeorm';

@Entity('rd_bom_sub_item')
export class BomSubItemEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ name: 'material_attribute', length: 50, comment: '物料属性' })
  materialAttribute: string;

  @Column({ name: 'material_category', length: 50, comment: '物料类别' })
  materialCategory: string;

  @Column({ name: 'sub_item_number', length: 100, comment: '子件编号' })
  subItemNumber: string;

  @Column({ name: 'product_name', length: 200, comment: '产品名称' })
  productName: string;

  @Column({ name: 'specification', length: 200, nullable: true, comment: '产品规格' })
  specification: string;

  @Column({ name: 'other_info', length: 200, nullable: true, comment: '其他/箱号' })
  otherInfo: string;

  @Column({ name: 'unit', length: 20, comment: '单位' })
  unit: string;

  @Column({ name: 'supplier', length: 100, comment: '供应商' })
  supplier: string;

  @Column({ name: 'standard_price', type: 'decimal', precision: 10, scale: 2, comment: '标准进价' })
  standardPrice: number;

  @Column({ name: 'incremental_base', type: 'decimal', precision: 10, scale: 2, comment: '增购基数' })
  incrementalBase: number;

  @Column({ name: 'minimum_purchase', type: 'decimal', precision: 10, scale: 2, comment: '最低采购量' })
  minimumPurchase: number;

  @CreateDateColumn({ name: 'create_time', comment: '创建时间' })
  createTime: Date;

  @UpdateDateColumn({ name: 'update_time', comment: '更新时间' })
  updateTime: Date;
} 