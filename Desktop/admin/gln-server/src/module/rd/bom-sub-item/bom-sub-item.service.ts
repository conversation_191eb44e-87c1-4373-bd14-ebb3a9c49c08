import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, Like } from 'typeorm';
import { BomSubItemEntity } from './bom-sub-item.entity';
import { CreateBomSubItemDto } from './dto/create-bom-sub-item.dto';
import { UpdateBomSubItemDto } from './dto/update-bom-sub-item.dto';
import { QueryBomSubItemDto } from './dto/query-bom-sub-item.dto';

@Injectable()
export class BomSubItemService {
  constructor(
    @InjectRepository(BomSubItemEntity)
    private bomSubItemRepository: Repository<BomSubItemEntity>,
  ) {}

  async create(createBomSubItemDto: CreateBomSubItemDto) {
    const bomSubItem = this.bomSubItemRepository.create(createBomSubItemDto);
    const result = await this.bomSubItemRepository.save(bomSubItem);
    return result;
  }

  async findAll(query: QueryBomSubItemDto) {
    const { pageNum = 1, pageSize = 10, materialAttribute, materialCategory, subItemNumber, productName } = query;
    
    const whereCondition: any = {};
    if (materialAttribute) {
      whereCondition.materialAttribute = materialAttribute;
    }
    if (materialCategory) {
      whereCondition.materialCategory = materialCategory;
    }
    if (subItemNumber) {
      whereCondition.subItemNumber = Like(`%${subItemNumber}%`);
    }
    if (productName) {
      whereCondition.productName = Like(`%${productName}%`);
    }

    const [rows, total] = await this.bomSubItemRepository.findAndCount({
      where: whereCondition,
      skip: (pageNum - 1) * pageSize,
      take: pageSize,
      order: { createTime: 'DESC' },
    });

    return {
      rows,
      total,
      pageNum,
      pageSize,
    };
  }

  async findOne(id: number) {
    const bomSubItem = await this.bomSubItemRepository.findOne({
      where: { id },
    });
    return bomSubItem;
  }

  async update(id: number, updateBomSubItemDto: UpdateBomSubItemDto) {
    await this.bomSubItemRepository.update(id, updateBomSubItemDto);
    return this.findOne(id);
  }

  async remove(id: number) {
    const bomSubItem = await this.findOne(id);
    if (bomSubItem) {
      await this.bomSubItemRepository.remove(bomSubItem);
    }
    return { message: '删除成功' };
  }
} 