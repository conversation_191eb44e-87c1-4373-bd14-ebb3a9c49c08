import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { BomSubItemService } from './bom-sub-item.service';
import { BomSubItemController } from './bom-sub-item.controller';
import { BomSubItemEntity } from './bom-sub-item.entity';

@Module({
  imports: [TypeOrmModule.forFeature([BomSubItemEntity])],
  controllers: [BomSubItemController],
  providers: [BomSubItemService],
  exports: [BomSubItemService],
})
export class BomSubItemModule {} 