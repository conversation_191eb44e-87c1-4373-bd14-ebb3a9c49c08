import { Controller, Get, Post, Body, Patch, Param, Delete, Query } from '@nestjs/common';
import { ApiTags, ApiOperation } from '@nestjs/swagger';
import { BomSubItemService } from './bom-sub-item.service';
import { CreateBomSubItemDto } from './dto/create-bom-sub-item.dto';
import { UpdateBomSubItemDto } from './dto/update-bom-sub-item.dto';
import { QueryBomSubItemDto } from './dto/query-bom-sub-item.dto';

@ApiTags('BOM子件管理')
@Controller('rd/bom-sub-item')
export class BomSubItemController {
  constructor(private readonly bomSubItemService: BomSubItemService) {}

  @Post()
  @ApiOperation({ summary: '新增BOM子件' })
  create(@Body() createBomSubItemDto: CreateBomSubItemDto) {
    return this.bomSubItemService.create(createBomSubItemDto);
  }

  @Get('list')
  @ApiOperation({ summary: '查询BOM子件列表' })
  findAll(@Query() query: QueryBomSubItemDto) {
    return this.bomSubItemService.findAll(query);
  }

  @Get(':id')
  @ApiOperation({ summary: '查询BOM子件详情' })
  findOne(@Param('id') id: string) {
    return this.bomSubItemService.findOne(+id);
  }

  @Patch(':id')
  @ApiOperation({ summary: '修改BOM子件' })
  update(@Param('id') id: string, @Body() updateBomSubItemDto: UpdateBomSubItemDto) {
    return this.bomSubItemService.update(+id, updateBomSubItemDto);
  }

  @Delete(':id')
  @ApiOperation({ summary: '删除BOM子件' })
  remove(@Param('id') id: string) {
    return this.bomSubItemService.remove(+id);
  }
} 