import { IsOptional, IsString } from 'class-validator';
import { Transform } from 'class-transformer';

export class QueryBomSubItemDto {
  @IsOptional()
  @IsString()
  materialAttribute?: string;

  @IsOptional()
  @IsString()
  materialCategory?: string;

  @IsOptional()
  @IsString()
  subItemNumber?: string;

  @IsOptional()
  @IsString()
  productName?: string;

  @IsOptional()
  @Transform(({ value }) => parseInt(value))
  pageNum?: number = 1;

  @IsOptional()
  @Transform(({ value }) => parseInt(value))
  pageSize?: number = 10;
} 