import { IsNotEmpty, IsString, IsN<PERSON>ber, IsOptional } from 'class-validator';

export class CreateBomSubItemDto {
  @IsNotEmpty({ message: '物料属性不能为空' })
  @IsString()
  materialAttribute: string;

  @IsNotEmpty({ message: '物料类别不能为空' })
  @IsString()
  materialCategory: string;

  @IsNotEmpty({ message: '子件编号不能为空' })
  @IsString()
  subItemNumber: string;

  @IsNotEmpty({ message: '产品名称不能为空' })
  @IsString()
  productName: string;

  @IsOptional()
  @IsString()
  specification?: string;

  @IsOptional()
  @IsString()
  otherInfo?: string;

  @IsNotEmpty({ message: '单位不能为空' })
  @IsString()
  unit: string;

  @IsNotEmpty({ message: '供应商不能为空' })
  @IsString()
  supplier: string;

  @IsNotEmpty({ message: '标准进价不能为空' })
  @IsNumber()
  standardPrice: number;

  @IsNotEmpty({ message: '增购基数不能为空' })
  @IsNumber()
  incrementalBase: number;

  @IsNotEmpty({ message: '最低采购量不能为空' })
  @IsNumber()
  minimumPurchase: number;
} 