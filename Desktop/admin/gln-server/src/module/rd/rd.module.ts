import { Module } from '@nestjs/common';
import { TechnicalDataModule } from './technical-data/technical-data.module';
import { ProductModule } from './product/product.module';
import { BomSubItemModule } from './bom-sub-item/bom-sub-item.module';

@Module({
  imports: [TechnicalDataModule, ProductModule, BomSubItemModule],
  exports: [TechnicalDataModule, ProductModule, BomSubItemModule],
})
export class RdModule {} 