import { IsNotEmpty, IsString, IsDateString, IsOptional } from 'class-validator';

export class CreateTechnicalDataDto {
  @IsNotEmpty({ message: '资料名称不能为空' })
  @IsString()
  dataName: string;

  @IsNotEmpty({ message: '订单号不能为空' })
  @IsString()
  orderNumber: string;

  @IsNotEmpty({ message: '产品型号不能为空' })
  @IsString()
  productModel: string;

  @IsNotEmpty({ message: '使用时间不能为空' })
  @IsDateString()
  useTime: string;

  @IsOptional()
  @IsString()
  fileName?: string;

  @IsOptional()
  @IsString()
  fileUrl?: string;
} 