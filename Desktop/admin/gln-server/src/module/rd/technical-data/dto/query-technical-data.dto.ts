import { IsOptional, IsString, IsDateString } from 'class-validator';
import { Transform } from 'class-transformer';

export class QueryTechnicalDataDto {
  @IsOptional()
  @IsString()
  dataName?: string;

  @IsOptional()
  @IsString()
  orderNumber?: string;

  @IsOptional()
  @IsString()
  productModel?: string;

  @IsOptional()
  @IsDateString()
  useTime?: string;

  @IsOptional()
  @Transform(({ value }) => parseInt(value))
  pageNum?: number = 1;

  @IsOptional()
  @Transform(({ value }) => parseInt(value))
  pageSize?: number = 10;
} 