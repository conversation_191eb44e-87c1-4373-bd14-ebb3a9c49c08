import { Entity, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn } from 'typeorm';

@Entity('rd_technical_data')
export class TechnicalDataEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ name: 'data_name', length: 200, comment: '资料名称' })
  dataName: string;

  @Column({ name: 'order_number', length: 100, comment: '订单号' })
  orderNumber: string;

  @Column({ name: 'product_model', length: 100, comment: '产品型号' })
  productModel: string;

  @Column({ name: 'use_time', type: 'date', comment: '使用时间' })
  useTime: Date;

  @Column({ name: 'file_name', length: 200, comment: '文件名称' })
  fileName: string;

  @Column({ name: 'file_url', length: 500, comment: '文件URL' })
  fileUrl: string;

  @Column({ name: 'submitter', length: 50, comment: '提交人' })
  submitter: string;

  @CreateDateColumn({ name: 'create_time', comment: '创建时间' })
  createTime: Date;

  @UpdateDateColumn({ name: 'update_time', comment: '更新时间' })
  updateTime: Date;
} 