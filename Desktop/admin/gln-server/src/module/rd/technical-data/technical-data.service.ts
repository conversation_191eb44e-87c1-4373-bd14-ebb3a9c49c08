import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, Like } from 'typeorm';
import { TechnicalDataEntity } from './technical-data.entity';
import { CreateTechnicalDataDto } from './dto/create-technical-data.dto';
import { UpdateTechnicalDataDto } from './dto/update-technical-data.dto';
import { QueryTechnicalDataDto } from './dto/query-technical-data.dto';
import { ApiDataResponse } from '../../../common/decorators/apiDataResponse.decorator';

@Injectable()
export class TechnicalDataService {
  constructor(
    @InjectRepository(TechnicalDataEntity)
    private technicalDataRepository: Repository<TechnicalDataEntity>,
  ) {}

  @ApiDataResponse()
  async create(createTechnicalDataDto: CreateTechnicalDataDto, username: string) {
    const technicalData = this.technicalDataRepository.create({
      ...createTechnicalDataDto,
      submitter: username,
    });
    const result = await this.technicalDataRepository.save(technicalData);
    return result;
  }

  @ApiDataResponse()
  async findAll(query: QueryTechnicalDataDto) {
    const { pageNum = 1, pageSize = 10, dataName, orderNumber, productModel, useTime } = query;
    
    const whereCondition: any = {};
    if (dataName) {
      whereCondition.dataName = Like(`%${dataName}%`);
    }
    if (orderNumber) {
      whereCondition.orderNumber = Like(`%${orderNumber}%`);
    }
    if (productModel) {
      whereCondition.productModel = Like(`%${productModel}%`);
    }
    if (useTime) {
      whereCondition.useTime = useTime;
    }

    const [rows, total] = await this.technicalDataRepository.findAndCount({
      where: whereCondition,
      skip: (pageNum - 1) * pageSize,
      take: pageSize,
      order: { createTime: 'DESC' },
    });

    return {
      rows,
      total,
      pageNum,
      pageSize,
    };
  }

  @ApiDataResponse()
  async findOne(id: number) {
    const technicalData = await this.technicalDataRepository.findOne({
      where: { id },
    });
    return technicalData;
  }

  @ApiDataResponse()
  async update(id: number, updateTechnicalDataDto: UpdateTechnicalDataDto) {
    await this.technicalDataRepository.update(id, updateTechnicalDataDto);
    return this.findOne(id);
  }

  @ApiDataResponse()
  async remove(id: number) {
    const technicalData = await this.findOne(id);
    if (technicalData) {
      await this.technicalDataRepository.remove(technicalData);
    }
    return { message: '删除成功' };
  }

  @ApiDataResponse()
  async download(id: number) {
    const technicalData = await this.findOne(id);
    if (!technicalData) {
      throw new Error('文件不存在');
    }
    return {
      fileName: technicalData.fileName,
      fileUrl: technicalData.fileUrl,
    };
  }
} 