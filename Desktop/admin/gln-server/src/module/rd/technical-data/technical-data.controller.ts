import { Controller, Get, Post, Body, Patch, Param, Delete, Query, UseGuards, Req } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiBearerAuth } from '@nestjs/swagger';
import { TechnicalDataService } from './technical-data.service';
import { CreateTechnicalDataDto } from './dto/create-technical-data.dto';
import { UpdateTechnicalDataDto } from './dto/update-technical-data.dto';
import { QueryTechnicalDataDto } from './dto/query-technical-data.dto';
import { JwtAuthGuard } from '../../../common/guards/auth.guard';
import { ApiDataResponse } from '../../../common/decorators/apiDataResponse.decorator';

@ApiTags('技术资料管理')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard)
@Controller('rd/technical-data')
export class TechnicalDataController {
  constructor(private readonly technicalDataService: TechnicalDataService) {}

  @Post()
  @ApiOperation({ summary: '新增技术资料' })
  @ApiDataResponse()
  create(@Body() createTechnicalDataDto: CreateTechnicalDataDto, @Req() req: any) {
    return this.technicalDataService.create(createTechnicalDataDto, req.user.username);
  }

  @Get('list')
  @ApiOperation({ summary: '查询技术资料列表' })
  @ApiDataResponse()
  findAll(@Query() query: QueryTechnicalDataDto) {
    return this.technicalDataService.findAll(query);
  }

  @Get(':id')
  @ApiOperation({ summary: '查询技术资料详情' })
  @ApiDataResponse()
  findOne(@Param('id') id: string) {
    return this.technicalDataService.findOne(+id);
  }

  @Patch(':id')
  @ApiOperation({ summary: '修改技术资料' })
  @ApiDataResponse()
  update(@Param('id') id: string, @Body() updateTechnicalDataDto: UpdateTechnicalDataDto) {
    return this.technicalDataService.update(+id, updateTechnicalDataDto);
  }

  @Delete(':id')
  @ApiOperation({ summary: '删除技术资料' })
  @ApiDataResponse()
  remove(@Param('id') id: string) {
    return this.technicalDataService.remove(+id);
  }

  @Get('download/:id')
  @ApiOperation({ summary: '下载技术资料文件' })
  @ApiDataResponse()
  download(@Param('id') id: string) {
    return this.technicalDataService.download(+id);
  }
} 