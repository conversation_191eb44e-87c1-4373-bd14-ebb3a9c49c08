import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { TechnicalDataService } from './technical-data.service';
import { TechnicalDataController } from './technical-data.controller';
import { TechnicalDataEntity } from './technical-data.entity';

@Module({
  imports: [TypeOrmModule.forFeature([TechnicalDataEntity])],
  controllers: [TechnicalDataController],
  providers: [TechnicalDataService],
  exports: [TechnicalDataService],
})
export class TechnicalDataModule {} 