import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, Like } from 'typeorm';
import { ProductEntity } from './product.entity';
import { CreateProductDto } from './dto/create-product.dto';
import { UpdateProductDto } from './dto/update-product.dto';
import { QueryProductDto } from './dto/query-product.dto';

@Injectable()
export class ProductService {
  constructor(
    @InjectRepository(ProductEntity)
    private productRepository: Repository<ProductEntity>,
  ) {}

  async create(createProductDto: CreateProductDto) {
    const product = this.productRepository.create(createProductDto);
    const result = await this.productRepository.save(product);
    return result;
  }

  async findAll(query: QueryProductDto) {
    const { pageNum = 1, pageSize = 10, productName, productModel, bomStatus } = query;
    
    const whereCondition: any = {};
    if (productName) {
      whereCondition.productName = Like(`%${productName}%`);
    }
    if (productModel) {
      whereCondition.productModel = Like(`%${productModel}%`);
    }
    if (bomStatus) {
      whereCondition.bomStatus = bomStatus;
    }

    const [rows, total] = await this.productRepository.findAndCount({
      where: whereCondition,
      skip: (pageNum - 1) * pageSize,
      take: pageSize,
      order: { createTime: 'DESC' },
    });

    return {
      rows,
      total,
      pageNum,
      pageSize,
    };
  }

  async findOne(id: number) {
    const product = await this.productRepository.findOne({
      where: { id },
    });
    return product;
  }

  async update(id: number, updateProductDto: UpdateProductDto) {
    await this.productRepository.update(id, updateProductDto);
    return this.findOne(id);
  }

  async remove(id: number) {
    const product = await this.findOne(id);
    if (product) {
      await this.productRepository.remove(product);
    }
    return { message: '删除成功' };
  }

  async getAccessories(productId: number) {
    // 这里应该从BOM子件表中查询该产品的配件
    // 暂时返回空数组
    return [];
  }

  async updateAccessories(productId: number, accessories: any[]) {
    // 这里应该更新产品的配件关系
    // 暂时返回成功消息
    return { message: '配件更新成功' };
  }
} 