import { Controller, Get, Post, Body, Patch, Param, Delete, Query, Put } from '@nestjs/common';
import { ApiTags, ApiOperation } from '@nestjs/swagger';
import { ProductService } from './product.service';
import { CreateProductDto } from './dto/create-product.dto';
import { UpdateProductDto } from './dto/update-product.dto';
import { QueryProductDto } from './dto/query-product.dto';

@ApiTags('产品列表管理')
@Controller('rd/product')
export class ProductController {
  constructor(private readonly productService: ProductService) {}

  @Post()
  @ApiOperation({ summary: '新增产品' })
  create(@Body() createProductDto: CreateProductDto) {
    return this.productService.create(createProductDto);
  }

  @Get('list')
  @ApiOperation({ summary: '查询产品列表' })
  findAll(@Query() query: QueryProductDto) {
    return this.productService.findAll(query);
  }

  @Get(':id')
  @ApiOperation({ summary: '查询产品详情' })
  findOne(@Param('id') id: string) {
    return this.productService.findOne(+id);
  }

  @Patch(':id')
  @ApiOperation({ summary: '修改产品' })
  update(@Param('id') id: string, @Body() updateProductDto: UpdateProductDto) {
    return this.productService.update(+id, updateProductDto);
  }

  @Delete(':id')
  @ApiOperation({ summary: '删除产品' })
  remove(@Param('id') id: string) {
    return this.productService.remove(+id);
  }

  @Get('accessories/:id')
  @ApiOperation({ summary: '获取产品配件列表' })
  getAccessories(@Param('id') id: string) {
    return this.productService.getAccessories(+id);
  }

  @Put('accessories/:id')
  @ApiOperation({ summary: '更新产品配件列表' })
  updateAccessories(@Param('id') id: string, @Body() accessories: any[]) {
    return this.productService.updateAccessories(+id, accessories);
  }
} 