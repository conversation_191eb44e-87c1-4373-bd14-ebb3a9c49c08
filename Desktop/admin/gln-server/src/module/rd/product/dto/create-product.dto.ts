import { IsNotEmpty, IsString, IsN<PERSON>ber, IsOptional } from 'class-validator';

export class CreateProductDto {
  @IsNotEmpty({ message: '产品名称不能为空' })
  @IsString()
  productName: string;

  @IsNotEmpty({ message: '产品型号不能为空' })
  @IsString()
  productModel: string;

  @IsNotEmpty({ message: 'BOM状态不能为空' })
  @IsString()
  bomStatus: string;

  @IsNotEmpty({ message: '产品重量不能为空' })
  @IsNumber()
  weight: number;

  @IsNotEmpty({ message: '单位不能为空' })
  @IsString()
  unit: string;

  @IsOptional()
  @IsString()
  drawing?: string;
} 