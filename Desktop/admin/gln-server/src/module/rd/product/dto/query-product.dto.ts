import { IsOptional, IsString } from 'class-validator';
import { Transform } from 'class-transformer';

export class QueryProductDto {
  @IsOptional()
  @IsString()
  productName?: string;

  @IsOptional()
  @IsString()
  productModel?: string;

  @IsOptional()
  @IsString()
  bomStatus?: string;

  @IsOptional()
  @Transform(({ value }) => parseInt(value))
  pageNum?: number = 1;

  @IsOptional()
  @Transform(({ value }) => parseInt(value))
  pageSize?: number = 10;
} 