import { Entity, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn } from 'typeorm';

@Entity('rd_product')
export class ProductEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ name: 'product_name', length: 200, comment: '产品名称' })
  productName: string;

  @Column({ name: 'product_model', length: 100, comment: '产品型号' })
  productModel: string;

  @Column({ name: 'bom_status', length: 20, comment: 'BOM状态' })
  bomStatus: string;

  @Column({ name: 'weight', type: 'decimal', precision: 10, scale: 2, comment: '产品重量' })
  weight: number;

  @Column({ name: 'unit', length: 20, comment: '单位' })
  unit: string;

  @Column({ name: 'drawing', length: 500, nullable: true, comment: '图纸URL' })
  drawing: string;

  @CreateDateColumn({ name: 'create_time', comment: '创建时间' })
  createTime: Date;

  @UpdateDateColumn({ name: 'update_time', comment: '更新时间' })
  updateTime: Date;
} 