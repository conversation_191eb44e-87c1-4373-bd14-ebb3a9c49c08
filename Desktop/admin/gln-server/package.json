{"name": "nest-v10", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "npm run start:dev", "start:dev": "cross-env NODE_ENV=development nest start --watch", "start:test": "cross-env NODE_ENV=test nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "cross-env NODE_ENV=production node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json"}, "dependencies": {"@nestjs/axios": "^3.0.2", "@nestjs/common": "^10.0.0", "@nestjs/config": "^3.1.1", "@nestjs/core": "^10.0.0", "@nestjs/jwt": "^10.2.0", "@nestjs/mapped-types": "*", "@nestjs/passport": "^10.0.3", "@nestjs/platform-express": "^10.0.0", "@nestjs/schedule": "^4.1.2", "@nestjs/swagger": "^7.1.17", "@nestjs/typeorm": "^10.0.1", "@songkeys/nestjs-redis": "^10.0.0", "@types/lodash": "^4.17.13", "@xobj/core": "^1.3.2", "archiver": "^7.0.1", "axios": "^1.6.7", "bcryptjs": "^3.0.2", "chalk": "^5.3.0", "class-transformer": "^0.5.1", "class-validator": "^0.14.1", "cos-nodejs-sdk-v5": "^2.16.0-beta.3", "cron": "^3.3.1", "dayjs": "^1.11.10", "exceljs": "^4.4.0", "express-rate-limit": "^7.1.5", "fs-extra": "^11.2.0", "helmet": "^7.1.0", "iconv-lite": "^0.6.3", "ioredis": "^5.4.1", "js-yaml": "^4.1.0", "lodash": "^4.17.21", "mime-types": "^2.1.35", "mysql2": "^3.6.5", "node-disk-info": "^1.3.0", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "path-to-regexp": "^6.2.1", "reflect-metadata": "^0.1.13", "request-ip": "^3.3.0", "rxjs": "^7.8.1", "svg-captcha": "^1.4.0", "typeorm": "^0.3.20", "useragent": "^2.3.0", "uuid": "^9.0.1", "winston": "^3.15.0", "winston-daily-rotate-file": "^5.0.0"}, "devDependencies": {"@nestjs/cli": "^10.0.0", "@nestjs/schematics": "^10.0.0", "@nestjs/testing": "^10.0.0", "@types/express": "^4.17.17", "@types/jest": "^29.5.2", "@types/mime-types": "^2.1.4", "@types/multer": "^1.4.11", "@types/node": "^20.3.1", "@types/supertest": "^2.0.12", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "cross-env": "^7.0.3", "eslint": "^8.42.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.0", "jest": "^29.5.0", "prettier": "^3.0.0", "source-map-support": "^0.5.21", "supertest": "^6.3.3", "ts-jest": "^29.1.0", "ts-loader": "^9.4.3", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0", "typescript": "^5.1.3"}, "resolutions": {"string-width": "4.2.3"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}