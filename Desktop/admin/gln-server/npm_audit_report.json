{"auditReportVersion": 2, "vulnerabilities": {"cos-nodejs-sdk-v5": {"name": "cos-nodejs-sdk-v5", "severity": "moderate", "isDirect": true, "via": ["request"], "effects": [], "range": "<=2.15.1", "nodes": ["node_modules/cos-nodejs-sdk-v5"], "fixAvailable": {"name": "cos-nodejs-sdk-v5", "version": "2.16.0-beta.3", "isSemVerMajor": true}}, "request": {"name": "request", "severity": "moderate", "isDirect": false, "via": [{"source": 1096727, "name": "request", "dependency": "request", "title": "Server-Side Request Forgery in Request", "url": "https://github.com/advisories/GHSA-p8p7-x288-28g6", "severity": "moderate", "cwe": ["CWE-918"], "cvss": {"score": 6.1, "vectorString": "CVSS:3.1/AV:N/AC:L/PR:N/UI:R/S:C/C:L/I:L/A:N"}, "range": "<=2.88.2"}, "tough-cookie"], "effects": ["cos-nodejs-sdk-v5"], "range": "*", "nodes": ["node_modules/request"], "fixAvailable": {"name": "cos-nodejs-sdk-v5", "version": "2.16.0-beta.3", "isSemVerMajor": true}}, "tough-cookie": {"name": "tough-cookie", "severity": "moderate", "isDirect": false, "via": [{"source": 1097682, "name": "tough-cookie", "dependency": "tough-cookie", "title": "tough-cookie Prototype Pollution vulnerability", "url": "https://github.com/advisories/GHSA-72xf-g2v4-qvf3", "severity": "moderate", "cwe": ["CWE-1321"], "cvss": {"score": 6.5, "vectorString": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:L/I:L/A:N"}, "range": "<4.1.3"}], "effects": ["request"], "range": "<4.1.3", "nodes": ["node_modules/tough-cookie"], "fixAvailable": {"name": "cos-nodejs-sdk-v5", "version": "2.16.0-beta.3", "isSemVerMajor": true}}, "useragent": {"name": "useragent", "severity": "moderate", "isDirect": true, "via": [{"source": 1100298, "name": "useragent", "dependency": "useragent", "title": "useragent Regular Expression Denial of Service vulnerability", "url": "https://github.com/advisories/GHSA-mgfv-m47x-4wqp", "severity": "moderate", "cwe": ["CWE-1333"], "cvss": {"score": 7.5, "vectorString": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:H"}, "range": "<=2.3.0"}], "effects": [], "range": "*", "nodes": ["node_modules/useragent"], "fixAvailable": false}}, "metadata": {"vulnerabilities": {"info": 0, "low": 0, "moderate": 4, "high": 0, "critical": 0, "total": 4}, "dependencies": {"prod": 500, "dev": 620, "optional": 4, "peer": 30, "peerOptional": 0, "total": 1147}}}