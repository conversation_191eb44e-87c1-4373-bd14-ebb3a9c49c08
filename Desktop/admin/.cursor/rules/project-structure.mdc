---
description: 
globs: 
alwaysApply: false
---
# GLN 项目结构指南

## 项目概述
这是一个前后端分离的管理系统项目，包含：
- `gln-cms/` - 前端 Vue3 + Vite 项目
- `gln-server/` - 后端 NestJS 项目

## 前端项目 (gln-cms)

### 核心配置文件
- [vite.config.js](mdc:gln-cms/vite.config.js) - Vite 构建配置，包含代理设置
- [src/utils/request.js](mdc:gln-cms/src/utils/request.js) - Axios 请求封装，自动添加 /dev-api 前缀

### API 请求配置
- 所有 API 请求会自动添加 `/dev-api` 前缀
- 代理配置将 `/dev-api` 请求转发到 `http://localhost:8080/`
- 前端开发服务器运行在 `http://localhost:8888`

### 重要目录结构
- `src/api/` - API 接口定义
- `src/utils/` - 工具函数
- `src/store/` - Pinia 状态管理
- `src/views/` - 页面组件

## 后端项目 (gln-server)

### 核心模块
- `src/module/main/` - 主要控制器，包含登录、登出接口
- `src/module/system/` - 系统管理模块
- `src/module/monitor/` - 监控模块
- `src/common/` - 公共工具和装饰器

### 数据库配置
- 默认数据库名：`gln_server`
- 需要先创建数据库：`CREATE DATABASE gln_server`
- 可导入初始化脚本：[db/init.sql](mdc:gln-server/db/init.sql)

### 服务依赖
- MySQL 数据库服务
- Redis 缓存服务（可选，但推荐）

## 开发环境启动

### 前端启动
```bash
cd gln-cms
npm install
npm run dev
```

### 后端启动
```bash
cd gln-server
npm install
npm run start:dev
```

### 数据库准备
1. 启动 MySQL 服务
2. 创建数据库：`CREATE DATABASE gln_server`
3. 可选：导入初始化数据

## 常见问题解决

### 404 错误
- 确保前端请求带有 `/dev-api` 前缀
- 检查后端服务是否在 8080 端口运行
- 确认代理配置正确

### 数据库连接错误
- 检查 MySQL 服务状态
- 确认数据库用户名密码正确
- 验证数据库是否存在

### Redis 连接错误
- 启动 Redis 服务：`redis-server`
- 或修改配置禁用 Redis（不推荐）
