# 格莱恩生产管理系统需求说明书

---

## 目录
1. 行政人事部
2. 物资采购部
3. 财务资金部
4. 生产制造部
   - 组装车间
   - 自动化车间
   - 挤出车间
   - 硫化车间
   - 灭菌车间
   - 注塑1车间
   - 注塑2车间
5. 计划物控部
6. 质量管理部
7. 技术研发部
8. 设备研发部
9. 市场营销部
10. 小程序端主要功能
11. 运营链路

---

## 1. 行政人事部

### 1.1 入职员工信息录入
- **页面名称**：员工信息录入页
- **页面地址**：/admin/employeeList
- **主要按钮/功能**：
  - 新增员工（按钮）
  - 编辑员工（按钮）
  - 删除员工（按钮）
  - 批量导入（按钮）
  - 导出（按钮）
  - 查询（按钮）
  - 附件上传（身份证图片、健康证图片）
  - 保存/提交（按钮）
- **接口**：
  - 查询员工列表：GET /api/admin/employee/list
    - 参数：name、dept、entryDateStart、entryDateEnd、pageNum、pageSize
  - 新增员工：POST /api/admin/employee
    - 字段：emp_no、name、entry_date、dept、position、gender、political、age、nation、education、graduate_school、address、id_card、phone、bank_info、bank_card、emergency_phone、current_addr、emp_type、social_security、labor_contract、emp_status、recruit_source、leave_date、health_cert、id_card_img、health_cert_img、remark
  - 编辑员工：PUT /api/admin/employee
    - 字段同上，需包含id
  - 删除员工：DELETE /api/admin/employee/{id}
  - 批量删除员工：DELETE /api/admin/employee/batch
    - 字段：ids
  - 离职操作：POST /api/admin/employee/leave
    - 字段：id、leave_date
  - 导入员工：POST /api/employee/importData
    - 字段：文件（xls/xlsx）、updateSupport
  - 下载导入模板：GET /employee/importTemplate
- **字段说明**：
  - emp_no：员工编号（字符串）
  - name：姓名（字符串）
  - entry_date：入职日期（YYYY-MM-DD）
  - dept：部门（字符串）
  - position：职位（字符串）
  - gender：性别（0男/1女/2未知）
  - political：政治面貌（字符串）
  - age：年龄（数字）
  - nation：民族（字符串）
  - education：学历（字符串）
  - graduate_school：毕业院校（字符串）
  - address：户籍地址（字符串）
  - id_card：身份证号（字符串）
  - phone：手机号（字符串）
  - bank_info：银行信息（字符串）
  - bank_card：银行卡号（字符串）
  - emergency_phone：紧急联系人电话（字符串）
  - current_addr：现居住地址（字符串）
  - emp_type：用工性质（字符串）
  - social_security：是否缴纳社保（0否/1是）
  - labor_contract：是否签劳动合同（0否/1是）
  - emp_status：员工状态（字符串）
  - recruit_source：招聘信息渠道（字符串）
  - leave_date：离职日期（YYYY-MM-DD）
  - health_cert：是否办理健康证（0否/1是）
  - id_card_img：身份证图片（url）
  - health_cert_img：健康证图片（url）
  - remark：备注（字符串）
- **数据来源/关联**：
  - 部门、岗位来源于组织架构表（/api/system/dept）
  - 附件存储于文件服务器
- **实际减负**：
  - 支持员工自助录入，减少HR手工录入压力
  - 批量导入提升效率，降低重复劳动
  - 附件上传自动对接文件服务器，减少人工整理

### 1.2 员工离职
- **页面名称**：员工离职管理页
- **页面地址**：/admin/employeeLeave
- **主要按钮/功能**：
  - 发起离职申请（按钮）
  - 审批（同意/驳回按钮）
  - 离职详情查看（按钮）
  - 查询（按钮）
  - 导出（按钮）
- **接口**：
  - 查询离职申请列表：GET /api/admin/employee/leave/list
    - 参数：name、dept、leaveDateStart、leaveDateEnd、status、pageNum、pageSize
  - 发起离职申请：POST /api/admin/employee/leave/apply
    - 字段：employee_id、leave_reason、leave_date、attachment
  - 审批离职申请：POST /api/admin/employee/leave/approve
    - 字段：apply_id、approver、opinion、status
  - 离职详情：GET /api/admin/employee/leave/{id}
  - 导出离职记录：GET /api/admin/employee/leave/export
- **字段说明**：
  - employee_id：员工ID（数字）
  - leave_reason：离职原因（字符串）
  - leave_date：离职日期（YYYY-MM-DD）
  - attachment：附件（url）
  - apply_id：申请ID（数字）
  - approver：审批人（字符串）
  - opinion：审批意见（字符串）
  - status：状态（字符串）
- **数据来源/关联**：
  - 员工信息表、审批流表
  - 附件存储于文件服务器
- **实际减负**：
  - 流程线上化，自动流转，减少纸质审批和人工通知

### 1.3 员工调岗
- **页面名称**：员工调岗管理页
- **页面地址**：/admin/employeeTransfer
- **主要按钮/功能**：
  - 发起调岗申请（按钮）
  - 审批（同意/驳回按钮）
  - 调岗详情查看（按钮）
  - 查询（按钮）
  - 导出（按钮）
- **接口**：
  - 查询调岗申请列表：GET /api/admin/employee/transfer/list
    - 参数：name、dept、transferDateStart、transferDateEnd、status、pageNum、pageSize
  - 发起调岗申请：POST /api/admin/employee/transfer/apply
    - 字段：employee_id、old_position、新_position、transfer_reason、transfer_date
  - 审批调岗申请：POST /api/admin/employee/transfer/approve
    - 字段：apply_id、approver、opinion、status
  - 调岗详情：GET /api/admin/employee/transfer/{id}
  - 导出调岗记录：GET /api/admin/employee/transfer/export
- **字段说明**：
  - employee_id：员工ID（数字）
  - old_position：原岗位（字符串）
  - new_position：新岗位（字符串）
  - transfer_reason：调岗原因（字符串）
  - transfer_date：调岗日期（YYYY-MM-DD）
  - apply_id：申请ID（数字）
  - approver：审批人（字符串）
  - opinion：审批意见（字符串）
  - status：状态（字符串）
- **数据来源/关联**：
  - 员工信息表、岗位表、审批流表
- **实际减负**：
  - 自动同步岗位、薪资，减少HR手动调整

### 1.4 办公用品申请-采购-领取
- **页面名称**：办公用品管理页
- **页面地址**：/admin/officeSupplies
- **主要按钮/功能**：
  - 申请采购（按钮）
  - 审批（按钮）
  - 入库（按钮）
  - 领用申请（按钮）
  - 领用审批（按钮）
  - 查询（按钮）
  - 导出（按钮）
- **接口**：
  - 采购申请：POST /api/admin/office/apply
    - 字段：applicant、item_name、model、quantity、purpose
  - 审批：POST /api/admin/office/approve
    - 字段：apply_id、approver、opinion、status
  - 入库：POST /api/admin/office/purchase
    - 字段：purchase_no、item、quantity、inbound_person
  - 查询库存：GET /api/admin/office/stock
  - 查询申请列表：GET /api/admin/office/apply/list
    - 参数：item_name、status、pageNum、pageSize
  - 导出库存：GET /api/admin/office/stock/export
- **字段说明**：
  - applicant：申请人（字符串）
  - item_name：物品名称（字符串）
  - model：型号（字符串）
  - quantity：数量（数字）
  - purpose：用途（字符串）
  - apply_id：申请ID（数字）
  - approver：审批人（字符串）
  - opinion：审批意见（字符串）
  - status：状态（字符串）
  - purchase_no：采购单号（字符串）
  - item：物品（字符串）
  - inbound_person：入库人（字符串）
- **数据来源/关联**：
  - 物品字典表、库存表、审批流表
- **实际减负**：
  - 采购、领用全流程线上化，自动扣减库存，减少手工台账

### 1.5 员工考勤记录
- **页面名称**：考勤管理页
- **页面地址**：/admin/attendance
- **主要按钮/功能**：
  - 查看考勤（按钮）
  - 导出考勤（按钮）
  - 异常标记（按钮）
  - 查询（按钮）
- **接口**：
  - 打卡上传：POST /api/admin/attendance/clockin
    - 字段：employee_id、clockin_time、type、location
  - 查询考勤：GET /api/admin/attendance/list
    - 参数：employee_id、dateStart、dateEnd、pageNum、pageSize
  - 导出考勤：GET /api/admin/attendance/export
- **字段说明**：
  - employee_id：员工ID（数字）
  - clockin_time：打卡时间（YYYY-MM-DD HH:mm:ss）
  - type：类型（上班/下班/外出等，字符串）
  - location：地点（字符串）
- **数据来源/关联**：
  - 打卡设备/小程序、员工信息表
- **实际减负**：
  - 自动统计考勤，异常自动标记，减少人工统计

### 1.6 员工请假记录
- **页面名称**：请假管理页
- **页面地址**：/admin/leave
- **主要按钮/功能**：
  - 发起请假申请（按钮）
  - 审批（按钮）
  - 查看请假记录（按钮）
  - 查询（按钮）
  - 导出（按钮）
- **接口**：
  - 申请：POST /api/admin/leave/apply
    - 字段：employee_id、leave_type、start_time、end_time、reason、attachment
  - 审批：POST /api/admin/leave/approve
    - 字段：apply_id、approver、opinion、status
  - 查询：GET /api/admin/leave/list
    - 参数：employee_id、leave_type、dateStart、dateEnd、status、pageNum、pageSize
  - 导出：GET /api/admin/leave/export
- **字段说明**：
  - employee_id：员工ID（数字）
  - leave_type：请假类型（字符串）
  - start_time：开始时间（YYYY-MM-DD HH:mm:ss）
  - end_time：结束时间（YYYY-MM-DD HH:mm:ss）
  - reason：请假原因（字符串）
  - attachment：附件（url）
  - apply_id：申请ID（数字）
  - approver：审批人（字符串）
  - opinion：审批意见（字符串）
  - status：状态（字符串）
- **数据来源/关联**：
  - 员工信息表、审批流表
- **实际减负**：
  - 流程自动流转，减少纸质单据

### 1.7 员工培训记录
- **页面名称**：培训管理页
- **页面地址**：/admin/training
- **主要按钮/功能**：
  - 新建培训计划（按钮）
  - 通知参与人（按钮）
  - 打印签到表（按钮）
  - 培训归档（按钮）
  - 查询（按钮）
  - 导出（按钮）
- **接口**：
  - 新建计划：POST /api/admin/training/create
    - 字段：topic、time、participants、material
  - 签到：POST /api/admin/training/signin
    - 字段：training_id、employee_id、signin_time
  - 查询：GET /api/admin/training/list
    - 参数：topic、dateStart、dateEnd、pageNum、pageSize
  - 导出：GET /api/admin/training/export
- **字段说明**：
  - topic：主题（字符串）
  - time：时间（YYYY-MM-DD HH:mm:ss）
  - participants：参与人（数组/字符串）
  - material：资料（url/字符串）
  - training_id：培训ID（数字）
  - employee_id：员工ID（数字）
  - signin_time：签到时间（YYYY-MM-DD HH:mm:ss）
- **数据来源/关联**：
  - 员工信息表、培训计划表
- **实际减负**：
  - 培训通知自动推送，签到自动归档

### 1.8 文件发放记录
- **页面名称**：文件发放管理页
- **页面地址**：/admin/fileGrant
- **主要按钮/功能**：
  - 上传文件（按钮）
  - 指定发放部门（下拉）
  - 打印（按钮）
  - 查询（按钮）
  - 导出（按钮）
- **接口**：
  - 上传：POST /api/admin/file/upload
    - 字段：file_name、type、grant_dept、dept_no、attachment
  - 查询：GET /api/admin/file/list
    - 参数：file_name、grant_dept、dateStart、dateEnd、pageNum、pageSize
  - 导出：GET /api/admin/file/export
- **字段说明**：
  - file_name：文件名（字符串）
  - type：类型（字符串）
  - grant_dept：发放部门（字符串）
  - dept_no：部门序号（字符串）
  - attachment：附件（url）
- **数据来源/关联**：
  - 文件服务器、部门表
- **实际减负**：
  - 文件发放、归档、打印一体化，减少纸质流转

### 1.9 薪资调整记录
- **页面名称**：薪资调整管理页
- **页面地址**：/admin/salaryAdjust
- **主要按钮/功能**：
  - 新增调整（按钮）
  - 审批（按钮）
  - 上传图片（按钮）
  - 查询（按钮）
  - 导出（按钮）
- **接口**：
  - 新增：POST /api/admin/salary/adjust
    - 字段：employee_id、type、amount、reason、image
  - 审批：POST /api/admin/salary/adjust/approve
    - 字段：adjust_id、approver、opinion、status
  - 查询：GET /api/admin/salary/adjust/list
    - 参数：employee_id、type、dateStart、dateEnd、status、pageNum、pageSize
  - 导出：GET /api/admin/salary/adjust/export
- **字段说明**：
  - employee_id：员工ID（数字）
  - type：类型（字符串）
  - amount：金额（数字）
  - reason：原因（字符串）
  - image：图片（url）
  - adjust_id：调整ID（数字）
  - approver：审批人（字符串）
  - opinion：审批意见（字符串）
  - status：状态（字符串）
- **数据来源/关联**：
  - 员工信息表、审批流表、工资表
- **实际减负**：
  - 调整自动计入工资，审批流线上化

### 1.10 文件夹
- **页面名称**：文件夹管理页
- **页面地址**：/admin/folder
- **主要按钮/功能**：
  - 上传文件（按钮）
  - 分类（下拉）
  - 下载（按钮）
  - 查询（按钮）
  - 导出（按钮）
- **接口**：
  - 上传：POST /api/admin/folder/upload
    - 字段：file_name、type、category、attachment
  - 查询：GET /api/admin/folder/list
    - 参数：file_name、category、dateStart、dateEnd、pageNum、pageSize
  - 导出：GET /api/admin/folder/export
- **字段说明**：
  - file_name：文件名（字符串）
  - type：类型（字符串）
  - category：分类（字符串）
  - attachment：附件（url）
- **数据来源/关联**：
  - 文件服务器
- **实际减负**：
  - 文件集中归档，便于查找和共享

---

## 2. 物资采购部

### 2.1 采购计划单
- **页面名称**：采购计划管理页
- **页面地址**：/purchase/plan
- **主要按钮/功能**：
  - 新建采购计划（按钮）
  - 编辑采购计划（按钮）
  - 删除采购计划（按钮）
  - 审批（按钮）
  - 导出（按钮）
  - 查询（按钮）
- **接口**：
  - 查询采购计划列表：GET /api/purchase/plan/list
    - 参数：category、item、status、dateStart、dateEnd、pageNum、pageSize
  - 新建采购计划：POST /api/purchase/plan
    - 字段：category、item、quantity、purpose、applicant、plan_date
  - 编辑采购计划：PUT /api/purchase/plan
    - 字段：id、category、item、quantity、purpose、applicant、plan_date
  - 删除采购计划：DELETE /api/purchase/plan/{id}
  - 审批采购计划：POST /api/purchase/plan/approve
    - 字段：plan_id、approver、opinion、status
  - 导出采购计划：GET /api/purchase/plan/export
- **字段说明**：
  - id：计划ID（数字）
  - category：类别（字符串）
  - item：物品（字符串）
  - quantity：数量（数字）
  - purpose：用途（字符串）
  - applicant：申请人（字符串）
  - plan_date：计划日期（YYYY-MM-DD）
  - approver：审批人（字符串）
  - opinion：审批意见（字符串）
  - status：状态（字符串）
- **数据来源/关联**：
  - 物品字典表、审批流表
- **实际减负**：
  - 采购流程自动化，减少手工台账

### 2.2 仓储管理
- **页面名称**：仓储管理页
- **页面地址**：/purchase/warehouse
- **主要按钮/功能**：
  - 入库（按钮）
  - 出库（按钮）
  - 退货（按钮）
  - 库存查询（按钮）
  - 导出（按钮）
  - 查询（按钮）
- **接口**：
  - 入库：POST /api/purchase/warehouse/in
    - 字段：purchase_no、item、quantity、inbound_person、inbound_date
  - 出库：POST /api/purchase/warehouse/out
    - 字段：receive_no、item、quantity、outbound_person、outbound_date
  - 退货：POST /api/purchase/warehouse/return
    - 字段：return_type、item、quantity、operator、return_date
  - 查询库存：GET /api/purchase/warehouse/stock
    - 参数：item、category、pageNum、pageSize
  - 导出库存：GET /api/purchase/warehouse/stock/export
- **字段说明**：
  - purchase_no：采购单号（字符串）
  - item：物品（字符串）
  - quantity：数量（数字）
  - inbound_person：入库人（字符串）
  - inbound_date：入库日期（YYYY-MM-DD）
  - receive_no：领用单号（字符串）
  - outbound_person：出库人（字符串）
  - outbound_date：出库日期（YYYY-MM-DD）
  - return_type：退货类型（字符串）
  - operator：操作人（字符串）
  - return_date：退货日期（YYYY-MM-DD）
- **数据来源/关联**：
  - 采购单、领用单、库存表
- **实际减负**：
  - 库存自动变更，减少人工统计

### 2.3 供应商管理
- **页面名称**：供应商管理页
- **页面地址**：/purchase/supplier
- **主要按钮/功能**：
  - 新增供应商（按钮）
  - 编辑供应商（按钮）
  - 禁用供应商（按钮）
  - 查询（按钮）
  - 导出（按钮）
- **接口**：
  - 查询供应商列表：GET /api/purchase/supplier/list
    - 参数：name、status、pageNum、pageSize
  - 新增供应商：POST /api/purchase/supplier
    - 字段：name、contact、phone、address、status
  - 编辑供应商：PUT /api/purchase/supplier
    - 字段：id、name、contact、phone、address、status
  - 禁用供应商：POST /api/purchase/supplier/disable
    - 字段：supplier_id
  - 导出供应商：GET /api/purchase/supplier/export
- **字段说明**：
  - id：供应商ID（数字）
  - name：名称（字符串）
  - contact：联系人（字符串）
  - phone：联系方式（字符串）
  - address：地址（字符串）
  - status：状态（字符串）
  - supplier_id：供应商ID（数字）
- **数据来源/关联**：
  - 供应商表
- **实际减负**：
  - 供应商信息集中管理，便于采购对接

### 2.4 产品目录
- **页面名称**：产品目录管理页
- **页面地址**：/purchase/product
- **主要按钮/功能**：
  - 新增产品（按钮）
  - 编辑产品（按钮）
  - 查询（按钮）
  - 导出（按钮）
- **接口**：
  - 查询产品列表：GET /api/purchase/product/list
    - 参数：type、model、pageNum、pageSize
  - 新增产品：POST /api/purchase/product
    - 字段：type、model、quantity、unit
  - 编辑产品：PUT /api/purchase/product
    - 字段：id、type、model、quantity、unit
  - 导出产品：GET /api/purchase/product/export
- **字段说明**：
  - id：产品ID（数字）
  - type：类型（字符串）
  - model：型号（字符串）
  - quantity：数量（数字）
  - unit：单位（字符串）
- **数据来源/关联**：
  - 产品表
- **实际减负**：
  - 产品信息标准化，便于采购和库存管理

---

## 3. 财务资金部

### 3.1 采购支出
- **页面名称**：采购支出管理页
- **页面地址**：/finance/purchase
- **主要按钮/功能**：
  - 审核（按钮）
  - 导出（按钮）
  - 查询（按钮）
- **接口**：
  - 查询采购支出列表：GET /api/finance/purchase/list
    - 参数：purchase_no、supplier、dateStart、dateEnd、status、pageNum、pageSize
  - 审核采购支出：POST /api/finance/purchase/approve
    - 字段：expense_id、approver、opinion、status
  - 导出采购支出：GET /api/finance/purchase/export
- **字段说明**：
  - expense_id：支出ID（数字）
  - purchase_no：采购单号（字符串）
  - supplier：供应商（字符串）
  - amount：金额（数字）
  - date：支出日期（YYYY-MM-DD）
  - approver：审核人（字符串）
  - opinion：审核意见（字符串）
  - status：状态（字符串）
- **数据来源/关联**：
  - 采购单、财务支出表
- **实际减负**：
  - 支出自动统计，减少人工核算

### 3.2 货款收入
- **页面名称**：货款收入管理页
- **页面地址**：/finance/income
- **主要按钮/功能**：
  - 新增收款（按钮）
  - 查询（按钮）
  - 导出（按钮）
- **接口**：
  - 新增收款记录：POST /api/finance/income/record
    - 字段：order_no、customer、amount、receive_time、account_period
  - 查询收款记录：GET /api/finance/income/list
    - 参数：order_no、customer、dateStart、dateEnd、pageNum、pageSize
  - 导出收款记录：GET /api/finance/income/export
- **字段说明**：
  - id：收款ID（数字）
  - order_no：订单号（字符串）
  - customer：客户（字符串）
  - amount：金额（数字）
  - receive_time：收款时间（YYYY-MM-DD）
  - account_period：账期（字符串）
- **数据来源/关联**：
  - 订单表、客户表、收款表
- **实际减负**：
  - 收款、欠款自动统计，账期提醒

### 3.3 薪资统计
- **页面名称**：薪资统计管理页
- **页面地址**：/finance/salary
- **主要按钮/功能**：
  - 查询（按钮）
  - 导出（按钮）
- **接口**：
  - 查询薪资统计：GET /api/finance/salary/list
    - 参数：employee_id、dateStart、dateEnd、pageNum、pageSize
  - 导出薪资统计：GET /api/finance/salary/export
- **字段说明**：
  - id：薪资ID（数字）
  - employee_id：员工ID（数字）
  - name：姓名（字符串）
  - amount：金额（数字）
  - date：日期（YYYY-MM-DD）
- **数据来源/关联**：
  - 员工表、薪资表
- **实际减负**：
  - 薪资自动汇总，减少人工统计

### 3.4 薪资设置
- **页面名称**：薪资设置管理页
- **页面地址**：/finance/salarySetting
- **主要按钮/功能**：
  - 批量设置（按钮）
  - 审批（按钮）
  - 查询（按钮）
  - 导出（按钮）
- **接口**：
  - 批量设置薪资：POST /api/finance/salary/setting
    - 字段：employee_ids、amount、reason
  - 审批薪资设置：POST /api/finance/salary/setting/approve
    - 字段：setting_id、approver、opinion、status
  - 查询薪资设置：GET /api/finance/salary/setting/list
    - 参数：employee_id、dateStart、dateEnd、status、pageNum、pageSize
  - 导出薪资设置：GET /api/finance/salary/setting/export
- **字段说明**：
  - setting_id：设置ID（数字）
  - employee_ids：员工ID数组（数组）
  - amount：金额（数字）
  - reason：原因（字符串）
  - approver：审批人（字符串）
  - opinion：审批意见（字符串）
  - status：状态（字符串）
- **数据来源/关联**：
  - 员工表、薪资表
- **实际减负**：
  - 批量调整，审批流自动化

### 3.5 成本核算
- **页面名称**：成本核算管理页
- **页面地址**：/finance/cost
- **主要按钮/功能**：
  - 统计（按钮）
  - 图表展示（按钮）
  - 查询（按钮）
  - 导出（按钮）
- **接口**：
  - 查询成本核算：GET /api/finance/cost/list
    - 参数：product、dateStart、dateEnd、pageNum、pageSize
  - 导出成本核算：GET /api/finance/cost/export
- **字段说明**：
  - id：成本ID（数字）
  - product：产品（字符串）
  - amount：金额（数字）
  - date：日期（YYYY-MM-DD）
- **数据来源/关联**：
  - 产品表、采购表、薪资表
- **实际减负**：
  - 自动生成成本报表，便于决策

### 3.6 财务报销
- **页面名称**：报销管理页
- **页面地址**：/finance/reimburse
- **主要按钮/功能**：
  - 新增报销（按钮）
  - 审批（按钮）
  - 结算（按钮）
  - 查询（按钮）
  - 导出（按钮）
- **接口**：
  - 新增报销：POST /api/finance/reimburse/apply
    - 字段：applicant、amount、reason、voucher
  - 审批报销：POST /api/finance/reimburse/approve
    - 字段：reimburse_id、approver、opinion、status
  - 结算报销：POST /api/finance/reimburse/pay
    - 字段：reimburse_id、pay_person、amount
  - 查询报销：GET /api/finance/reimburse/list
    - 参数：applicant、dateStart、dateEnd、status、pageNum、pageSize
  - 导出报销：GET /api/finance/reimburse/export
- **字段说明**：
  - reimburse_id：报销ID（数字）
  - applicant：申请人（字符串）
  - amount：金额（数字）
  - reason：原因（字符串）
  - voucher：凭证（url）
  - approver：审批人（字符串）
  - opinion：审批意见（字符串）
  - status：状态（字符串）
  - pay_person：结算人（字符串）
- **数据来源/关联**：
  - 报销表、员工表、财务表
- **实际减负**：
  - 报销流程自动化，减少纸质单据

---

## 4. 生产制造部

### 4.1 组装车间
#### 主页
- 组装车间主页用于功能导航，进入各业务页面。
- **页面名称**：组装车间主页
- **页面地址**：/production/assembly/index
- **主要按钮/功能**：功能图标导航、进入各功能页面
- **接口**：无（仅导航）
- **字段说明**：无
- **数据来源/关联**：无
- **实际减负**：统一入口，便于操作

#### 产量日报
- 记录每日组装产量，便于绩效考核。
- **页面名称**：组装车间产量日报页
- **页面地址**：/production/assembly/output
- **主要按钮/功能**：新增日报、查询、导出
- **接口**：
  - 新增日报：POST /api/assembly/output
    - 字段：date、product_model、output、operator
  - 查询日报：GET /api/assembly/output/list
    - 参数：dateStart、dateEnd、product_model、pageNum、pageSize
  - 导出日报：GET /api/assembly/output/export
- **字段说明**：
  - date：日期（YYYY-MM-DD）
  - product_model：产品型号（字符串）
  - output：产量（数字）
  - operator：操作人（字符串）
- **数据来源/关联**：产品表、员工表
- **实际减负**：自动统计产量，便于绩效考核

#### 奖惩记录
- 记录员工奖惩，自动同步薪资。
- **页面名称**：组装车间奖惩记录页
- **页面地址**：/production/assembly/rewardpunish
- **主要按钮/功能**：新增奖惩、审批、查询、导出
- **接口**：
  - 新增奖惩：POST /api/assembly/rewardpunish
    - 字段：employee_id、type、amount、reason、date
  - 审批奖惩：POST /api/assembly/rewardpunish/approve
    - 字段：rewardpunish_id、approver、opinion、status
  - 查询奖惩：GET /api/assembly/rewardpunish/list
    - 参数：employee_id、type、dateStart、dateEnd、status、pageNum、pageSize
  - 导出奖惩：GET /api/assembly/rewardpunish/export
- **字段说明**：
  - rewardpunish_id：奖惩ID（数字）
  - employee_id：员工ID（数字）
  - type：类型（奖励/惩罚，字符串）
  - amount：金额（数字）
  - reason：原因（字符串）
  - date：时间（YYYY-MM-DD）
  - approver：审批人（字符串）
  - opinion：审批意见（字符串）
  - status：状态（字符串）
- **数据来源/关联**：员工表、审批流表、薪资表
- **实际减负**：奖惩自动同步薪资，减少人工统计

#### 员工产量
- 记录员工个人产量，自动计算工资。
- **页面名称**：组装车间员工产量页
- **页面地址**：/production/assembly/employeeOutput
- **主要按钮/功能**：新增产量、查询、导出
- **接口**：
  - 新增产量：POST /api/assembly/employee/output
    - 字段：employee_id、date、output、product_model
  - 查询产量：GET /api/assembly/employee/output/list
    - 参数：employee_id、dateStart、dateEnd、product_model、pageNum、pageSize
  - 导出产量：GET /api/assembly/employee/output/export
- **字段说明**：
  - employee_id：员工ID（数字）
  - date：日期（YYYY-MM-DD）
  - output：产量（数字）
  - product_model：产品型号（字符串）
- **数据来源/关联**：员工表、产品表
- **实际减负**：自动计算工资，提升效率

#### 绩效方案
- 维护绩效方案，自动关联工资。
- **页面名称**：组装车间绩效方案页
- **页面地址**：/production/assembly/performance
- **主要按钮/功能**：新增方案、编辑、查询、导出
- **接口**：
  - 新增方案：POST /api/assembly/performance
    - 字段：process_node、fee、product_model
  - 查询方案：GET /api/assembly/performance/list
    - 参数：product_model、process_node、pageNum、pageSize
  - 导出方案：GET /api/assembly/performance/export
- **字段说明**：
  - performance_id：方案ID（数字）
  - process_node：工序节点（字符串）
  - fee：费用（数字）
  - product_model：适用产品（字符串）
- **数据来源/关联**：产品表、工序表
- **实际减负**：绩效自动关联工资，减少手工核算

#### 内包提点
- 维护产品提点比例，自动计入工资。
- **页面名称**：组装车间内包提点页
- **页面地址**：/production/assembly/bonus
- **主要按钮/功能**：新增提点、查询、导出
- **接口**：
  - 新增提点：POST /api/assembly/bonus
    - 字段：product_model、bonus_rate
  - 查询提点：GET /api/assembly/bonus/list
    - 参数：product_model、pageNum、pageSize
  - 导出提点：GET /api/assembly/bonus/export
- **字段说明**：
  - bonus_id：提点ID（数字）
  - product_model：产品型号（字符串）
  - bonus_rate：提点比例（百分比/数字）
- **数据来源/关联**：产品表
- **实际减负**：提点自动计入工资

#### 不良记录
- 记录不良品，便于质量改进。
- **页面名称**：组装车间不良记录页
- **页面地址**：/production/assembly/defect
- **主要按钮/功能**：新增不良、查询、统计、导出
- **接口**：
  - 新增不良：POST /api/assembly/defect
    - 字段：date、product、defect_type、quantity、reason、measure
  - 查询不良：GET /api/assembly/defect/list
    - 参数：dateStart、dateEnd、product、defect_type、pageNum、pageSize
  - 导出不良：GET /api/assembly/defect/export
- **字段说明**：
  - defect_id：不良ID（数字）
  - date：日期（YYYY-MM-DD）
  - product：产品（字符串）
  - defect_type：问题类型（字符串）
  - quantity：数量（数字）
  - reason：原因（字符串）
  - measure：处理措施（字符串）
- **数据来源/关联**：产品表、员工表
- **实际减负**：不良自动统计，便于质量改进

#### 每日UDI
- 记录每日UDI，便于追溯。
- **页面名称**：组装车间UDI记录页
- **页面地址**：/production/assembly/udi
- **主要按钮/功能**：新增UDI、查询、导出
- **接口**：
  - 新增UDI：POST /api/assembly/udi
    - 字段：date、batch_no、udi、package_info、operator
  - 查询UDI：GET /api/assembly/udi/list
    - 参数：dateStart、dateEnd、batch_no、udi、pageNum、pageSize
  - 导出UDI：GET /api/assembly/udi/export
- **字段说明**：
  - udi_id：UDI记录ID（数字）
  - date：日期（YYYY-MM-DD）
  - batch_no：批号（字符串）
  - udi：UDI（字符串）
  - package_info：包装信息（字符串）
  - operator：操作人（字符串）
- **数据来源/关联**：产品表、批号表
- **实际减负**：UDI追溯自动化

#### 批号记录
- 记录批号，便于追溯。
- **页面名称**：组装车间批号记录页
- **页面地址**：/production/assembly/batch
- **主要按钮/功能**：新增批号、查询、导出
- **接口**：
  - 新增批号：POST /api/assembly/batch
    - 字段：batch_no、quantity、date、operator
  - 查询批号：GET /api/assembly/batch/list
    - 参数：batch_no、dateStart、dateEnd、pageNum、pageSize
  - 导出批号：GET /api/assembly/batch/export
- **字段说明**：
  - batch_id：批号ID（数字）
  - batch_no：批号（字符串）
  - quantity：数量（数字）
  - date：时间（YYYY-MM-DD）
  - operator：操作人（字符串）
- **数据来源/关联**：批号表、员工表
- **实际减负**：批号追溯自动化

#### 物料记录
- 记录物料流转，自动统计。
- **页面名称**：组装车间物料记录页
- **页面地址**：/production/assembly/material
- **主要按钮/功能**：新增物料、查询、导出
- **接口**：
  - 新增物料：POST /api/assembly/material
    - 字段：material_name、type、quantity、operation_type、date、operator
  - 查询物料：GET /api/assembly/material/list
    - 参数：material_name、type、dateStart、dateEnd、operation_type、pageNum、pageSize
  - 导出物料：GET /api/assembly/material/export
- **字段说明**：
  - material_id：物料ID（数字）
  - material_name：物料名称（字符串）
  - type：类型（字符串）
  - quantity：数量（数字）
  - operation_type：操作类型（入库/出库/退库等，字符串）
  - date：时间（YYYY-MM-DD）
  - operator：操作人（字符串）
- **数据来源/关联**：物料表、员工表
- **实际减负**：物料流转自动统计

#### 制令单记录
- 记录制令单，进度自动同步。
- **页面名称**：组装车间制令单记录页
- **页面地址**：/production/assembly/mo
- **主要按钮/功能**：新增制令单、查询、导出
- **接口**：
  - 新增制令单：POST /api/assembly/mo
    - 字段：mo_no、product、drawing、weight、batch_no、progress、status
  - 查询制令单：GET /api/assembly/mo/list
    - 参数：mo_no、product、batch_no、status、pageNum、pageSize
  - 导出制令单：GET /api/assembly/mo/export
- **字段说明**：
  - mo_id：制令单ID（数字）
  - mo_no：制令单号（字符串）
  - product：产品（字符串）
  - drawing：图纸（url/字符串）
  - weight：克重（数字）
  - batch_no：批号（字符串）
  - progress：进度（字符串）
  - status：状态（字符串）
- **数据来源/关联**：制令单表、产品表
- **实际减负**：制令单进度自动同步

#### 文件夹
- 文件集中归档，便于查找和共享。
- **页面名称**：组装车间文件夹管理页
- **页面地址**：/production/assembly/file
- **主要按钮/功能**：上传文件、分类、下载、查询、导出
- **接口**：
  - 上传文件：POST /api/assembly/file/upload
    - 字段：file_name、type、category、attachment
  - 查询文件：GET /api/assembly/file/list
    - 参数：file_name、category、dateStart、dateEnd、pageNum、pageSize
  - 导出文件：GET /api/assembly/file/export
- **字段说明**：
  - file_id：文件ID（数字）
  - file_name：文件名（字符串）
  - type：类型（字符串）
  - category：分类（字符串）
  - attachment：附件（url）
- **数据来源/关联**：文件服务器
- **实际减负**：文件集中归档，便于查找和共享

### 4.2 自动化车间
#### 主页
- 自动化车间主页用于功能导航，进入各业务页面。
- **页面名称**：自动化车间主页
- **页面地址**：/production/automation/index
- **主要按钮/功能**：功能图标导航、进入各功能页面
- **接口**：无（仅导航）
- **字段说明**：无
- **数据来源/关联**：无
- **实际减负**：统一入口，便于操作

#### 产量日报
- 记录每日自动化产量，便于绩效考核。
- **页面名称**：自动化车间产量日报页
- **页面地址**：/production/automation/output
- **主要按钮/功能**：新增日报、查询、导出
- **接口**：
  - 新增日报：POST /api/automation/output
    - 字段：date、product_model、output、operator
  - 查询日报：GET /api/automation/output/list
    - 参数：dateStart、dateEnd、product_model、pageNum、pageSize
  - 导出日报：GET /api/automation/output/export
- **字段说明**：
  - date：日期（YYYY-MM-DD）
  - product_model：产品型号（字符串）
  - output：产量（数字）
  - operator：操作人（字符串）
- **数据来源/关联**：产品表、员工表
- **实际减负**：自动统计产量，便于绩效考核

#### 奖惩记录
- 记录员工奖惩，自动同步薪资。
- **页面名称**：自动化车间奖惩记录页
- **页面地址**：/production/automation/rewardpunish
- **主要按钮/功能**：新增奖惩、审批、查询、导出
- **接口**：
  - 新增奖惩：POST /api/automation/rewardpunish
    - 字段：employee_id、type、amount、reason、date
  - 审批奖惩：POST /api/automation/rewardpunish/approve
    - 字段：rewardpunish_id、approver、opinion、status
  - 查询奖惩：GET /api/automation/rewardpunish/list
    - 参数：employee_id、type、dateStart、dateEnd、status、pageNum、pageSize
  - 导出奖惩：GET /api/automation/rewardpunish/export
- **字段说明**：
  - rewardpunish_id：奖惩ID（数字）
  - employee_id：员工ID（数字）
  - type：类型（奖励/惩罚，字符串）
  - amount：金额（数字）
  - reason：原因（字符串）
  - date：时间（YYYY-MM-DD）
  - approver：审批人（字符串）
  - opinion：审批意见（字符串）
  - status：状态（字符串）
- **数据来源/关联**：员工表、审批流表、薪资表
- **实际减负**：奖惩自动同步薪资，减少人工统计

#### 员工产量
- 记录员工个人产量，自动计算工资。
- **页面名称**：自动化车间员工产量页
- **页面地址**：/production/automation/employeeOutput
- **主要按钮/功能**：新增产量、查询、导出
- **接口**：
  - 新增产量：POST /api/automation/employee/output
    - 字段：employee_id、date、output、product_model
  - 查询产量：GET /api/automation/employee/output/list
    - 参数：employee_id、dateStart、dateEnd、product_model、pageNum、pageSize
  - 导出产量：GET /api/automation/employee/output/export
- **字段说明**：
  - employee_id：员工ID（数字）
  - date：日期（YYYY-MM-DD）
  - output：产量（数字）
  - product_model：产品型号（字符串）
- **数据来源/关联**：员工表、产品表
- **实际减负**：自动计算工资，提升效率

#### 绩效方案
- 维护绩效方案，自动关联工资。
- **页面名称**：自动化车间绩效方案页
- **页面地址**：/production/automation/performance
- **主要按钮/功能**：新增方案、编辑、查询、导出
- **接口**：
  - 新增方案：POST /api/automation/performance
    - 字段：process_node、fee、product_model
  - 查询方案：GET /api/automation/performance/list
    - 参数：product_model、process_node、pageNum、pageSize
  - 导出方案：GET /api/automation/performance/export
- **字段说明**：
  - performance_id：方案ID（数字）
  - process_node：工序节点（字符串）
  - fee：费用（数字）
  - product_model：适用产品（字符串）
- **数据来源/关联**：产品表、工序表
- **实际减负**：绩效自动关联工资，减少手工核算

#### 内包提点
- 维护产品提点比例，自动计入工资。
- **页面名称**：自动化车间内包提点页
- **页面地址**：/production/automation/bonus
- **主要按钮/功能**：新增提点、查询、导出
- **接口**：
  - 新增提点：POST /api/automation/bonus
    - 字段：product_model、bonus_rate
  - 查询提点：GET /api/automation/bonus/list
    - 参数：product_model、pageNum、pageSize
  - 导出提点：GET /api/automation/bonus/export
- **字段说明**：
  - bonus_id：提点ID（数字）
  - product_model：产品型号（字符串）
  - bonus_rate：提点比例（百分比/数字）
- **数据来源/关联**：产品表
- **实际减负**：提点自动计入工资

#### 不良记录
- 记录不良品，便于质量改进。
- **页面名称**：自动化车间不良记录页
- **页面地址**：/production/automation/defect
- **主要按钮/功能**：新增不良、查询、统计、导出
- **接口**：
  - 新增不良：POST /api/automation/defect
    - 字段：date、product、defect_type、quantity、reason、measure
  - 查询不良：GET /api/automation/defect/list
    - 参数：dateStart、dateEnd、product、defect_type、pageNum、pageSize
  - 导出不良：GET /api/automation/defect/export
- **字段说明**：
  - defect_id：不良ID（数字）
  - date：日期（YYYY-MM-DD）
  - product：产品（字符串）
  - defect_type：问题类型（字符串）
  - quantity：数量（数字）
  - reason：原因（字符串）
  - measure：处理措施（字符串）
- **数据来源/关联**：产品表、员工表
- **实际减负**：不良自动统计，便于质量改进

#### 每日UDI
- 记录每日UDI，便于追溯。
- **页面名称**：自动化车间UDI记录页
- **页面地址**：/production/automation/udi
- **主要按钮/功能**：新增UDI、查询、导出
- **接口**：
  - 新增UDI：POST /api/automation/udi
    - 字段：date、batch_no、udi、package_info、operator
  - 查询UDI：GET /api/automation/udi/list
    - 参数：dateStart、dateEnd、batch_no、udi、pageNum、pageSize
  - 导出UDI：GET /api/automation/udi/export
- **字段说明**：
  - udi_id：UDI记录ID（数字）
  - date：日期（YYYY-MM-DD）
  - batch_no：批号（字符串）
  - udi：UDI（字符串）
  - package_info：包装信息（字符串）
  - operator：操作人（字符串）
- **数据来源/关联**：产品表、批号表
- **实际减负**：UDI追溯自动化

#### 批号记录
- 记录批号，便于追溯。
- **页面名称**：自动化车间批号记录页
- **页面地址**：/production/automation/batch
- **主要按钮/功能**：新增批号、查询、导出
- **接口**：
  - 新增批号：POST /api/automation/batch
    - 字段：batch_no、quantity、date、operator
  - 查询批号：GET /api/automation/batch/list
    - 参数：batch_no、dateStart、dateEnd、pageNum、pageSize
  - 导出批号：GET /api/automation/batch/export
- **字段说明**：
  - batch_id：批号ID（数字）
  - batch_no：批号（字符串）
  - quantity：数量（数字）
  - date：时间（YYYY-MM-DD）
  - operator：操作人（字符串）
- **数据来源/关联**：批号表、员工表
- **实际减负**：批号追溯自动化

#### 物料记录
- 记录物料流转，自动统计。
- **页面名称**：自动化车间物料记录页
- **页面地址**：/production/automation/material
- **主要按钮/功能**：新增物料、查询、导出
- **接口**：
  - 新增物料：POST /api/automation/material
    - 字段：material_name、type、quantity、operation_type、date、operator
  - 查询物料：GET /api/automation/material/list
    - 参数：material_name、type、dateStart、dateEnd、operation_type、pageNum、pageSize
  - 导出物料：GET /api/automation/material/export
- **字段说明**：
  - material_id：物料ID（数字）
  - material_name：物料名称（字符串）
  - type：类型（字符串）
  - quantity：数量（数字）
  - operation_type：操作类型（入库/出库/退库等，字符串）
  - date：时间（YYYY-MM-DD）
  - operator：操作人（字符串）
- **数据来源/关联**：物料表、员工表
- **实际减负**：物料流转自动统计

#### 制令单记录
- 记录制令单，进度自动同步。
- **页面名称**：自动化车间制令单记录页
- **页面地址**：/production/automation/mo
- **主要按钮/功能**：新增制令单、查询、导出
- **接口**：
  - 新增制令单：POST /api/automation/mo
    - 字段：mo_no、product、drawing、weight、batch_no、progress、status
  - 查询制令单：GET /api/automation/mo/list
    - 参数：mo_no、product、batch_no、status、pageNum、pageSize
  - 导出制令单：GET /api/automation/mo/export
- **字段说明**：
  - mo_id：制令单ID（数字）
  - mo_no：制令单号（字符串）
  - product：产品（字符串）
  - drawing：图纸（url/字符串）
  - weight：克重（数字）
  - batch_no：批号（字符串）
  - progress：进度（字符串）
  - status：状态（字符串）
- **数据来源/关联**：制令单表、产品表
- **实际减负**：制令单进度自动同步

#### 文件夹
- 文件集中归档，便于查找和共享。
- **页面名称**：自动化车间文件夹管理页
- **页面地址**：/production/automation/file
- **主要按钮/功能**：上传文件、分类、下载、查询、导出
- **接口**：
  - 上传文件：POST /api/automation/file/upload
    - 字段：file_name、type、category、attachment
  - 查询文件：GET /api/automation/file/list
    - 参数：file_name、category、dateStart、dateEnd、pageNum、pageSize
  - 导出文件：GET /api/automation/file/export
- **字段说明**：
  - file_id：文件ID（数字）
  - file_name：文件名（字符串）
  - type：类型（字符串）
  - category：分类（字符串）
  - attachment：附件（url）
- **数据来源/关联**：文件服务器
- **实际减负**：文件集中归档，便于查找和共享

### 4.3 挤出车间
#### 主页
- 挤出车间主页用于功能导航，进入各业务页面。
- **页面名称**：挤出车间主页
- **页面地址**：/production/extrusion/index
- **主要按钮/功能**：功能图标导航、进入各功能页面
- **接口**：无（仅导航）
- **字段说明**：无
- **数据来源/关联**：无
- **实际减负**：统一入口，便于操作

#### 产量日报
- 记录每日挤出产量，便于绩效考核。
- **页面名称**：挤出车间产量日报页
- **页面地址**：/production/extrusion/output
- **主要按钮/功能**：新增日报、查询、导出
- **接口**：
  - 新增日报：POST /api/extrusion/output
    - 字段：date、product_model、output、operator
  - 查询日报：GET /api/extrusion/output/list
    - 参数：dateStart、dateEnd、product_model、pageNum、pageSize
  - 导出日报：GET /api/extrusion/output/export
- **字段说明**：
  - date：日期（YYYY-MM-DD）
  - product_model：产品型号（字符串）
  - output：产量（数字）
  - operator：操作人（字符串）
- **数据来源/关联**：产品表、员工表
- **实际减负**：自动统计产量，便于绩效考核

#### 奖惩记录
- 记录员工奖惩，自动同步薪资。
- **页面名称**：挤出车间奖惩记录页
- **页面地址**：/production/extrusion/rewardpunish
- **主要按钮/功能**：新增奖惩、审批、查询、导出
- **接口**：
  - 新增奖惩：POST /api/extrusion/rewardpunish
    - 字段：employee_id、type、amount、reason、date
  - 审批奖惩：POST /api/extrusion/rewardpunish/approve
    - 字段：rewardpunish_id、approver、opinion、status
  - 查询奖惩：GET /api/extrusion/rewardpunish/list
    - 参数：employee_id、type、dateStart、dateEnd、status、pageNum、pageSize
  - 导出奖惩：GET /api/extrusion/rewardpunish/export
- **字段说明**：
  - rewardpunish_id：奖惩ID（数字）
  - employee_id：员工ID（数字）
  - type：类型（奖励/惩罚，字符串）
  - amount：金额（数字）
  - reason：原因（字符串）
  - date：时间（YYYY-MM-DD）
  - approver：审批人（字符串）
  - opinion：审批意见（字符串）
  - status：状态（字符串）
- **数据来源/关联**：员工表、审批流表、薪资表
- **实际减负**：奖惩自动同步薪资，减少人工统计

#### 员工产量
- 记录员工个人产量，自动计算工资。
- **页面名称**：挤出车间员工产量页
- **页面地址**：/production/extrusion/employeeOutput
- **主要按钮/功能**：新增产量、查询、导出
- **接口**：
  - 新增产量：POST /api/extrusion/employee/output
    - 字段：employee_id、date、output、product_model
  - 查询产量：GET /api/extrusion/employee/output/list
    - 参数：employee_id、dateStart、dateEnd、product_model、pageNum、pageSize
  - 导出产量：GET /api/extrusion/employee/output/export
- **字段说明**：
  - employee_id：员工ID（数字）
  - date：日期（YYYY-MM-DD）
  - output：产量（数字）
  - product_model：产品型号（字符串）
- **数据来源/关联**：员工表、产品表
- **实际减负**：自动计算工资，提升效率

#### 绩效方案
- 维护绩效方案，自动关联工资。
- **页面名称**：挤出车间绩效方案页
- **页面地址**：/production/extrusion/performance
- **主要按钮/功能**：新增方案、编辑、查询、导出
- **接口**：
  - 新增方案：POST /api/extrusion/performance
    - 字段：process_node、fee、product_model
  - 查询方案：GET /api/extrusion/performance/list
    - 参数：product_model、process_node、pageNum、pageSize
  - 导出方案：GET /api/extrusion/performance/export
- **字段说明**：
  - performance_id：方案ID（数字）
  - process_node：工序节点（字符串）
  - fee：费用（数字）
  - product_model：适用产品（字符串）
- **数据来源/关联**：产品表、工序表
- **实际减负**：绩效自动关联工资，减少手工核算

#### 内包提点
- 维护产品提点比例，自动计入工资。
- **页面名称**：挤出车间内包提点页
- **页面地址**：/production/extrusion/bonus
- **主要按钮/功能**：新增提点、查询、导出
- **接口**：
  - 新增提点：POST /api/extrusion/bonus
    - 字段：product_model、bonus_rate
  - 查询提点：GET /api/extrusion/bonus/list
    - 参数：product_model、pageNum、pageSize
  - 导出提点：GET /api/extrusion/bonus/export
- **字段说明**：
  - bonus_id：提点ID（数字）
  - product_model：产品型号（字符串）
  - bonus_rate：提点比例（百分比/数字）
- **数据来源/关联**：产品表
- **实际减负**：提点自动计入工资

#### 不良记录
- 记录不良品，便于质量改进。
- **页面名称**：挤出车间不良记录页
- **页面地址**：/production/extrusion/defect
- **主要按钮/功能**：新增不良、查询、统计、导出
- **接口**：
  - 新增不良：POST /api/extrusion/defect
    - 字段：date、product、defect_type、quantity、reason、measure
  - 查询不良：GET /api/extrusion/defect/list
    - 参数：dateStart、dateEnd、product、defect_type、pageNum、pageSize
  - 导出不良：GET /api/extrusion/defect/export
- **字段说明**：
  - defect_id：不良ID（数字）
  - date：日期（YYYY-MM-DD）
  - product：产品（字符串）
  - defect_type：问题类型（字符串）
  - quantity：数量（数字）
  - reason：原因（字符串）
  - measure：处理措施（字符串）
- **数据来源/关联**：产品表、员工表
- **实际减负**：不良自动统计，便于质量改进

#### 每日UDI
- 记录每日UDI，便于追溯。
- **页面名称**：挤出车间UDI记录页
- **页面地址**：/production/extrusion/udi
- **主要按钮/功能**：新增UDI、查询、导出
- **接口**：
  - 新增UDI：POST /api/extrusion/udi
    - 字段：date、batch_no、udi、package_info、operator
  - 查询UDI：GET /api/extrusion/udi/list
    - 参数：dateStart、dateEnd、batch_no、udi、pageNum、pageSize
  - 导出UDI：GET /api/extrusion/udi/export
- **字段说明**：
  - udi_id：UDI记录ID（数字）
  - date：日期（YYYY-MM-DD）
  - batch_no：批号（字符串）
  - udi：UDI（字符串）
  - package_info：包装信息（字符串）
  - operator：操作人（字符串）
- **数据来源/关联**：产品表、批号表
- **实际减负**：UDI追溯自动化

#### 批号记录
- 记录批号，便于追溯。
- **页面名称**：挤出车间批号记录页
- **页面地址**：/production/extrusion/batch
- **主要按钮/功能**：新增批号、查询、导出
- **接口**：
  - 新增批号：POST /api/extrusion/batch
    - 字段：batch_no、quantity、date、operator
  - 查询批号：GET /api/extrusion/batch/list
    - 参数：batch_no、dateStart、dateEnd、pageNum、pageSize
  - 导出批号：GET /api/extrusion/batch/export
- **字段说明**：
  - batch_id：批号ID（数字）
  - batch_no：批号（字符串）
  - quantity：数量（数字）
  - date：时间（YYYY-MM-DD）
  - operator：操作人（字符串）
- **数据来源/关联**：批号表、员工表
- **实际减负**：批号追溯自动化

#### 物料记录
- 记录物料流转，自动统计。
- **页面名称**：挤出车间物料记录页
- **页面地址**：/production/extrusion/material
- **主要按钮/功能**：新增物料、查询、导出
- **接口**：
  - 新增物料：POST /api/extrusion/material
    - 字段：material_name、type、quantity、operation_type、date、operator
  - 查询物料：GET /api/extrusion/material/list
    - 参数：material_name、type、dateStart、dateEnd、operation_type、pageNum、pageSize
  - 导出物料：GET /api/extrusion/material/export
- **字段说明**：
  - material_id：物料ID（数字）
  - material_name：物料名称（字符串）
  - type：类型（字符串）
  - quantity：数量（数字）
  - operation_type：操作类型（入库/出库/退库等，字符串）
  - date：时间（YYYY-MM-DD）
  - operator：操作人（字符串）
- **数据来源/关联**：物料表、员工表
- **实际减负**：物料流转自动统计

#### 制令单记录
- 记录制令单，进度自动同步。
- **页面名称**：挤出车间制令单记录页
- **页面地址**：/production/extrusion/mo
- **主要按钮/功能**：新增制令单、查询、导出
- **接口**：
  - 新增制令单：POST /api/extrusion/mo
    - 字段：mo_no、product、drawing、weight、batch_no、progress、status
  - 查询制令单：GET /api/extrusion/mo/list
    - 参数：mo_no、product、batch_no、status、pageNum、pageSize
  - 导出制令单：GET /api/extrusion/mo/export
- **字段说明**：
  - mo_id：制令单ID（数字）
  - mo_no：制令单号（字符串）
  - product：产品（字符串）
  - drawing：图纸（url/字符串）
  - weight：克重（数字）
  - batch_no：批号（字符串）
  - progress：进度（字符串）
  - status：状态（字符串）
- **数据来源/关联**：制令单表、产品表
- **实际减负**：制令单进度自动同步

#### 文件夹
- 文件集中归档，便于查找和共享。
- **页面名称**：挤出车间文件夹管理页
- **页面地址**：/production/extrusion/file
- **主要按钮/功能**：上传文件、分类、下载、查询、导出
- **接口**：
  - 上传文件：POST /api/extrusion/file/upload
    - 字段：file_name、type、category、attachment
  - 查询文件：GET /api/extrusion/file/list
    - 参数：file_name、category、dateStart、dateEnd、pageNum、pageSize
  - 导出文件：GET /api/extrusion/file/export
- **字段说明**：
  - file_id：文件ID（数字）
  - file_name：文件名（字符串）
  - type：类型（字符串）
  - category：分类（字符串）
  - attachment：附件（url）
- **数据来源/关联**：文件服务器
- **实际减负**：文件集中归档，便于查找和共享

### 4.4 硫化车间
#### 主页
- 硫化车间主页用于功能导航，进入各业务页面。
- **页面名称**：硫化车间主页
- **页面地址**：/production/vulcanization/index
- **主要按钮/功能**：功能图标导航、进入各功能页面
- **接口**：无（仅导航）
- **字段说明**：无
- **数据来源/关联**：无
- **实际减负**：统一入口，便于操作

#### 产量日报
- 记录每日硫化产量，便于绩效考核。
- **页面名称**：硫化车间产量日报页
- **页面地址**：/production/vulcanization/output
- **主要按钮/功能**：新增日报、查询、导出
- **接口**：
  - 新增日报：POST /api/vulcanization/output
    - 字段：date、product_model、output、operator
  - 查询日报：GET /api/vulcanization/output/list
    - 参数：dateStart、dateEnd、product_model、pageNum、pageSize
  - 导出日报：GET /api/vulcanization/output/export
- **字段说明**：
  - date：日期（YYYY-MM-DD）
  - product_model：产品型号（字符串）
  - output：产量（数字）
  - operator：操作人（字符串）
- **数据来源/关联**：产品表、员工表
- **实际减负**：自动统计产量，便于绩效考核

#### 奖惩记录
- 记录员工奖惩，自动同步薪资。
- **页面名称**：硫化车间奖惩记录页
- **页面地址**：/production/vulcanization/rewardpunish
- **主要按钮/功能**：新增奖惩、审批、查询、导出
- **接口**：
  - 新增奖惩：POST /api/vulcanization/rewardpunish
    - 字段：employee_id、type、amount、reason、date
  - 审批奖惩：POST /api/vulcanization/rewardpunish/approve
    - 字段：rewardpunish_id、approver、opinion、status
  - 查询奖惩：GET /api/vulcanization/rewardpunish/list
    - 参数：employee_id、type、dateStart、dateEnd、status、pageNum、pageSize
  - 导出奖惩：GET /api/vulcanization/rewardpunish/export
- **字段说明**：
  - rewardpunish_id：奖惩ID（数字）
  - employee_id：员工ID（数字）
  - type：类型（奖励/惩罚，字符串）
  - amount：金额（数字）
  - reason：原因（字符串）
  - date：时间（YYYY-MM-DD）
  - approver：审批人（字符串）
  - opinion：审批意见（字符串）
  - status：状态（字符串）
- **数据来源/关联**：员工表、审批流表、薪资表
- **实际减负**：奖惩自动同步薪资，减少人工统计

#### 员工产量
- 记录员工个人产量，自动计算工资。
- **页面名称**：硫化车间员工产量页
- **页面地址**：/production/vulcanization/employeeOutput
- **主要按钮/功能**：新增产量、查询、导出
- **接口**：
  - 新增产量：POST /api/vulcanization/employee/output
    - 字段：employee_id、date、output、product_model
  - 查询产量：GET /api/vulcanization/employee/output/list
    - 参数：employee_id、dateStart、dateEnd、product_model、pageNum、pageSize
  - 导出产量：GET /api/vulcanization/employee/output/export
- **字段说明**：
  - employee_id：员工ID（数字）
  - date：日期（YYYY-MM-DD）
  - output：产量（数字）
  - product_model：产品型号（字符串）
- **数据来源/关联**：员工表、产品表
- **实际减负**：自动计算工资，提升效率

#### 绩效方案
- 维护绩效方案，自动关联工资。
- **页面名称**：硫化车间绩效方案页
- **页面地址**：/production/vulcanization/performance
- **主要按钮/功能**：新增方案、编辑、查询、导出
- **接口**：
  - 新增方案：POST /api/vulcanization/performance
    - 字段：process_node、fee、product_model
  - 查询方案：GET /api/vulcanization/performance/list
    - 参数：product_model、process_node、pageNum、pageSize
  - 导出方案：GET /api/vulcanization/performance/export
- **字段说明**：
  - performance_id：方案ID（数字）
  - process_node：工序节点（字符串）
  - fee：费用（数字）
  - product_model：适用产品（字符串）
- **数据来源/关联**：产品表、工序表
- **实际减负**：绩效自动关联工资，减少手工核算

#### 内包提点
- 维护产品提点比例，自动计入工资。
- **页面名称**：硫化车间内包提点页
- **页面地址**：/production/vulcanization/bonus
- **主要按钮/功能**：新增提点、查询、导出
- **接口**：
  - 新增提点：POST /api/vulcanization/bonus
    - 字段：product_model、bonus_rate
  - 查询提点：GET /api/vulcanization/bonus/list
    - 参数：product_model、pageNum、pageSize
  - 导出提点：GET /api/vulcanization/bonus/export
- **字段说明**：
  - bonus_id：提点ID（数字）
  - product_model：产品型号（字符串）
  - bonus_rate：提点比例（百分比/数字）
- **数据来源/关联**：产品表
- **实际减负**：提点自动计入工资

#### 不良记录
- 记录不良品，便于质量改进。
- **页面名称**：硫化车间不良记录页
- **页面地址**：/production/vulcanization/defect
- **主要按钮/功能**：新增不良、查询、统计、导出
- **接口**：
  - 新增不良：POST /api/vulcanization/defect
    - 字段：date、product、defect_type、quantity、reason、measure
  - 查询不良：GET /api/vulcanization/defect/list
    - 参数：dateStart、dateEnd、product、defect_type、pageNum、pageSize
  - 导出不良：GET /api/vulcanization/defect/export
- **字段说明**：
  - defect_id：不良ID（数字）
  - date：日期（YYYY-MM-DD）
  - product：产品（字符串）
  - defect_type：问题类型（字符串）
  - quantity：数量（数字）
  - reason：原因（字符串）
  - measure：处理措施（字符串）
- **数据来源/关联**：产品表、员工表
- **实际减负**：不良自动统计，便于质量改进

#### 每日UDI
- 记录每日UDI，便于追溯。
- **页面名称**：硫化车间UDI记录页
- **页面地址**：/production/vulcanization/udi
- **主要按钮/功能**：新增UDI、查询、导出
- **接口**：
  - 新增UDI：POST /api/vulcanization/udi
    - 字段：date、batch_no、udi、package_info、operator
  - 查询UDI：GET /api/vulcanization/udi/list
    - 参数：dateStart、dateEnd、batch_no、udi、pageNum、pageSize
  - 导出UDI：GET /api/vulcanization/udi/export
- **字段说明**：
  - udi_id：UDI记录ID（数字）
  - date：日期（YYYY-MM-DD）
  - batch_no：批号（字符串）
  - udi：UDI（字符串）
  - package_info：包装信息（字符串）
  - operator：操作人（字符串）
- **数据来源/关联**：产品表、批号表
- **实际减负**：UDI追溯自动化

#### 批号记录
- 记录批号，便于追溯。
- **页面名称**：硫化车间批号记录页
- **页面地址**：/production/vulcanization/batch
- **主要按钮/功能**：新增批号、查询、导出
- **接口**：
  - 新增批号：POST /api/vulcanization/batch
    - 字段：batch_no、quantity、date、operator
  - 查询批号：GET /api/vulcanization/batch/list
    - 参数：batch_no、dateStart、dateEnd、pageNum、pageSize
  - 导出批号：GET /api/vulcanization/batch/export
- **字段说明**：
  - batch_id：批号ID（数字）
  - batch_no：批号（字符串）
  - quantity：数量（数字）
  - date：时间（YYYY-MM-DD）
  - operator：操作人（字符串）
- **数据来源/关联**：批号表、员工表
- **实际减负**：批号追溯自动化

#### 物料记录
- 记录物料流转，自动统计。
- **页面名称**：硫化车间物料记录页
- **页面地址**：/production/vulcanization/material
- **主要按钮/功能**：新增物料、查询、导出
- **接口**：
  - 新增物料：POST /api/vulcanization/material
    - 字段：material_name、type、quantity、operation_type、date、operator
  - 查询物料：GET /api/vulcanization/material/list
    - 参数：material_name、type、dateStart、dateEnd、operation_type、pageNum、pageSize
  - 导出物料：GET /api/vulcanization/material/export
- **字段说明**：
  - material_id：物料ID（数字）
  - material_name：物料名称（字符串）
  - type：类型（字符串）
  - quantity：数量（数字）
  - operation_type：操作类型（入库/出库/退库等，字符串）
  - date：时间（YYYY-MM-DD）
  - operator：操作人（字符串）
- **数据来源/关联**：物料表、员工表
- **实际减负**：物料流转自动统计

#### 制令单记录
- 记录制令单，进度自动同步。
- **页面名称**：硫化车间制令单记录页
- **页面地址**：/production/vulcanization/mo
- **主要按钮/功能**：新增制令单、查询、导出
- **接口**：
  - 新增制令单：POST /api/vulcanization/mo
    - 字段：mo_no、product、drawing、weight、batch_no、progress、status
  - 查询制令单：GET /api/vulcanization/mo/list
    - 参数：mo_no、product、batch_no、status、pageNum、pageSize
  - 导出制令单：GET /api/vulcanization/mo/export
- **字段说明**：
  - mo_id：制令单ID（数字）
  - mo_no：制令单号（字符串）
  - product：产品（字符串）
  - drawing：图纸（url/字符串）
  - weight：克重（数字）
  - batch_no：批号（字符串）
  - progress：进度（字符串）
  - status：状态（字符串）
- **数据来源/关联**：制令单表、产品表
- **实际减负**：制令单进度自动同步

#### 文件夹
- 文件集中归档，便于查找和共享。
- **页面名称**：硫化车间文件夹管理页
- **页面地址**：/production/vulcanization/file
- **主要按钮/功能**：上传文件、分类、下载、查询、导出
- **接口**：
  - 上传文件：POST /api/vulcanization/file/upload
    - 字段：file_name、type、category、attachment
  - 查询文件：GET /api/vulcanization/file/list
    - 参数：file_name、category、dateStart、dateEnd、pageNum、pageSize
  - 导出文件：GET /api/vulcanization/file/export
- **字段说明**：
  - file_id：文件ID（数字）
  - file_name：文件名（字符串）
  - type：类型（字符串）
  - category：分类（字符串）
  - attachment：附件（url）
- **数据来源/关联**：文件服务器
- **实际减负**：文件集中归档，便于查找和共享

### 4.5 灭菌车间
#### 主页
- 灭菌车间主页用于功能导航，进入各业务页面。
- **页面名称**：灭菌车间主页
- **页面地址**：/production/sterilization/index
- **主要按钮/功能**：功能图标导航、进入各功能页面
- **接口**：无（仅导航）
- **字段说明**：无
- **数据来源/关联**：无
- **实际减负**：统一入口，便于操作

#### 产量日报
- 记录每日灭菌产量，便于绩效考核。
- **页面名称**：灭菌车间产量日报页
- **页面地址**：/production/sterilization/output
- **主要按钮/功能**：新增日报、查询、导出
- **接口**：
  - 新增日报：POST /api/sterilization/output
    - 字段：date、product_model、output、operator
  - 查询日报：GET /api/sterilization/output/list
    - 参数：dateStart、dateEnd、product_model、pageNum、pageSize
  - 导出日报：GET /api/sterilization/output/export
- **字段说明**：
  - date：日期（YYYY-MM-DD）
  - product_model：产品型号（字符串）
  - output：产量（数字）
  - operator：操作人（字符串）
- **数据来源/关联**：产品表、员工表
- **实际减负**：自动统计产量，便于绩效考核

#### 奖惩记录
- 记录员工奖惩，自动同步薪资。
- **页面名称**：灭菌车间奖惩记录页
- **页面地址**：/production/sterilization/rewardpunish
- **主要按钮/功能**：新增奖惩、审批、查询、导出
- **接口**：
  - 新增奖惩：POST /api/sterilization/rewardpunish
    - 字段：employee_id、type、amount、reason、date
  - 审批奖惩：POST /api/sterilization/rewardpunish/approve
    - 字段：rewardpunish_id、approver、opinion、status
  - 查询奖惩：GET /api/sterilization/rewardpunish/list
    - 参数：employee_id、type、dateStart、dateEnd、status、pageNum、pageSize
  - 导出奖惩：GET /api/sterilization/rewardpunish/export
- **字段说明**：
  - rewardpunish_id：奖惩ID（数字）
  - employee_id：员工ID（数字）
  - type：类型（奖励/惩罚，字符串）
  - amount：金额（数字）
  - reason：原因（字符串）
  - date：时间（YYYY-MM-DD）
  - approver：审批人（字符串）
  - opinion：审批意见（字符串）
  - status：状态（字符串）
- **数据来源/关联**：员工表、审批流表、薪资表
- **实际减负**：奖惩自动同步薪资，减少人工统计

#### 员工产量
- 记录员工个人产量，自动计算工资。
- **页面名称**：灭菌车间员工产量页
- **页面地址**：/production/sterilization/employeeOutput
- **主要按钮/功能**：新增产量、查询、导出
- **接口**：
  - 新增产量：POST /api/sterilization/employee/output
    - 字段：employee_id、date、output、product_model
  - 查询产量：GET /api/sterilization/employee/output/list
    - 参数：employee_id、dateStart、dateEnd、product_model、pageNum、pageSize
  - 导出产量：GET /api/sterilization/employee/output/export
- **字段说明**：
  - employee_id：员工ID（数字）
  - date：日期（YYYY-MM-DD）
  - output：产量（数字）
  - product_model：产品型号（字符串）
- **数据来源/关联**：员工表、产品表
- **实际减负**：自动计算工资，提升效率

#### 绩效方案
- 维护绩效方案，自动关联工资。
- **页面名称**：灭菌车间绩效方案页
- **页面地址**：/production/sterilization/performance
- **主要按钮/功能**：新增方案、编辑、查询、导出
- **接口**：
  - 新增方案：POST /api/sterilization/performance
    - 字段：process_node、fee、product_model
  - 查询方案：GET /api/sterilization/performance/list
    - 参数：product_model、process_node、pageNum、pageSize
  - 导出方案：GET /api/sterilization/performance/export
- **字段说明**：
  - performance_id：方案ID（数字）
  - process_node：工序节点（字符串）
  - fee：费用（数字）
  - product_model：适用产品（字符串）
- **数据来源/关联**：产品表、工序表
- **实际减负**：绩效自动关联工资，减少手工核算

#### 内包提点
- 维护产品提点比例，自动计入工资。
- **页面名称**：灭菌车间内包提点页
- **页面地址**：/production/sterilization/bonus
- **主要按钮/功能**：新增提点、查询、导出
- **接口**：
  - 新增提点：POST /api/sterilization/bonus
    - 字段：product_model、bonus_rate
  - 查询提点：GET /api/sterilization/bonus/list
    - 参数：product_model、pageNum、pageSize
  - 导出提点：GET /api/sterilization/bonus/export
- **字段说明**：
  - bonus_id：提点ID（数字）
  - product_model：产品型号（字符串）
  - bonus_rate：提点比例（百分比/数字）
- **数据来源/关联**：产品表
- **实际减负**：提点自动计入工资

#### 不良记录
- 记录不良品，便于质量改进。
- **页面名称**：灭菌车间不良记录页
- **页面地址**：/production/sterilization/defect
- **主要按钮/功能**：新增不良、查询、统计、导出
- **接口**：
  - 新增不良：POST /api/sterilization/defect
    - 字段：date、product、defect_type、quantity、reason、measure
  - 查询不良：GET /api/sterilization/defect/list
    - 参数：dateStart、dateEnd、product、defect_type、pageNum、pageSize
  - 导出不良：GET /api/sterilization/defect/export
- **字段说明**：
  - defect_id：不良ID（数字）
  - date：日期（YYYY-MM-DD）
  - product：产品（字符串）
  - defect_type：问题类型（字符串）
  - quantity：数量（数字）
  - reason：原因（字符串）
  - measure：处理措施（字符串）
- **数据来源/关联**：产品表、员工表
- **实际减负**：不良自动统计，便于质量改进

#### 每日UDI
- 记录每日UDI，便于追溯。
- **页面名称**：灭菌车间UDI记录页
- **页面地址**：/production/sterilization/udi
- **主要按钮/功能**：新增UDI、查询、导出
- **接口**：
  - 新增UDI：POST /api/sterilization/udi
    - 字段：date、batch_no、udi、package_info、operator
  - 查询UDI：GET /api/sterilization/udi/list
    - 参数：dateStart、dateEnd、batch_no、udi、pageNum、pageSize
  - 导出UDI：GET /api/sterilization/udi/export
- **字段说明**：
  - udi_id：UDI记录ID（数字）
  - date：日期（YYYY-MM-DD）
  - batch_no：批号（字符串）
  - udi：UDI（字符串）
  - package_info：包装信息（字符串）
  - operator：操作人（字符串）
- **数据来源/关联**：产品表、批号表
- **实际减负**：UDI追溯自动化

#### 批号记录
- 记录批号，便于追溯。
- **页面名称**：灭菌车间批号记录页
- **页面地址**：/production/sterilization/batch
- **主要按钮/功能**：新增批号、查询、导出
- **接口**：
  - 新增批号：POST /api/sterilization/batch
    - 字段：batch_no、quantity、date、operator
  - 查询批号：GET /api/sterilization/batch/list
    - 参数：batch_no、dateStart、dateEnd、pageNum、pageSize
  - 导出批号：GET /api/sterilization/batch/export
- **字段说明**：
  - batch_id：批号ID（数字）
  - batch_no：批号（字符串）
  - quantity：数量（数字）
  - date：时间（YYYY-MM-DD）
  - operator：操作人（字符串）
- **数据来源/关联**：批号表、员工表
- **实际减负**：批号追溯自动化

#### 物料记录
- 记录物料流转，自动统计。
- **页面名称**：灭菌车间物料记录页
- **页面地址**：/production/sterilization/material
- **主要按钮/功能**：新增物料、查询、导出
- **接口**：
  - 新增物料：POST /api/sterilization/material
    - 字段：material_name、type、quantity、operation_type、date、operator
  - 查询物料：GET /api/sterilization/material/list
    - 参数：material_name、type、dateStart、dateEnd、operation_type、pageNum、pageSize
  - 导出物料：GET /api/sterilization/material/export
- **字段说明**：
  - material_id：物料ID（数字）
  - material_name：物料名称（字符串）
  - type：类型（字符串）
  - quantity：数量（数字）
  - operation_type：操作类型（入库/出库/退库等，字符串）
  - date：时间（YYYY-MM-DD）
  - operator：操作人（字符串）
- **数据来源/关联**：物料表、员工表
- **实际减负**：物料流转自动统计

#### 制令单记录
- 记录制令单，进度自动同步。
- **页面名称**：灭菌车间制令单记录页
- **页面地址**：/production/sterilization/mo
- **主要按钮/功能**：新增制令单、查询、导出
- **接口**：
  - 新增制令单：POST /api/sterilization/mo
    - 字段：mo_no、product、drawing、weight、batch_no、progress、status
  - 查询制令单：GET /api/sterilization/mo/list
    - 参数：mo_no、product、batch_no、status、pageNum、pageSize
  - 导出制令单：GET /api/sterilization/mo/export
- **字段说明**：
  - mo_id：制令单ID（数字）
  - mo_no：制令单号（字符串）
  - product：产品（字符串）
  - drawing：图纸（url/字符串）
  - weight：克重（数字）
  - batch_no：批号（字符串）
  - progress：进度（字符串）
  - status：状态（字符串）
- **数据来源/关联**：制令单表、产品表
- **实际减负**：制令单进度自动同步

#### 文件夹
- 文件集中归档，便于查找和共享。
- **页面名称**：灭菌车间文件夹管理页
- **页面地址**：/production/sterilization/file
- **主要按钮/功能**：上传文件、分类、下载、查询、导出
- **接口**：
  - 上传文件：POST /api/sterilization/file/upload
    - 字段：file_name、type、category、attachment
  - 查询文件：GET /api/sterilization/file/list
    - 参数：file_name、category、dateStart、dateEnd、pageNum、pageSize
  - 导出文件：GET /api/sterilization/file/export
- **字段说明**：
  - file_id：文件ID（数字）
  - file_name：文件名（字符串）
  - type：类型（字符串）
  - category：分类（字符串）
  - attachment：附件（url）
- **数据来源/关联**：文件服务器
- **实际减负**：文件集中归档，便于查找和共享

### 4.6 注塑1车间
#### 主页
- 注塑1车间主页用于功能导航，进入各业务页面。
- **页面名称**：注塑1车间主页
- **页面地址**：/production/injection1/index
- **主要按钮/功能**：功能图标导航、进入各功能页面
- **接口**：无（仅导航）
- **字段说明**：无
- **数据来源/关联**：无
- **实际减负**：统一入口，便于操作

#### 产量日报
- 记录每日注塑1产量，便于绩效考核。
- **页面名称**：注塑1车间产量日报页
- **页面地址**：/production/injection1/output
- **主要按钮/功能**：新增日报、查询、导出
- **接口**：
  - 新增日报：POST /api/injection1/output
    - 字段：date、product_model、output、operator
  - 查询日报：GET /api/injection1/output/list
    - 参数：dateStart、dateEnd、product_model、pageNum、pageSize
  - 导出日报：GET /api/injection1/output/export
- **字段说明**：
  - date：日期（YYYY-MM-DD）
  - product_model：产品型号（字符串）
  - output：产量（数字）
  - operator：操作人（字符串）
- **数据来源/关联**：产品表、员工表
- **实际减负**：自动统计产量，便于绩效考核

#### 奖惩记录
- 记录员工奖惩，自动同步薪资。
- **页面名称**：注塑1车间奖惩记录页
- **页面地址**：/production/injection1/rewardpunish
- **主要按钮/功能**：新增奖惩、审批、查询、导出
- **接口**：
  - 新增奖惩：POST /api/injection1/rewardpunish
    - 字段：employee_id、type、amount、reason、date
  - 审批奖惩：POST /api/injection1/rewardpunish/approve
    - 字段：rewardpunish_id、approver、opinion、status
  - 查询奖惩：GET /api/injection1/rewardpunish/list
    - 参数：employee_id、type、dateStart、dateEnd、status、pageNum、pageSize
  - 导出奖惩：GET /api/injection1/rewardpunish/export
- **字段说明**：
  - rewardpunish_id：奖惩ID（数字）
  - employee_id：员工ID（数字）
  - type：类型（奖励/惩罚，字符串）
  - amount：金额（数字）
  - reason：原因（字符串）
  - date：时间（YYYY-MM-DD）
  - approver：审批人（字符串）
  - opinion：审批意见（字符串）
  - status：状态（字符串）
- **数据来源/关联**：员工表、审批流表、薪资表
- **实际减负**：奖惩自动同步薪资，减少人工统计

#### 员工产量
- 记录员工个人产量，自动计算工资。
- **页面名称**：注塑1车间员工产量页
- **页面地址**：/production/injection1/employeeOutput
- **主要按钮/功能**：新增产量、查询、导出
- **接口**：
  - 新增产量：POST /api/injection1/employee/output
    - 字段：employee_id、date、output、product_model
  - 查询产量：GET /api/injection1/employee/output/list
    - 参数：employee_id、dateStart、dateEnd、product_model、pageNum、pageSize
  - 导出产量：GET /api/injection1/employee/output/export
- **字段说明**：
  - employee_id：员工ID（数字）
  - date：日期（YYYY-MM-DD）
  - output：产量（数字）
  - product_model：产品型号（字符串）
- **数据来源/关联**：员工表、产品表
- **实际减负**：自动计算工资，提升效率

#### 绩效方案
- 维护绩效方案，自动关联工资。
- **页面名称**：注塑1车间绩效方案页
- **页面地址**：/production/injection1/performance
- **主要按钮/功能**：新增方案、编辑、查询、导出
- **接口**：
  - 新增方案：POST /api/injection1/performance
    - 字段：process_node、fee、product_model
  - 查询方案：GET /api/injection1/performance/list
    - 参数：product_model、process_node、pageNum、pageSize
  - 导出方案：GET /api/injection1/performance/export
- **字段说明**：
  - performance_id：方案ID（数字）
  - process_node：工序节点（字符串）
  - fee：费用（数字）
  - product_model：适用产品（字符串）
- **数据来源/关联**：产品表、工序表
- **实际减负**：绩效自动关联工资，减少手工核算

#### 内包提点
- 维护产品提点比例，自动计入工资。
- **页面名称**：注塑1车间内包提点页
- **页面地址**：/production/injection1/bonus
- **主要按钮/功能**：新增提点、查询、导出
- **接口**：
  - 新增提点：POST /api/injection1/bonus
    - 字段：product_model、bonus_rate
  - 查询提点：GET /api/injection1/bonus/list
    - 参数：product_model、pageNum、pageSize
  - 导出提点：GET /api/injection1/bonus/export
- **字段说明**：
  - bonus_id：提点ID（数字）
  - product_model：产品型号（字符串）
  - bonus_rate：提点比例（百分比/数字）
- **数据来源/关联**：产品表
- **实际减负**：提点自动计入工资

#### 不良记录
- 记录不良品，便于质量改进。
- **页面名称**：注塑1车间不良记录页
- **页面地址**：/production/injection1/defect
- **主要按钮/功能**：新增不良、查询、统计、导出
- **接口**：
  - 新增不良：POST /api/injection1/defect
    - 字段：date、product、defect_type、quantity、reason、measure
  - 查询不良：GET /api/injection1/defect/list
    - 参数：dateStart、dateEnd、product、defect_type、pageNum、pageSize
  - 导出不良：GET /api/injection1/defect/export
- **字段说明**：
  - defect_id：不良ID（数字）
  - date：日期（YYYY-MM-DD）
  - product：产品（字符串）
  - defect_type：问题类型（字符串）
  - quantity：数量（数字）
  - reason：原因（字符串）
  - measure：处理措施（字符串）
- **数据来源/关联**：产品表、员工表
- **实际减负**：不良自动统计，便于质量改进

#### 每日UDI
- 记录每日UDI，便于追溯。
- **页面名称**：注塑1车间UDI记录页
- **页面地址**：/production/injection1/udi
- **主要按钮/功能**：新增UDI、查询、导出
- **接口**：
  - 新增UDI：POST /api/injection1/udi
    - 字段：date、batch_no、udi、package_info、operator
  - 查询UDI：GET /api/injection1/udi/list
    - 参数：dateStart、dateEnd、batch_no、udi、pageNum、pageSize
  - 导出UDI：GET /api/injection1/udi/export
- **字段说明**：
  - udi_id：UDI记录ID（数字）
  - date：日期（YYYY-MM-DD）
  - batch_no：批号（字符串）
  - udi：UDI（字符串）
  - package_info：包装信息（字符串）
  - operator：操作人（字符串）
- **数据来源/关联**：产品表、批号表
- **实际减负**：UDI追溯自动化

#### 批号记录
- 记录批号，便于追溯。
- **页面名称**：注塑1车间批号记录页
- **页面地址**：/production/injection1/batch
- **主要按钮/功能**：新增批号、查询、导出
- **接口**：
  - 新增批号：POST /api/injection1/batch
    - 字段：batch_no、quantity、date、operator
  - 查询批号：GET /api/injection1/batch/list
    - 参数：batch_no、dateStart、dateEnd、pageNum、pageSize
  - 导出批号：GET /api/injection1/batch/export
- **字段说明**：
  - batch_id：批号ID（数字）
  - batch_no：批号（字符串）
  - quantity：数量（数字）
  - date：时间（YYYY-MM-DD）
  - operator：操作人（字符串）
- **数据来源/关联**：批号表、员工表
- **实际减负**：批号追溯自动化

#### 物料记录
- 记录物料流转，自动统计。
- **页面名称**：注塑1车间物料记录页
- **页面地址**：/production/injection1/material
- **主要按钮/功能**：新增物料、查询、导出
- **接口**：
  - 新增物料：POST /api/injection1/material
    - 字段：material_name、type、quantity、operation_type、date、operator
  - 查询物料：GET /api/injection1/material/list
    - 参数：material_name、type、dateStart、dateEnd、operation_type、pageNum、pageSize
  - 导出物料：GET /api/injection1/material/export
- **字段说明**：
  - material_id：物料ID（数字）
  - material_name：物料名称（字符串）
  - type：类型（字符串）
  - quantity：数量（数字）
  - operation_type：操作类型（入库/出库/退库等，字符串）
  - date：时间（YYYY-MM-DD）
  - operator：操作人（字符串）
- **数据来源/关联**：物料表、员工表
- **实际减负**：物料流转自动统计

#### 制令单记录
- 记录制令单，进度自动同步。
- **页面名称**：注塑1车间制令单记录页
- **页面地址**：/production/injection1/mo
- **主要按钮/功能**：新增制令单、查询、导出
- **接口**：
  - 新增制令单：POST /api/injection1/mo
    - 字段：mo_no、product、drawing、weight、batch_no、progress、status
  - 查询制令单：GET /api/injection1/mo/list
    - 参数：mo_no、product、batch_no、status、pageNum、pageSize
  - 导出制令单：GET /api/injection1/mo/export
- **字段说明**：
  - mo_id：制令单ID（数字）
  - mo_no：制令单号（字符串）
  - product：产品（字符串）
  - drawing：图纸（url/字符串）
  - weight：克重（数字）
  - batch_no：批号（字符串）
  - progress：进度（字符串）
  - status：状态（字符串）
- **数据来源/关联**：制令单表、产品表
- **实际减负**：制令单进度自动同步

#### 文件夹
- 文件集中归档，便于查找和共享。
- **页面名称**：注塑1车间文件夹管理页
- **页面地址**：/production/injection1/file
- **主要按钮/功能**：上传文件、分类、下载、查询、导出
- **接口**：
  - 上传文件：POST /api/injection1/file/upload
    - 字段：file_name、type、category、attachment
  - 查询文件：GET /api/injection1/file/list
    - 参数：file_name、category、dateStart、dateEnd、pageNum、pageSize
  - 导出文件：GET /api/injection1/file/export
- **字段说明**：
  - file_id：文件ID（数字）
  - file_name：文件名（字符串）
  - type：类型（字符串）
  - category：分类（字符串）
  - attachment：附件（url）
- **数据来源/关联**：文件服务器
- **实际减负**：文件集中归档，便于查找和共享

### 4.7 注塑2车间
#### 主页
- 注塑2车间主页用于功能导航，进入各业务页面。
- **页面名称**：注塑2车间主页
- **页面地址**：/production/injection2/index
- **主要按钮/功能**：功能图标导航、进入各功能页面
- **接口**：无（仅导航）
- **字段说明**：无
- **数据来源/关联**：无
- **实际减负**：统一入口，便于操作

#### 产量日报
- 记录每日注塑2产量，便于绩效考核。
- **页面名称**：注塑2车间产量日报页
- **页面地址**：/production/injection2/output
- **主要按钮/功能**：新增日报、查询、导出
- **接口**：
  - 新增日报：POST /api/injection2/output
    - 字段：date、product_model、output、operator
  - 查询日报：GET /api/injection2/output/list
    - 参数：dateStart、dateEnd、product_model、pageNum、pageSize
  - 导出日报：GET /api/injection2/output/export
- **字段说明**：
  - date：日期（YYYY-MM-DD）
  - product_model：产品型号（字符串）
  - output：产量（数字）
  - operator：操作人（字符串）
- **数据来源/关联**：产品表、员工表
- **实际减负**：自动统计产量，便于绩效考核

#### 奖惩记录
- 记录员工奖惩，自动同步薪资。
- **页面名称**：注塑2车间奖惩记录页
- **页面地址**：/production/injection2/rewardpunish
- **主要按钮/功能**：新增奖惩、审批、查询、导出
- **接口**：
  - 新增奖惩：POST /api/injection2/rewardpunish
    - 字段：employee_id、type、amount、reason、date
  - 审批奖惩：POST /api/injection2/rewardpunish/approve
    - 字段：rewardpunish_id、approver、opinion、status
  - 查询奖惩：GET /api/injection2/rewardpunish/list
    - 参数：employee_id、type、dateStart、dateEnd、status、pageNum、pageSize
  - 导出奖惩：GET /api/injection2/rewardpunish/export
- **字段说明**：
  - rewardpunish_id：奖惩ID（数字）
  - employee_id：员工ID（数字）
  - type：类型（奖励/惩罚，字符串）
  - amount：金额（数字）
  - reason：原因（字符串）
  - date：时间（YYYY-MM-DD）
  - approver：审批人（字符串）
  - opinion：审批意见（字符串）
  - status：状态（字符串）
- **数据来源/关联**：员工表、审批流表、薪资表
- **实际减负**：奖惩自动同步薪资，减少人工统计

#### 员工产量
- 记录员工个人产量，自动计算工资。
- **页面名称**：注塑2车间员工产量页
- **页面地址**：/production/injection2/employeeOutput
- **主要按钮/功能**：新增产量、查询、导出
- **接口**：
  - 新增产量：POST /api/injection2/employee/output
    - 字段：employee_id、date、output、product_model
  - 查询产量：GET /api/injection2/employee/output/list
    - 参数：employee_id、dateStart、dateEnd、product_model、pageNum、pageSize
  - 导出产量：GET /api/injection2/employee/output/export
- **字段说明**：
  - employee_id：员工ID（数字）
  - date：日期（YYYY-MM-DD）
  - output：产量（数字）
  - product_model：产品型号（字符串）
- **数据来源/关联**：员工表、产品表
- **实际减负**：自动计算工资，提升效率

#### 绩效方案
- 维护绩效方案，自动关联工资。
- **页面名称**：注塑2车间绩效方案页
- **页面地址**：/production/injection2/performance
- **主要按钮/功能**：新增方案、编辑、查询、导出
- **接口**：
  - 新增方案：POST /api/injection2/performance
    - 字段：process_node、fee、product_model
  - 查询方案：GET /api/injection2/performance/list
    - 参数：product_model、process_node、pageNum、pageSize
  - 导出方案：GET /api/injection2/performance/export
- **字段说明**：
  - performance_id：方案ID（数字）
  - process_node：工序节点（字符串）
  - fee：费用（数字）
  - product_model：适用产品（字符串）
- **数据来源/关联**：产品表、工序表
- **实际减负**：绩效自动关联工资，减少手工核算

#### 内包提点
- 维护产品提点比例，自动计入工资。
- **页面名称**：注塑2车间内包提点页
- **页面地址**：/production/injection2/bonus
- **主要按钮/功能**：新增提点、查询、导出
- **接口**：
  - 新增提点：POST /api/injection2/bonus
    - 字段：product_model、bonus_rate
  - 查询提点：GET /api/injection2/bonus/list
    - 参数：product_model、pageNum、pageSize
  - 导出提点：GET /api/injection2/bonus/export
- **字段说明**：
  - bonus_id：提点ID（数字）
  - product_model：产品型号（字符串）
  - bonus_rate：提点比例（百分比/数字）
- **数据来源/关联**：产品表
- **实际减负**：提点自动计入工资

#### 不良记录
- 记录不良品，便于质量改进。
- **页面名称**：注塑2车间不良记录页
- **页面地址**：/production/injection2/defect
- **主要按钮/功能**：新增不良、查询、统计、导出
- **接口**：
  - 新增不良：POST /api/injection2/defect
    - 字段：date、product、defect_type、quantity、reason、measure
  - 查询不良：GET /api/injection2/defect/list
    - 参数：dateStart、dateEnd、product、defect_type、pageNum、pageSize
  - 导出不良：GET /api/injection2/defect/export
- **字段说明**：
  - defect_id：不良ID（数字）
  - date：日期（YYYY-MM-DD）
  - product：产品（字符串）
  - defect_type：问题类型（字符串）
  - quantity：数量（数字）
  - reason：原因（字符串）
  - measure：处理措施（字符串）
- **数据来源/关联**：产品表、员工表
- **实际减负**：不良自动统计，便于质量改进

#### 每日UDI
- 记录每日UDI，便于追溯。
- **页面名称**：注塑2车间UDI记录页
- **页面地址**：/production/injection2/udi
- **主要按钮/功能**：新增UDI、查询、导出
- **接口**：
  - 新增UDI：POST /api/injection2/udi
    - 字段：date、batch_no、udi、package_info、operator
  - 查询UDI：GET /api/injection2/udi/list
    - 参数：dateStart、dateEnd、batch_no、udi、pageNum、pageSize
  - 导出UDI：GET /api/injection2/udi/export
- **字段说明**：
  - udi_id：UDI记录ID（数字）
  - date：日期（YYYY-MM-DD）
  - batch_no：批号（字符串）
  - udi：UDI（字符串）
  - package_info：包装信息（字符串）
  - operator：操作人（字符串）
- **数据来源/关联**：产品表、批号表
- **实际减负**：UDI追溯自动化

#### 批号记录
- 记录批号，便于追溯。
- **页面名称**：注塑2车间批号记录页
- **页面地址**：/production/injection2/batch
- **主要按钮/功能**：新增批号、查询、导出
- **接口**：
  - 新增批号：POST /api/injection2/batch
    - 字段：batch_no、quantity、date、operator
  - 查询批号：GET /api/injection2/batch/list
    - 参数：batch_no、dateStart、dateEnd、pageNum、pageSize
  - 导出批号：GET /api/injection2/batch/export
- **字段说明**：
  - batch_id：批号ID（数字）
  - batch_no：批号（字符串）
  - quantity：数量（数字）
  - date：时间（YYYY-MM-DD）
  - operator：操作人（字符串）
- **数据来源/关联**：批号表、员工表
- **实际减负**：批号追溯自动化

#### 物料记录
- 记录物料流转，自动统计。
- **页面名称**：注塑2车间物料记录页
- **页面地址**：/production/injection2/material
- **主要按钮/功能**：新增物料、查询、导出
- **接口**：
  - 新增物料：POST /api/injection2/material
    - 字段：material_name、type、quantity、operation_type、date、operator
  - 查询物料：GET /api/injection2/material/list
    - 参数：material_name、type、dateStart、dateEnd、operation_type、pageNum、pageSize
  - 导出物料：GET /api/injection2/material/export
- **字段说明**：
  - material_id：物料ID（数字）
  - material_name：物料名称（字符串）
  - type：类型（字符串）
  - quantity：数量（数字）
  - operation_type：操作类型（入库/出库/退库等，字符串）
  - date：时间（YYYY-MM-DD）
  - operator：操作人（字符串）
- **数据来源/关联**：物料表、员工表
- **实际减负**：物料流转自动统计

#### 制令单记录
- 记录制令单，进度自动同步。
- **页面名称**：注塑2车间制令单记录页
- **页面地址**：/production/injection2/mo
- **主要按钮/功能**：新增制令单、查询、导出
- **接口**：
  - 新增制令单：POST /api/injection2/mo
    - 字段：mo_no、product、drawing、weight、batch_no、progress、status
  - 查询制令单：GET /api/injection2/mo/list
    - 参数：mo_no、product、batch_no、status、pageNum、pageSize
  - 导出制令单：GET /api/injection2/mo/export
- **字段说明**：
  - mo_id：制令单ID（数字）
  - mo_no：制令单号（字符串）
  - product：产品（字符串）
  - drawing：图纸（url/字符串）
  - weight：克重（数字）
  - batch_no：批号（字符串）
  - progress：进度（字符串）
  - status：状态（字符串）
- **数据来源/关联**：制令单表、产品表
- **实际减负**：制令单进度自动同步

#### 文件夹
- 文件集中归档，便于查找和共享。
- **页面名称**：注塑2车间文件夹管理页
- **页面地址**：/production/injection2/file
- **主要按钮/功能**：上传文件、分类、下载、查询、导出
- **接口**：
  - 上传文件：POST /api/injection2/file/upload
    - 字段：file_name、type、category、attachment
  - 查询文件：GET /api/injection2/file/list
    - 参数：file_name、category、dateStart、dateEnd、pageNum、pageSize
  - 导出文件：GET /api/injection2/file/export
- **字段说明**：
  - file_id：文件ID（数字）
  - file_name：文件名（字符串）
  - type：类型（字符串）
  - category：分类（字符串）
  - attachment：附件（url）
- **数据来源/关联**：文件服务器
  - 上传：POST /api/injection2/file/upload
    - 字段：文件名、类型、分类、附件
  - 查询：GET /api/injection2/file/list
- **数据来源/关联**：
  - 文件服务器
- **实际减负**：
  - 文件集中归档，便于查找和共享

---

## 5. 计划物控部

### 5.1 生产计划管理
#### 生产计划管理页
- 生产计划管理用于新建、编辑、删除、导入、导出和查询生产计划。
- **页面名称**：生产计划管理页
- **页面地址**：/planning/plan
- **主要按钮/功能**：
  - 新建生产计划（按钮）
  - 编辑（按钮）
  - 删除（按钮）
  - 导入（按钮）
  - 导出（按钮）
  - 查询（按钮）
- **接口**：
  - 新建：POST /api/plan/create
    - 字段：plan_no、product、quantity、start_date、end_date、owner、remark
  - 编辑：POST /api/plan/update
    - 字段：plan_id、变更内容
  - 删除：POST /api/plan/delete
    - 字段：plan_id
  - 查询：GET /api/plan/list
    - 参数：plan_no、product、owner、dateStart、dateEnd、pageNum、pageSize
  - 导入：POST /api/plan/import
    - 字段：file（Excel文件）
  - 导出：GET /api/plan/export
    - 参数：plan_no、product、owner、dateStart、dateEnd
- **字段说明**：
  - plan_id：计划ID（数字）
  - plan_no：计划编号（字符串）
  - product：产品名称（字符串）
  - quantity：数量（数字）
  - start_date：计划开始日期（YYYY-MM-DD）
  - end_date：计划结束日期（YYYY-MM-DD）
  - owner：负责人（字符串）
  - remark：备注（字符串）
- **数据来源/关联**：产品表、员工表、订单表
- **实际减负**：计划自动分解，减少人工排产，提升响应速度

### 5.2 物料需求计划（MRP）
#### 物料需求计划页
- 物料需求计划用于根据生产计划自动生成物料需求，支持生成、导入、导出、查询。
- **页面名称**：物料需求计划页
- **页面地址**：/planning/mrp
- **主要按钮/功能**：
  - 生成MRP（按钮）
  - 导入（按钮）
  - 导出（按钮）
  - 查询（按钮）
- **接口**：
  - 生成：POST /api/mrp/generate
    - 字段：plan_id、生成参数
  - 查询：GET /api/mrp/list
    - 参数：plan_id、material_name、dateStart、dateEnd、pageNum、pageSize
  - 导入：POST /api/mrp/import
    - 字段：file（Excel文件）
  - 导出：GET /api/mrp/export
    - 参数：plan_id、material_name、dateStart、dateEnd
- **字段说明**：
  - mrp_id：MRP记录ID（数字）
  - plan_id：关联生产计划ID（数字）
  - material_name：物料名称（字符串）
  - quantity：需求数量（数字）
  - required_date：需求日期（YYYY-MM-DD）
  - remark：备注（字符串）
- **数据来源/关联**：生产计划表、物料表、BOM表
- **实际减负**：自动生成物料需求，减少手工统计，提升物料供应效率

### 5.3 生产进度跟踪
#### 生产进度跟踪页
- 实时跟踪各生产计划、工单、车间的生产进度，支持进度上报、查询、导出。
- **页面名称**：生产进度跟踪页
- **页面地址**：/planning/progress
- **主要按钮/功能**：
  - 进度上报（按钮）
  - 查询（按钮）
  - 导出（按钮）
- **接口**：
  - 上报进度：POST /api/progress/report
    - 字段：plan_id、workshop、process_node、progress_percent、report_time、reporter、remark
  - 查询进度：GET /api/progress/list
    - 参数：plan_id、workshop、process_node、dateStart、dateEnd、pageNum、pageSize
  - 导出进度：GET /api/progress/export
    - 参数：plan_id、workshop、process_node、dateStart、dateEnd
- **字段说明**：
  - progress_id：进度ID（数字）
  - plan_id：生产计划ID（数字）
  - workshop：车间（字符串）
  - process_node：工序节点（字符串）
  - progress_percent：进度百分比（数字）
  - report_time：上报时间（YYYY-MM-DD HH:mm）
  - reporter：上报人（字符串）
  - remark：备注（字符串）
- **数据来源/关联**：生产计划表、工单表、员工表
- **实际减负**：进度自动汇总，减少人工统计，提升生产透明度

### 5.4 生产异常管理
#### 生产异常管理页
- 记录、跟踪、处理生产过程中的各类异常，支持异常上报、处理、查询、导出。
- **页面名称**：生产异常管理页
- **页面地址**：/planning/abnormal
- **主要按钮/功能**：
  - 异常上报（按钮）
  - 处理（按钮）
  - 查询（按钮）
  - 导出（按钮）
- **接口**：
  - 上报异常：POST /api/abnormal/report
    - 字段：plan_id、workshop、process_node、abnormal_type、description、report_time、reporter、status
  - 处理异常：POST /api/abnormal/handle
    - 字段：abnormal_id、handler、handle_time、handle_result、remark
  - 查询异常：GET /api/abnormal/list
    - 参数：plan_id、workshop、process_node、abnormal_type、status、dateStart、dateEnd、pageNum、pageSize
  - 导出异常：GET /api/abnormal/export
    - 参数：plan_id、workshop、process_node、abnormal_type、status、dateStart、dateEnd
- **字段说明**：
  - abnormal_id：异常ID（数字）
  - plan_id：生产计划ID（数字）
  - workshop：车间（字符串）
  - process_node：工序节点（字符串）
  - abnormal_type：异常类型（字符串）
  - description：异常描述（字符串）
  - report_time：上报时间（YYYY-MM-DD HH:mm）
  - reporter：上报人（字符串）
  - status：状态（待处理/已处理，字符串）
  - handler：处理人（字符串）
  - handle_time：处理时间（YYYY-MM-DD HH:mm）
  - handle_result：处理结果（字符串）
  - remark：备注（字符串）
- **数据来源/关联**：生产计划表、工单表、员工表
- **实际减负**：异常自动流转，减少遗漏，提升响应速度和闭环率

### 5.5 订单管理
#### 订单管理页
- 订单管理用于录入、维护、查询、导入、导出客户订单，自动与生产计划、库存关联。
- **页面名称**：订单管理页
- **页面地址**：/planning/order
- **主要按钮/功能**：
  - 新建订单（按钮）
  - 编辑（按钮）
  - 删除（按钮）
  - 导入（按钮）
  - 导出（按钮）
  - 查询（按钮）
- **接口**：
  - 新建订单：POST /api/order/create
    - 字段：order_no、customer、product、quantity、order_date、delivery_date、owner、remark
  - 编辑订单：POST /api/order/update
    - 字段：order_id、变更内容
  - 删除订单：POST /api/order/delete
    - 字段：order_id
  - 查询订单：GET /api/order/list
    - 参数：order_no、customer、product、owner、dateStart、dateEnd、pageNum、pageSize
  - 导入订单：POST /api/order/import
    - 字段：file（Excel文件）
  - 导出订单：GET /api/order/export
    - 参数：order_no、customer、product、owner、dateStart、dateEnd
- **字段说明**：
  - order_id：订单ID（数字）
  - order_no：订单编号（字符串）
  - customer：客户名称（字符串）
  - product：产品名称（字符串）
  - quantity：数量（数字）
  - order_date：下单日期（YYYY-MM-DD）
  - delivery_date：交付日期（YYYY-MM-DD）
  - owner：负责人（字符串）
  - remark：备注（字符串）
- **数据来源/关联**：客户表、产品表、生产计划表、库存表
- **实际减负**：订单与计划、库存自动关联，减少重复录入，提升订单响应速度

### 5.6 库存预警
#### 库存预警页
- 实时监控物料、成品库存，自动预警低于安全库存的物料，支持查询、导出、预警处理。
- **页面名称**：库存预警页
- **页面地址**：/planning/inventory-warning
- **主要按钮/功能**：
  - 查询（按钮）
  - 导出（按钮）
  - 预警处理（按钮）
- **接口**：
  - 查询库存预警：GET /api/inventory-warning/list
    - 参数：material_name、product_name、warning_type、dateStart、dateEnd、pageNum、pageSize
  - 导出库存预警：GET /api/inventory-warning/export
    - 参数：material_name、product_name、warning_type、dateStart、dateEnd
  - 处理预警：POST /api/inventory-warning/handle
    - 字段：warning_id、handler、handle_time、handle_result、remark
- **字段说明**：
  - warning_id：预警ID（数字）
  - material_name：物料名称（字符串）
  - product_name：产品名称（字符串）
  - warning_type：预警类型（低库存/超储/呆滞等，字符串）
  - quantity：当前库存（数字）
  - safe_quantity：安全库存（数字）
  - warning_time：预警时间（YYYY-MM-DD HH:mm）
  - handler：处理人（字符串）
  - handle_time：处理时间（YYYY-MM-DD HH:mm）
  - handle_result：处理结果（字符串）
  - remark：备注（字符串）
- **数据来源/关联**：库存表、物料表、产品表
- **实际减负**：自动预警，减少缺料、超储风险，提升库存管理效率

---

## 6. 质量管理部

### 6.1 进料检验管理
#### 进料检验管理页
- 对采购入库物料进行检验，记录检验结果，支持检验单生成、编辑、查询、导出。
- **页面名称**：进料检验管理页
- **页面地址**：/quality/incoming-inspection
- **主要按钮/功能**：
  - 新建检验单（按钮）
  - 编辑（按钮）
  - 删除（按钮）
  - 查询（按钮）
  - 导出（按钮）
- **接口**：
  - 新建检验单：POST /api/incoming-inspection/create
    - 字段：inspection_no、material_name、supplier、quantity、inspection_date、inspector、result、remark
  - 编辑检验单：POST /api/incoming-inspection/update
    - 字段：inspection_id、变更内容
  - 删除检验单：POST /api/incoming-inspection/delete
    - 字段：inspection_id
  - 查询检验单：GET /api/incoming-inspection/list
    - 参数：inspection_no、material_name、supplier、inspector、dateStart、dateEnd、pageNum、pageSize
  - 导出检验单：GET /api/incoming-inspection/export
    - 参数：inspection_no、material_name、supplier、inspector、dateStart、dateEnd
- **字段说明**：
  - inspection_id：检验单ID（数字）
  - inspection_no：检验单号（字符串）
  - material_name：物料名称（字符串）
  - supplier：供应商（字符串）
  - quantity：数量（数字）
  - inspection_date：检验日期（YYYY-MM-DD）
  - inspector：检验员（字符串）
  - result：检验结果（合格/不合格，字符串）
  - remark：备注（字符串）
- **数据来源/关联**：采购入库单、物料表、供应商表、员工表
- **实际减负**：检验流程电子化，自动统计合格率，提升追溯效率

### 6.2 过程检验管理
#### 过程检验管理页
- 对生产过程中的关键工序进行检验，记录过程检验数据，支持检验单生成、编辑、查询、导出。
- **页面名称**：过程检验管理页
- **页面地址**：/quality/process-inspection
- **主要按钮/功能**：
  - 新建检验单（按钮）
  - 编辑（按钮）
  - 删除（按钮）
  - 查询（按钮）
  - 导出（按钮）
- **接口**：
  - 新建检验单：POST /api/process-inspection/create
    - 字段：inspection_no、plan_id、workshop、process_node、inspection_date、inspector、result、remark
  - 编辑检验单：POST /api/process-inspection/update
    - 字段：inspection_id、变更内容
  - 删除检验单：POST /api/process-inspection/delete
    - 字段：inspection_id
  - 查询检验单：GET /api/process-inspection/list
    - 参数：inspection_no、plan_id、workshop、process_node、inspector、dateStart、dateEnd、pageNum、pageSize
  - 导出检验单：GET /api/process-inspection/export
    - 参数：inspection_no、plan_id、workshop、process_node、inspector、dateStart、dateEnd
- **字段说明**：
  - inspection_id：检验单ID（数字）
  - inspection_no：检验单号（字符串）
  - plan_id：生产计划ID（数字）
  - workshop：车间（字符串）
  - process_node：工序节点（字符串）
  - inspection_date：检验日期（YYYY-MM-DD）
  - inspector：检验员（字符串）
  - result：检验结果（合格/不合格，字符串）
  - remark：备注（字符串）
- **数据来源/关联**：生产计划表、工序表、员工表
- **实际减负**：过程检验自动化，提升质量控制效率

### 6.3 成品检验管理
#### 成品检验管理页
- 对生产完成的成品进行检验，记录成品检验结果，支持检验单生成、编辑、查询、导出。
- **页面名称**：成品检验管理页
- **页面地址**：/quality/finished-inspection
- **主要按钮/功能**：
  - 新建检验单（按钮）
  - 编辑（按钮）
  - 删除（按钮）
  - 查询（按钮）
  - 导出（按钮）
- **接口**：
  - 新建检验单：POST /api/finished-inspection/create
    - 字段：inspection_no、product_name、quantity、inspection_date、inspector、result、remark
  - 编辑检验单：POST /api/finished-inspection/update
    - 字段：inspection_id、变更内容
  - 删除检验单：POST /api/finished-inspection/delete
    - 字段：inspection_id
  - 查询检验单：GET /api/finished-inspection/list
    - 参数：inspection_no、product_name、inspector、dateStart、dateEnd、pageNum、pageSize
  - 导出检验单：GET /api/finished-inspection/export
    - 参数：inspection_no、product_name、inspector、dateStart、dateEnd
- **字段说明**：
  - inspection_id：检验单ID（数字）
  - inspection_no：检验单号（字符串）
  - product_name：产品名称（字符串）
  - quantity：数量（数字）
  - inspection_date：检验日期（YYYY-MM-DD）
  - inspector：检验员（字符串）
  - result：检验结果（合格/不合格，字符串）
  - remark：备注（字符串）
- **数据来源/关联**：成品入库单、产品表、员工表
- **实际减负**：成品检验电子化，自动统计合格率，提升出厂质量

### 6.4 不良品管理
#### 不良品管理页
- 记录、跟踪、处理各环节产生的不良品，支持不良品登记、处理、查询、导出。
- **页面名称**：不良品管理页
- **页面地址**：/quality/defect
- **主要按钮/功能**：
  - 新建不良品登记（按钮）
  - 处理（按钮）
  - 查询（按钮）
  - 导出（按钮）
- **接口**：
  - 新建不良品登记：POST /api/defect/create
    - 字段：defect_no、source_type、source_id、product_name、quantity、defect_type、discovery_date、discoverer、status、remark
  - 处理不良品：POST /api/defect/handle
    - 字段：defect_id、handler、handle_time、handle_result、remark
  - 查询不良品：GET /api/defect/list
    - 参数：defect_no、product_name、defect_type、status、discoverer、dateStart、dateEnd、pageNum、pageSize
  - 导出不良品：GET /api/defect/export
    - 参数：defect_no、product_name、defect_type、status、discoverer、dateStart、dateEnd
- **字段说明**：
  - defect_id：不良品ID（数字）
  - defect_no：不良品编号（字符串）
  - source_type：来源类型（进料/过程/成品，字符串）
  - source_id：来源单据ID（数字）
  - product_name：产品名称（字符串）
  - quantity：数量（数字）
  - defect_type：不良类型（字符串）
  - discovery_date：发现日期（YYYY-MM-DD）
  - discoverer：发现人（字符串）
  - status：状态（待处理/已处理，字符串）
  - handler：处理人（字符串）
  - handle_time：处理时间（YYYY-MM-DD HH:mm）
  - handle_result：处理结果（字符串）
  - remark：备注（字符串）
- **数据来源/关联**：进料检验单、过程检验单、成品检验单、产品表、员工表
- **实际减负**：不良品全流程追溯，自动统计，提升闭环处理率

---

## 7. 技术研发部

### 7.1 技术资料管理
#### 技术资料管理页
- 管理产品相关的技术文档、图纸、标准等资料，支持上传、分类、下载、查询、导出。
- **页面名称**：技术资料管理页
- **页面地址**：/rd/tech-doc
- **主要按钮/功能**：
  - 上传资料（按钮）
  - 分类（下拉）
  - 下载（按钮）
  - 查询（按钮）
  - 导出（按钮）
- **接口**：
  - 上传资料：POST /api/tech-doc/upload
    - 字段：doc_name、category、type、uploader、upload_time、attachment、remark
  - 查询资料：GET /api/tech-doc/list
    - 参数：doc_name、category、type、uploader、dateStart、dateEnd、pageNum、pageSize
  - 下载资料：GET /api/tech-doc/download
    - 参数：doc_id
  - 导出资料：GET /api/tech-doc/export
    - 参数：doc_name、category、type、uploader、dateStart、dateEnd
- **字段说明**：
  - doc_id：资料ID（数字）
  - doc_name：资料名称（字符串）
  - category：分类（字符串）
  - type：类型（图纸/标准/说明书等，字符串）
  - uploader：上传人（字符串）
  - upload_time：上传时间（YYYY-MM-DD HH:mm）
  - attachment：附件（url）
  - remark：备注（字符串）
- **数据来源/关联**：产品表、文件服务器、员工表
- **实际减负**：资料集中管理，便于查找和共享，减少资料丢失

### 7.2 工艺管理
#### 工艺管理页
- 管理产品生产工艺流程、参数、工艺变更，支持工艺卡维护、工艺变更、查询、导出。
- **页面名称**：工艺管理页
- **页面地址**：/rd/process
- **主要按钮/功能**：
  - 新建工艺卡（按钮）
  - 工艺变更（按钮）
  - 编辑（按钮）
  - 删除（按钮）
  - 查询（按钮）
  - 导出（按钮）
- **接口**：
  - 新建工艺卡：POST /api/process/create
    - 字段：process_no、product_name、process_flow、parameters、creator、create_time、remark
  - 工艺变更：POST /api/process/change
    - 字段：process_id、change_content、changer、change_time、remark
  - 编辑工艺卡：POST /api/process/update
    - 字段：process_id、变更内容
  - 删除工艺卡：POST /api/process/delete
    - 字段：process_id
  - 查询工艺卡：GET /api/process/list
    - 参数：process_no、product_name、creator、dateStart、dateEnd、pageNum、pageSize
  - 导出工艺卡：GET /api/process/export
    - 参数：process_no、product_name、creator、dateStart、dateEnd
- **字段说明**：
  - process_id：工艺卡ID（数字）
  - process_no：工艺编号（字符串）
  - product_name：产品名称（字符串）
  - process_flow：工艺流程（字符串/JSON）
  - parameters：工艺参数（字符串/JSON）
  - creator：创建人（字符串）
  - create_time：创建时间（YYYY-MM-DD HH:mm）
  - change_content：变更内容（字符串）
  - changer：变更人（字符串）
  - change_time：变更时间（YYYY-MM-DD HH:mm）
  - remark：备注（字符串）
- **数据来源/关联**：产品表、工艺表、员工表
- **实际减负**：工艺变更留痕，流程标准化，减少工艺失控

### 7.3 新产品开发管理
#### 新产品开发管理页
- 管理新产品开发项目，记录立项、设计、试产、评审等阶段，支持项目创建、进度维护、查询、导出。
- **页面名称**：新产品开发管理页
- **页面地址**：/rd/new-product
- **主要按钮/功能**：
  - 新建项目（按钮）
  - 编辑（按钮）
  - 删除（按钮）
  - 阶段维护（按钮）
  - 查询（按钮）
  - 导出（按钮）
- **接口**：
  - 新建项目：POST /api/new-product/create
    - 字段：project_no、project_name、product_name、leader、start_date、status、remark
  - 编辑项目：POST /api/new-product/update
    - 字段：project_id、变更内容
  - 删除项目：POST /api/new-product/delete
    - 字段：project_id
  - 阶段维护：POST /api/new-product/stage
    - 字段：project_id、stage、stage_status、stage_time、remark
  - 查询项目：GET /api/new-product/list
    - 参数：project_no、project_name、product_name、leader、status、dateStart、dateEnd、pageNum、pageSize
  - 导出项目：GET /api/new-product/export
    - 参数：project_no、project_name、product_name、leader、status、dateStart、dateEnd
- **字段说明**：
  - project_id：项目ID（数字）
  - project_no：项目编号（字符串）
  - project_name：项目名称（字符串）
  - product_name：产品名称（字符串）
  - leader：负责人（字符串）
  - start_date：立项日期（YYYY-MM-DD）
  - status：项目状态（立项/设计/试产/评审/结项，字符串）
  - stage：阶段（字符串）
  - stage_status：阶段状态（字符串）
  - stage_time：阶段时间（YYYY-MM-DD）
  - remark：备注（字符串）
- **数据来源/关联**：产品表、项目表、员工表
- **实际减负**：新产品开发全流程可视化，进度透明，减少遗漏和延误

---

## 8. 设备研发部

### 8.1 设备台账管理
#### 设备台账管理页
- 管理公司所有设备的基础信息、档案、状态，支持设备新增、编辑、查询、导出。
- **页面名称**：设备台账管理页
- **页面地址**：/equipment/ledger
- **主要按钮/功能**：
  - 新增设备（按钮）
  - 编辑（按钮）
  - 删除（按钮）
  - 查询（按钮）
  - 导出（按钮）
- **接口**：
  - 新增设备：POST /api/equipment/ledger/create
    - 字段：equipment_no、equipment_name、model、spec、location、status、purchase_date、owner、remark
  - 编辑设备：POST /api/equipment/ledger/update
    - 字段：equipment_id、变更内容
  - 删除设备：POST /api/equipment/ledger/delete
    - 字段：equipment_id
  - 查询设备：GET /api/equipment/ledger/list
    - 参数：equipment_no、equipment_name、model、status、owner、dateStart、dateEnd、pageNum、pageSize
  - 导出设备：GET /api/equipment/ledger/export
    - 参数：equipment_no、equipment_name、model、status、owner、dateStart、dateEnd
- **字段说明**：
  - equipment_id：设备ID（数字）
  - equipment_no：设备编号（字符串）
  - equipment_name：设备名称（字符串）
  - model：型号（字符串）
  - spec：规格（字符串）
  - location：安装位置（字符串）
  - status：状态（在用/停用/报废，字符串）
  - purchase_date：购置日期（YYYY-MM-DD）
  - owner：负责人（字符串）
  - remark：备注（字符串）
- **数据来源/关联**：设备表、采购表、员工表
- **实际减负**：设备信息集中管理，便于追溯和维护

### 8.2 设备维护管理
#### 设备维护管理页
- 记录设备维护、保养、维修等活动，支持维护计划制定、维护记录、查询、导出。
- **页面名称**：设备维护管理页
- **页面地址**：/equipment/maintenance
- **主要按钮/功能**：
  - 新建维护计划（按钮）
  - 新建维护记录（按钮）
  - 编辑（按钮）
  - 删除（按钮）
  - 查询（按钮）
  - 导出（按钮）
- **接口**：
  - 新建维护计划：POST /api/equipment/maintenance/plan/create
    - 字段：plan_no、equipment_id、maintenance_type、plan_date、owner、remark
  - 新建维护记录：POST /api/equipment/maintenance/record/create
    - 字段：record_no、equipment_id、maintenance_type、maintenance_date、maintainer、result、remark
  - 编辑维护：POST /api/equipment/maintenance/update
    - 字段：maintenance_id、变更内容
  - 删除维护：POST /api/equipment/maintenance/delete
    - 字段：maintenance_id
  - 查询维护：GET /api/equipment/maintenance/list
    - 参数：equipment_id、maintenance_type、owner、dateStart、dateEnd、pageNum、pageSize
  - 导出维护：GET /api/equipment/maintenance/export
    - 参数：equipment_id、maintenance_type、owner、dateStart、dateEnd
- **字段说明**：
  - maintenance_id：维护ID（数字）
  - plan_no：维护计划编号（字符串）
  - record_no：维护记录编号（字符串）
  - equipment_id：设备ID（数字）
  - maintenance_type：维护类型（保养/维修/巡检，字符串）
  - plan_date：计划日期（YYYY-MM-DD）
  - maintenance_date：维护日期（YYYY-MM-DD）
  - maintainer：维护人（字符串）
  - result：维护结果（正常/异常，字符串）
  - owner：负责人（字符串）
  - remark：备注（字符串）
- **数据来源/关联**：设备表、维护计划表、维护记录表、员工表
- **实际减负**：维护计划自动提醒，维护记录电子化，减少设备故障

### 8.3 设备备件管理
#### 设备备件管理页
- 管理设备所需备件的采购、库存、领用，支持备件入库、出库、查询、导出。
- **页面名称**：设备备件管理页
- **页面地址**：/equipment/spare
- **主要按钮/功能**：
  - 新增备件（按钮）
  - 入库（按钮）
  - 出库（按钮）
  - 编辑（按钮）
  - 删除（按钮）
  - 查询（按钮）
  - 导出（按钮）
- **接口**：
  - 新增备件：POST /api/equipment/spare/create
    - 字段：spare_no、spare_name、model、spec、unit、quantity、location、owner、remark
  - 入库：POST /api/equipment/spare/in
    - 字段：spare_id、quantity、in_date、operator、remark
  - 出库：POST /api/equipment/spare/out
    - 字段：spare_id、quantity、out_date、operator、remark
  - 编辑备件：POST /api/equipment/spare/update
    - 字段：spare_id、变更内容
  - 删除备件：POST /api/equipment/spare/delete
    - 字段：spare_id
  - 查询备件：GET /api/equipment/spare/list
    - 参数：spare_no、spare_name、model、location、owner、dateStart、dateEnd、pageNum、pageSize
  - 导出备件：GET /api/equipment/spare/export
    - 参数：spare_no、spare_name、model、location、owner、dateStart、dateEnd
- **字段说明**：
  - spare_id：备件ID（数字）
  - spare_no：备件编号（字符串）
  - spare_name：备件名称（字符串）
  - model：型号（字符串）
  - spec：规格（字符串）
  - unit：单位（字符串）
  - quantity：数量（数字）
  - location：存放位置（字符串）
  - in_date：入库日期（YYYY-MM-DD）
  - out_date：出库日期（YYYY-MM-DD）
  - operator：操作人（字符串）
  - owner：负责人（字符串）
  - remark：备注（字符串）
- **数据来源/关联**：备件表、设备表、采购表、库存表、员工表
- **实际减负**：备件库存自动统计，减少缺件停机

### 8.4 设备改造与创新管理
#### 设备改造与创新管理页
- 管理设备改造、创新项目，记录立项、实施、评审等过程，支持项目创建、进度维护、查询、导出。
- **页面名称**：设备改造与创新管理页
- **页面地址**：/equipment/innovation
- **主要按钮/功能**：
  - 新建项目（按钮）
  - 编辑（按钮）
  - 删除（按钮）
  - 阶段维护（按钮）
  - 查询（按钮）
  - 导出（按钮）
- **接口**：
  - 新建项目：POST /api/equipment/innovation/create
    - 字段：project_no、project_name、equipment_id、leader、start_date、status、remark
  - 编辑项目：POST /api/equipment/innovation/update
    - 字段：project_id、变更内容
  - 删除项目：POST /api/equipment/innovation/delete
    - 字段：project_id
  - 阶段维护：POST /api/equipment/innovation/stage
    - 字段：project_id、stage、stage_status、stage_time、remark
  - 查询项目：GET /api/equipment/innovation/list
    - 参数：project_no、project_name、equipment_id、leader、status、dateStart、dateEnd、pageNum、pageSize
  - 导出项目：GET /api/equipment/innovation/export
    - 参数：project_no、project_name、equipment_id、leader、status、dateStart、dateEnd
- **字段说明**：
  - project_id：项目ID（数字）
  - project_no：项目编号（字符串）
  - project_name：项目名称（字符串）
  - equipment_id：设备ID（数字）
  - leader：负责人（字符串）
  - start_date：立项日期（YYYY-MM-DD）
  - status：项目状态（立项/实施/评审/结项，字符串）
  - stage：阶段（字符串）
  - stage_status：阶段状态（字符串）
  - stage_time：阶段时间（YYYY-MM-DD）
  - remark：备注（字符串）
- **数据来源/关联**：设备表、项目表、员工表
- **实际减负**：设备创新全流程可视化，进度透明，提升设备利用率和创新能力

---

## 9. 市场营销部

### 9.1 客户管理
#### 客户管理页
- 管理客户基础信息、联系人、历史合作记录，支持客户新增、编辑、查询、导出。
- **页面名称**：客户管理页
- **页面地址**：/marketing/customer
- **主要按钮/功能**：
  - 新增客户（按钮）
  - 编辑（按钮）
  - 删除（按钮）
  - 查询（按钮）
  - 导出（按钮）
- **接口**：
  - 新增客户：POST /api/marketing/customer/create
    - 字段：customer_no、customer_name、contact_person、contact_phone、address、industry、level、owner、remark
  - 编辑客户：POST /api/marketing/customer/update
    - 字段：customer_id、变更内容
  - 删除客户：POST /api/marketing/customer/delete
    - 字段：customer_id
  - 查询客户：GET /api/marketing/customer/list
    - 参数：customer_no、customer_name、contact_person、industry、level、owner、dateStart、dateEnd、pageNum、pageSize
  - 导出客户：GET /api/marketing/customer/export
    - 参数：customer_no、customer_name、contact_person、industry、level、owner、dateStart、dateEnd
- **字段说明**：
  - customer_id：客户ID（数字）
  - customer_no：客户编号（字符串）
  - customer_name：客户名称（字符串）
  - contact_person：联系人（字符串）
  - contact_phone：联系电话（字符串）
  - address：地址（字符串）
  - industry：行业（字符串）
  - level：客户等级（A/B/C，字符串）
  - owner：负责人（字符串）
  - remark：备注（字符串）
- **数据来源/关联**：客户表、订单表、员工表
- **实际减负**：客户信息集中管理，便于跟进和维护

### 9.2 销售机会管理
#### 销售机会管理页
- 跟踪销售线索、商机进展，支持机会新增、阶段推进、查询、导出。
- **页面名称**：销售机会管理页
- **页面地址**：/marketing/opportunity
- **主要按钮/功能**：
  - 新增机会（按钮）
  - 阶段推进（按钮）
  - 编辑（按钮）
  - 删除（按钮）
  - 查询（按钮）
  - 导出（按钮）
- **接口**：
  - 新增机会：POST /api/marketing/opportunity/create
    - 字段：opportunity_no、customer_id、title、stage、amount、owner、create_time、remark
  - 阶段推进：POST /api/marketing/opportunity/advance
    - 字段：opportunity_id、stage、advance_time、remark
  - 编辑机会：POST /api/marketing/opportunity/update
    - 字段：opportunity_id、变更内容
  - 删除机会：POST /api/marketing/opportunity/delete
    - 字段：opportunity_id
  - 查询机会：GET /api/marketing/opportunity/list
    - 参数：opportunity_no、customer_id、title、stage、owner、dateStart、dateEnd、pageNum、pageSize
  - 导出机会：GET /api/marketing/opportunity/export
    - 参数：opportunity_no、customer_id、title、stage、owner、dateStart、dateEnd
- **字段说明**：
  - opportunity_id：机会ID（数字）
  - opportunity_no：机会编号（字符串）
  - customer_id：客户ID（数字）
  - title：机会标题（字符串）
  - stage：阶段（初步接洽/方案/报价/谈判/成交，字符串）
  - amount：预计金额（数字）
  - owner：负责人（字符串）
  - create_time：创建时间（YYYY-MM-DD HH:mm）
  - advance_time：推进时间（YYYY-MM-DD HH:mm）
  - remark：备注（字符串）
- **数据来源/关联**：客户表、订单表、员工表
- **实际减负**：销售机会全流程跟踪，减少遗漏，提升转化率

### 9.3 合同管理
#### 合同管理页
- 管理销售合同的签订、履约、归档，支持合同新增、编辑、查询、导出。
- **页面名称**：合同管理页
- **页面地址**：/marketing/contract
- **主要按钮/功能**：
  - 新增合同（按钮）
  - 编辑（按钮）
  - 删除（按钮）
  - 查询（按钮）
  - 导出（按钮）
- **接口**：
  - 新增合同：POST /api/marketing/contract/create
    - 字段：contract_no、customer_id、title、sign_date、amount、status、owner、remark
  - 编辑合同：POST /api/marketing/contract/update
    - 字段：contract_id、变更内容
  - 删除合同：POST /api/marketing/contract/delete
    - 字段：contract_id
  - 查询合同：GET /api/marketing/contract/list
    - 参数：contract_no、customer_id、title、status、owner、dateStart、dateEnd、pageNum、pageSize
  - 导出合同：GET /api/marketing/contract/export
    - 参数：contract_no、customer_id、title、status、owner、dateStart、dateEnd
- **字段说明**：
  - contract_id：合同ID（数字）
  - contract_no：合同编号（字符串）
  - customer_id：客户ID（数字）
  - title：合同标题（字符串）
  - sign_date：签订日期（YYYY-MM-DD）
  - amount：合同金额（数字）
  - status：合同状态（履行中/已完成/作废，字符串）
  - owner：负责人（字符串）
  - remark：备注（字符串）
- **数据来源/关联**：客户表、订单表、员工表
- **实际减负**：合同履约全流程可查，减少纠纷和遗漏

### 9.4 市场活动管理
#### 市场活动管理页
- 管理市场推广、展会、客户拜访等活动，支持活动新增、编辑、查询、导出。
- **页面名称**：市场活动管理页
- **页面地址**：/marketing/activity
- **主要按钮/功能**：
  - 新建活动（按钮）
  - 编辑（按钮）
  - 删除（按钮）
  - 查询（按钮）
  - 导出（按钮）
- **接口**：
  - 新建活动：POST /api/marketing/activity/create
    - 字段：activity_no、title、type、start_date、end_date、organizer、status、remark
  - 编辑活动：POST /api/marketing/activity/update
    - 字段：activity_id、变更内容
  - 删除活动：POST /api/marketing/activity/delete
    - 字段：activity_id
  - 查询活动：GET /api/marketing/activity/list
    - 参数：activity_no、title、type、organizer、status、dateStart、dateEnd、pageNum、pageSize
  - 导出活动：GET /api/marketing/activity/export
    - 参数：activity_no、title、type、organizer、status、dateStart、dateEnd
- **字段说明**：
  - activity_id：活动ID（数字）
  - activity_no：活动编号（字符串）
  - title：活动标题（字符串）
  - type：活动类型（展会/拜访/推广等，字符串）
  - start_date：开始日期（YYYY-MM-DD）
  - end_date：结束日期（YYYY-MM-DD）
  - organizer：主办人（字符串）
  - status：活动状态（计划/进行中/已结束，字符串）
  - remark：备注（字符串）
- **数据来源/关联**：客户表、员工表、活动表
- **实际减负**：活动全流程记录，便于复盘和统计效果

---

## 10. 小程序端主要功能

### 10.1 首页
#### 首页
- 展示公司公告、待办事项、快捷入口、个人信息等。
- **页面名称**：小程序首页
- **页面地址**：/miniapp/index
- **主要按钮/功能**：
  - 公告栏（展示）
  - 待办事项（展示）
  - 快捷入口（考勤、上报、申请、我的等）
  - 个人信息（展示/跳转）
- **接口**：
  - 获取首页数据：GET /api/miniapp/index
    - 参数：user_id
- **字段说明**：
  - notice_list：公告列表（数组）
  - todo_list：待办事项（数组）
  - quick_entry：快捷入口（数组）
  - user_info：个人信息（对象）
- **数据来源/关联**：公告表、待办表、用户表
- **实际减负**：信息集中展示，提升使用效率

### 10.2 我的
#### 我的页面
- 展示个人信息、常用功能、设置等。
- **页面名称**：我的页面
- **页面地址**：/miniapp/mine
- **主要按钮/功能**：
  - 个人信息（展示/编辑）
  - 修改密码（按钮）
  - 设置（按钮）
  - 退出登录（按钮）
- **接口**：
  - 获取个人信息：GET /api/miniapp/user/info
    - 参数：user_id
  - 修改个人信息：POST /api/miniapp/user/update
    - 字段：user_id、avatar、phone、email、remark
  - 修改密码：POST /api/miniapp/user/password
    - 字段：user_id、old_password、new_password
- **字段说明**：
  - user_id：用户ID（数字）
  - avatar：头像（url）
  - phone：手机号（字符串）
  - email：邮箱（字符串）
  - remark：备注（字符串）
- **数据来源/关联**：用户表
- **实际减负**：自助维护信息，减少人工干预

### 10.3 考勤打卡
#### 考勤打卡页
- 支持上下班打卡、定位、考勤日历、异常说明。
- **页面名称**：考勤打卡页
- **页面地址**：/miniapp/attendance
- **主要按钮/功能**：
  - 上班打卡（按钮）
  - 下班打卡（按钮）
  - 考勤日历（组件）
  - 异常说明（弹窗/按钮）
- **接口**：
  - 打卡：POST /api/miniapp/attendance/punch
    - 字段：user_id、type（上班/下班）、location、time、remark
  - 获取考勤记录：GET /api/miniapp/attendance/record
    - 参数：user_id、dateStart、dateEnd
- **字段说明**：
  - attendance_id：考勤ID（数字）
  - user_id：用户ID（数字）
  - type：类型（上班/下班，字符串）
  - location：定位（字符串）
  - time：打卡时间（YYYY-MM-DD HH:mm）
  - remark：备注（字符串）
- **数据来源/关联**：考勤表、用户表
- **实际减负**：移动打卡，自动统计，减少漏打

### 10.4 请假申请
#### 请假申请页
- 支持请假类型选择、时间段选择、审批流。
- **页面名称**：请假申请页
- **页面地址**：/miniapp/leave
- **主要按钮/功能**：
  - 新建请假（按钮）
  - 历史记录（按钮）
  - 审批进度（展示）
- **接口**：
  - 新建请假：POST /api/miniapp/leave/create
    - 字段：user_id、leave_type、start_time、end_time、reason、attachment
  - 查询请假：GET /api/miniapp/leave/list
    - 参数：user_id、dateStart、dateEnd
- **字段说明**：
  - leave_id：请假ID（数字）
  - user_id：用户ID（数字）
  - leave_type：请假类型（事假/病假/年假等，字符串）
  - start_time：开始时间（YYYY-MM-DD HH:mm）
  - end_time：结束时间（YYYY-MM-DD HH:mm）
  - reason：请假原因（字符串）
  - attachment：附件（url）
- **数据来源/关联**：请假表、用户表、审批流表
- **实际减负**：移动请假，自动流转，减少纸质单据

### 10.5 出差申请
#### 出差申请页
- 支持出差类型、时间、地点、审批流。
- **页面名称**：出差申请页
- **页面地址**：/miniapp/travel
- **主要按钮/功能**：
  - 新建出差（按钮）
  - 历史记录（按钮）
  - 审批进度（展示）
- **接口**：
  - 新建出差：POST /api/miniapp/travel/create
    - 字段：user_id、travel_type、start_time、end_time、destination、reason、attachment
  - 查询出差：GET /api/miniapp/travel/list
    - 参数：user_id、dateStart、dateEnd
- **字段说明**：
  - travel_id：出差ID（数字）
  - user_id：用户ID（数字）
  - travel_type：出差类型（国内/国外，字符串）
  - start_time：开始时间（YYYY-MM-DD HH:mm）
  - end_time：结束时间（YYYY-MM-DD HH:mm）
  - destination：目的地（字符串）
  - reason：出差事由（字符串）
  - attachment：附件（url）
- **数据来源/关联**：出差表、用户表、审批流表
- **实际减负**：移动出差申请，自动流转，提升效率

### 10.6 报销申请
#### 报销申请页
- 支持多种报销类型、上传凭证、审批流。
- **页面名称**：报销申请页
- **页面地址**：/miniapp/reimburse
- **主要按钮/功能**：
  - 新建报销（按钮）
  - 历史记录（按钮）
  - 审批进度（展示）
- **接口**：
  - 新建报销：POST /api/miniapp/reimburse/create
    - 字段：user_id、reimburse_type、amount、reason、attachment
  - 查询报销：GET /api/miniapp/reimburse/list
    - 参数：user_id、dateStart、dateEnd
- **字段说明**：
  - reimburse_id：报销ID（数字）
  - user_id：用户ID（数字）
  - reimburse_type：报销类型（差旅/交通/招待等，字符串）
  - amount：金额（数字）
  - reason：报销事由（字符串）
  - attachment：附件（url）
- **数据来源/关联**：报销表、用户表、审批流表
- **实际减负**：移动报销，自动流转，提升报销效率

### 10.7 生产上报
#### 生产上报页
- 支持员工每日上报产量、工序、异常等。
- **页面名称**：生产上报页
- **页面地址**：/miniapp/report
- **主要按钮/功能**：
  - 新建上报（按钮）
  - 历史记录（按钮）
- **接口**：
  - 新建上报：POST /api/miniapp/report/create
    - 字段：user_id、date、workshop、process_node、product、output、abnormal、remark
  - 查询上报：GET /api/miniapp/report/list
    - 参数：user_id、dateStart、dateEnd
- **字段说明**：
  - report_id：上报ID（数字）
  - user_id：用户ID（数字）
  - date：日期（YYYY-MM-DD）
  - workshop：车间（字符串）
  - process_node：工序节点（字符串）
  - product：产品（字符串）
  - output：产量（数字）
  - abnormal：异常情况（字符串）
  - remark：备注（字符串）
- **数据来源/关联**：上报表、员工表、生产计划表
- **实际减负**：移动上报，自动统计，提升数据及时性

### 10.8 绩效查询
#### 绩效查询页
- 支持员工查询个人绩效、工资、奖惩等。
- **页面名称**：绩效查询页
- **页面地址**：/miniapp/performance
- **主要按钮/功能**：
  - 查询绩效（按钮）
  - 查询工资（按钮）
  - 查询奖惩（按钮）
- **接口**：
  - 查询绩效：GET /api/miniapp/performance/list
    - 参数：user_id、dateStart、dateEnd
  - 查询工资：GET /api/miniapp/salary/list
    - 参数：user_id、dateStart、dateEnd
  - 查询奖惩：GET /api/miniapp/rewardpunish/list
    - 参数：user_id、dateStart、dateEnd
- **字段说明**：
  - performance_id：绩效ID（数字）
  - salary_id：工资ID（数字）
  - rewardpunish_id：奖惩ID（数字）
  - user_id：用户ID（数字）
  - date：日期（YYYY-MM-DD）
  - amount：金额（数字）
  - type：类型（绩效/工资/奖励/惩罚，字符串）
  - remark：备注（字符串）
- **数据来源/关联**：绩效表、工资表、奖惩表、用户表
- **实际减负**：自助查询，提升透明度，减少人工答疑

### 10.9 通知公告
#### 通知公告页
- 展示公司各类通知公告，支持公告详情、已读未读。
- **页面名称**：通知公告页
- **页面地址**：/miniapp/notice
- **主要按钮/功能**：
  - 公告列表（展示）
  - 公告详情（跳转）
  - 标记已读（按钮）
- **接口**：
  - 查询公告：GET /api/miniapp/notice/list
    - 参数：user_id、dateStart、dateEnd、read_status
  - 公告详情：GET /api/miniapp/notice/detail
    - 参数：notice_id
  - 标记已读：POST /api/miniapp/notice/read
    - 字段：user_id、notice_id
- **字段说明**：
  - notice_id：公告ID（数字）
  - title：标题（字符串）
  - content：内容（字符串）
  - publish_time：发布时间（YYYY-MM-DD HH:mm）
  - read_status：已读状态（已读/未读，字符串）
- **数据来源/关联**：公告表、用户表
- **实际减负**：移动通知，及时触达，减少遗漏

### 10.10 文件中心
#### 文件中心页
- 管理公司文件、资料，支持上传、下载、分类、查询。
- **页面名称**：文件中心页
- **页面地址**：/miniapp/file
- **主要按钮/功能**：
  - 上传文件（按钮）
  - 下载文件（按钮）
  - 分类（下拉）
  - 查询（按钮）
- **接口**：
  - 上传文件：POST /api/miniapp/file/upload
    - 字段：file_name、category、uploader、upload_time、attachment、remark
  - 查询文件：GET /api/miniapp/file/list
    - 参数：file_name、category、uploader、dateStart、dateEnd、pageNum、pageSize
  - 下载文件：GET /api/miniapp/file/download
    - 参数：file_id
- **字段说明**：
  - file_id：文件ID（数字）
  - file_name：文件名（字符串）
  - category：分类（字符串）
  - uploader：上传人（字符串）
  - upload_time：上传时间（YYYY-MM-DD HH:mm）
  - attachment：附件（url）
  - remark：备注（字符串）
- **数据来源/关联**：文件表、用户表
- **实际减负**：文件移动管理，便于查找和共享

---

## 11. 运营链路
- 客户询问→产品打样→下单→排产→制令单→各车间生产→中转→组装→灭菌→仓库→发货→客户收货。

---

> 如需补充具体页面、字段、接口、数据表结构等，请告知！如需导出为Word，将自动转换格式。 