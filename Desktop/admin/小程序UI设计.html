<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>企业员工服务系统</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;500;700&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Noto Sans SC', sans-serif;
        }
        
        /* 企业级专业配色方案（基于搜索结果优化） */
        :root {
            /* 主色调：深蓝色 - 传达专业、可靠的企业形象:cite[3]:cite[7] */
            --primary: #409EFF;
            /* 辅助色：金色 - 提升活力与品质感:cite[8] */
            --accent: #d4af37;
            /* 背景色：浅灰蓝 - 降低视觉疲劳:cite[6] */
            --background: #f0f4f8;
            /* 文字主色：深灰 - 保证可读性:cite[5] */
            --text-primary: #2d3748;
            /* 文字辅助色 */
            --text-secondary: #4a5568;
            /* 卡片背景：纯白 - 突出内容层次:cite[2] */
            --card-bg: #ffffff;
            /* 成功色 */
            --success: #38a169;
            /* 警告色 */
            --warning: #dd6b20;
            /* 错误色 */
            --error: #e53e3e;
        }
        
        body {
            background-color: var(--background);
            color: var(--text-primary);
            min-height: 812px;
            position: relative;
            overflow-x: hidden;
            padding-bottom: 70px;
        }
        
        /* 主容器 */
        .app-container {
            max-width: 500px;
            margin: 0 auto;
            background: var(--card-bg);
            border-radius: 20px;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
            min-height: 812px;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
        }
        
        /* 顶部导航 */
        .header {
            background: var(--primary);
            color: white;
            padding: 25px 20px 15px;
            text-align: center;
            position: relative;
        }
        
        .logo {
            display: flex;
            align-items: center;
            justify-content: center;
            min-height: 44px;
        }
        
        .logo-icon {
            width: 50px;
            height: 50px;
            background: rgba(255, 255, 255, 0.15);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 12px;
        }
        
        .logo-icon i {
            font-size: 26px;
            color: var(--accent);
        }
        
        .logo-text h1 {
            font-size: 22px;
            font-weight: 700;
            letter-spacing: 0.5px;
        }
        
        .user-avatar {
            position: absolute;
            top: 25px;
            right: 20px;
            width: 44px;
            height: 44px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.15);
            display: flex;
            align-items: center;
            justify-content: center;
            overflow: hidden;
        }
        
        .user-avatar img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        
        /* 主内容区 */
        .main-content {
            padding: 20px;
            flex: 1 0 auto;
        }
        
        /* 功能卡片 */
        .card-container {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 10px;
            margin-bottom: 25px;
        }
        
        .card {
            background: var(--card-bg);
            border-radius: 12px;
            padding: 12px 10px;
            text-align: center;
            transition: all 0.3s ease;
            cursor: pointer;
            border: 1px solid #e2e8f0;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
        }
        
        .card:hover {
            transform: translateY(-5px);
            border-color: var(--accent);
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
        }
        
        .card i {
            font-size: 22px;
            color: var(--primary);
            margin-bottom: 8px;
            display: block;
        }
        
        .card h3 {
            font-size: 14px;
            font-weight: 500;
            color: var(--text-primary);
        }
        
        /* 我的区域 */
        .profile {
            display: flex;
            align-items: center;
            padding: 12px;
            background: var(--card-bg);
            border-radius: 16px;
            margin-bottom: 20px;
            border: 1px solid #e2e8f0;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
        }
        
        .profile-avatar {
            width: 48px;
            height: 48px;
            border-radius: 50%;
            background: #edf2f7;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 12px;
            overflow: hidden;
            border: 2px solid var(--accent);
        }
        
        .profile-avatar img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        
        .profile-info h2 {
            font-size: 15px;
            font-weight: 600;
            margin-bottom: 5px;
            color: var(--text-primary);
        }
        
        .profile-info p {
            font-size: 12px;
            color: var(--text-secondary);
            margin-bottom: 8px;
        }
        
        .badge {
            display: inline-block;
            background: rgba(212, 175, 55, 0.15);
            color: var(--accent);
            padding: 2px 8px;
            border-radius: 20px;
            font-size: 11px;
            font-weight: 500;
        }
        
        /* 薪资卡片 */
        .salary-card {
            background: linear-gradient(135deg, var(--primary), #0a2e5c);
            padding: 12px;
            border-radius: 12px;
            margin: 12px 0;
            color: white;
        }
        
        .salary-header {
            display: flex;
            justify-content: space-between;
            margin-bottom: 18px;
        }
        
        .salary-header h3 {
            font-size: 14px;
            font-weight: 600;
        }
        
        .salary-header p {
            font-size: 12px;
        }
        
        .salary-amount {
            font-size: 22px;
            font-weight: 700;
            text-align: center;
            margin: 10px 0;
            letter-spacing: 1px;
        }
        
        .salary-details {
            display: flex;
            justify-content: space-between;
            margin-top: 15px;
        }
        
        .detail-item {
            text-align: center;
        }
        
        .detail-item p:first-child {
            font-size: 12px;
            margin-bottom: 5px;
            opacity: 0.8;
        }
        
        .detail-item p:last-child {
            font-size: 13px;
            font-weight: 500;
        }
        
        /* 底部导航 */
        .tab-bar {
            position: fixed;
            left: 50%;
            bottom: 0;
            transform: translateX(-50%);
            width: 100%;
            max-width: 500px;
            z-index: 100;
            box-shadow: 0 -2px 12px rgba(0,0,0,0.04);
            background: var(--card-bg);
            display: flex;
            padding: 6px 0;
            flex-shrink: 0;
        }
        
        .tab-item {
            flex: 1;
            display: flex;
            flex-direction: column;
            align-items: center;
            font-size: 11px;
            color: var(--text-secondary);
            cursor: pointer;
            transition: all 0.3s;
            padding: 5px 0;
        }
        
        .tab-item.active {
            color: var(--primary);
        }
        
        .tab-item i {
            font-size: 16px;
            margin-bottom: 2px;
            transition: all 0.3s;
        }
        
        .tab-item.active i {
            color: var(--accent);
        }
        
        .tab-indicator {
            position: absolute;
            bottom: 0;
            height: 3px;
            width: 33.333%;
            background: var(--accent);
            border-radius: 10px;
            transition: all 0.3s ease;
        }
        
        /* 状态标签 */
        .status-tag {
            display: inline-block;
            padding: 3px 10px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }
        
        .status-approved {
            background: rgba(56, 161, 105, 0.15);
            color: var(--success);
        }
        
        .status-pending {
            background: rgba(221, 107, 32, 0.15);
            color: var(--warning);
        }
        
        /* 页面切换效果 */
        .page {
            display: none;
            animation: fadeIn 0.5s ease;
        }
        
        .page.active {
            display: block;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        .section-title {
            font-size: 15px;
            font-weight: 600;
            margin: 14px 0 10px;
            color: var(--primary);
            display: flex;
            align-items: center;
        }
        
        .section-title i {
            margin-right: 10px;
            color: var(--accent);
        }
        
        /* 响应式调整 */
        @media (max-width: 480px) {
            .card-container {
                grid-template-columns: 1fr;
            }
            
            .profile {
                padding: 15px;
            }
            
            .profile-avatar {
                width: 60px;
                height: 60px;
            }
            body, .app-container {
                min-height: 812px;
            }
        }
        
        .card[style*="display:flex"] {
            padding: 10px 10px;
        }
        
        /* 个人设置卡片并排展示 */
        .profile-settings.card-container {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
            margin-bottom: 18px;
        }
        .profile-settings .card {
            min-width: 0;
            flex: 1 1 0%;
            margin: 0;
        }
        /* 申请服务卡片并排展示 */
        .apply-services.card-container {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
            margin-bottom: 18px;
        }
        .apply-services .card {
            min-width: 0;
            flex: 1 1 0%;
            margin: 0;
        }
        /* 生产上报卡片并排展示 */
        .production-report.card-container {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
            margin-bottom: 18px;
        }
        .production-report .card {
            min-width: 0;
            flex: 1 1 0%;
            margin: 0;
        }
        /* 薪资记录横向排列，最小高度70px */
        .salary-record-list .card {
            display: flex;
            align-items: center;
            min-height: 80px;
            padding: 0 16px;
            margin-bottom: 12px;
            justify-content: space-between;
        }
        .salary-record-list .record-left {
            display: flex;
            align-items: center;
            gap: 12px;
        }
        .salary-record-list .record-icon {
            font-size: 22px;
            color: var(--accent);
            margin-right: 8px;
        }
        .salary-record-list .record-title {
            font-size: 15px;
            font-weight: 500;
            color: var(--text-primary);
        }
        .salary-record-list .record-amount {
            font-size: 16px;
            font-weight: 600;
            color: var(--accent);
        }
        /* 打卡日历弹窗样式 */
        .attendance-calendar-modal {
            position: fixed;
            left: 0; top: 0; right: 0; bottom: 0;
            background: rgba(0,0,0,0.18);
            z-index: 9999;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .attendance-calendar-box {
            margin-left: auto;
            margin-right: auto;
            /* width: 400px; */
            /* max-width: 98vw; */
            background: #fff;
            border-radius: 22px;
            box-shadow: 0 6px 32px rgba(0,0,0,0.10);
            padding: 24px 18px 18px 18px;
        }
        .calendar-header {
            display: flex;
            align-items: center;
            justify-content: flex-start;
            margin-bottom: 8px;
            gap: 16px;
        }
        .calendar-user {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .calendar-user-avatar {
            width: 40px; height: 40px; border-radius: 50%; background: #eaf3ff; overflow: hidden;
            box-shadow: 0 2px 8px rgba(64,158,255,0.08);
        }
        .calendar-user-avatar img { width: 100%; height: 100%; object-fit: cover; }
        .calendar-user-name { font-weight: 700; font-size: 16px; color: #222; }
        .calendar-switch {
            background: #f5f6fa;
            border-radius: 10px;
            display: flex;
            overflow: hidden;
            font-size: 14px;
            margin-left: 18px;
        }
        .calendar-switch-btn {
            padding: 4px 18px;
            border: none;
            background: none;
            color: #888;
            cursor: pointer;
            font-weight: 500;
            border-radius: 8px;
            transition: background 0.2s, color 0.2s;
        }
        .calendar-switch-btn.active {
            background: #eaf3ff;
            color: var(--primary);
        }
        .calendar-date-bar {
            font-size: 17px;
            font-weight: 700;
            color: #222;
            margin: 10px 0 8px 0;
            letter-spacing: 1px;
        }
        .calendar-status-tip {
            background: #f8fafd;
            border-radius: 14px;
            padding: 10px 16px;
            font-size: 12px;
            color: #666;
            margin-bottom: 14px;
            display: flex;
            flex-direction: column;
            gap: 4px;
        }
        .calendar-status-tip .legend {
            display: flex;
            align-items: center;
            gap: 6px;
            margin-bottom: 2px;
        }
        .legend-dot {
            width: 10px; height: 10px; border-radius: 50%; display: inline-block;
            margin-right: 4px;
        }
        .legend-normal { background: #409eff; }
        .legend-exception { background: #ff9900; }
        .legend-apply { background: #000; }
        .calendar-dot{
            width: 5px; height: 5px; border-radius: 50%; display: inline-block;
        }
        .dot-normal { background: #409eff; }
        .dot-exception { background: #ff9900; }
        .dot-leave { background: #000; }
        .calendar-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 8px;
        }
        .calendar-table td, .calendar-table th {
            width: 40px;
            height: 40px;
            text-align: center;
            font-size: 17px;
            border-radius: 50%;
            position: relative;
            vertical-align: middle;
        }
        .calendar-table th {
            color: #aaa;
            font-weight: 400;
            font-size: 14px;
        }
        .calendar-table td {
            position: relative;
            padding-bottom: 0;
            font-size: 18px;
            vertical-align: top;
        }
        .calendar-cell-inner {
            position: relative;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            width: 40px;
            height: 40px;
        }
        .calendar-active-circle {
            position: absolute;
            left: 0; top: 0;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #409EFF;
            z-index: 1;
        }
        .calendar-cell-num {
            font-size: 12px;
            line-height: 1.1;
            margin-bottom: 5px;
            font-weight: 500;
            position: relative;
            z-index: 2;
            color: inherit;
        }
        .calendar-dot {
            position: relative;
            z-index: 2;
        }
        .calendar-table td.active .calendar-cell-num {
            color: #fff;
            font-weight: 700;
        }
        .calendar-table td.active {
            background: none;
            color: inherit;
            border-radius: 0;
            width: auto;
            height: auto;
            aspect-ratio: unset;
            padding: 0;
        }
        .calendar-close {
            position: absolute;
            right: 16px; top: 16px;
            font-size: 18px;
            color: #bbb;
            cursor: pointer;
        }
    </style>
</head>
<body>
    <div class="app-container">
        <!-- 顶部导航 -->
        <div class="header">
            <div class="logo">
                <!-- <div class="logo-icon">
                    <i class="fas fa-building"></i>
                </div> -->
                <div class="logo-text">
                    <h1>格莱恩生产管理系统</h1>
                </div>
            </div>
            <div class="user-avatar">
                <img src="https://randomuser.me/api/portraits/men/32.jpg" alt="用户头像">
            </div>
        </div>
        
        <!-- 主内容区 -->
        <div class="main-content">
            <!-- 首页/考勤 -->
            <div id="attendancePage" class="page active">
                <div class="section-title">
                    <i class="fas fa-calendar-check"></i>
                    <h2>考勤打卡</h2>
                </div>
                <div class="card-container" style="grid-template-columns: repeat(2, 1fr); margin-bottom: 18px;">
                    <div class="card">
                        <i class="fas fa-sun"></i>
                        <h3>早班</h3>
                        <div style="font-size:13px;color:var(--text-secondary);margin-top:6px;">08:00 - 20:00</div>
                    </div>
                    <div class="card">
                        <i class="fas fa-moon"></i>
                        <h3>晚班</h3>
                        <div style="font-size:13px;color:var(--text-secondary);margin-top:6px;">20:00 - 次日08:00</div>
                    </div>
                    <div class="card">
                        <i class="fas fa-business-time"></i>
                        <h3>加班打卡</h3>
                        <div style="font-size:13px;color:var(--text-secondary);margin-top:6px;">时间不固定</div>
                    </div>
                    <div class="card">
                        <i class="fas fa-user-clock"></i>
                        <h3>自由打卡</h3>
                        <div style="font-size:13px;color:var(--text-secondary);margin-top:6px;">时间不固定</div>
                    </div>
                </div>
                <div class="card" style="margin-bottom:18px;">
                    <button style="width:100%;background:var(--primary);color:#fff;font-size:18px;padding:12px 0;border:none;border-radius:8px;">立即打卡</button>
                </div>
                <div class="card-container" style="grid-template-columns: repeat(3, 1fr);">
                    <div class="card">
                        <div style="font-size:22px;color:var(--primary);font-weight:700;">22</div>
                        <div style="font-size:13px;color:var(--text-secondary);">出勤天数</div>
                    </div>
                    <div class="card">
                        <div style="font-size:22px;color:var(--primary);font-weight:700;">176</div>
                        <div style="font-size:13px;color:var(--text-secondary);">工作时长(h)</div>
                    </div>
                    <div class="card">
                        <div style="font-size:22px;color:var(--primary);font-weight:700;">0</div>
                        <div style="font-size:13px;color:var(--text-secondary);">迟到次数</div>
                    </div>
                </div>

                <div class="attendance-calendar-box">
                    <div class="calendar-header">
                        <div class="calendar-user">
                            <div class="calendar-user-avatar"><img src="https://randomuser.me/api/portraits/men/32.jpg"></div>
                            <span class="calendar-user-name">张伟</span>
                        </div>
                        <div class="calendar-switch">
                            <button class="calendar-switch-btn active">日</button>
                            <button class="calendar-switch-btn">周</button>
                            <button class="calendar-switch-btn">月</button>
                        </div>
                    </div>
                    <div class="calendar-date-bar">2025&nbsp;&nbsp;06.26</div>
                    <div class="calendar-status-tip">
                        <div class="legend"><span class="legend-dot legend-normal"></span>全天考勤正常</div>
                        <div class="legend"><span class="legend-dot legend-exception"></span><span style="color:#ff9900;">当天存在异常：迟到、早退、缺卡</span></div>
                        <div class="legend"><span class="legend-dot legend-apply"></span>当天提交过：请假、加班、出差、外出、补卡申请</div>
                    </div>
                    <table class="calendar-table">
                        <thead>
                            <tr><th>一</th><th>二</th><th>三</th><th>四</th><th>五</th><th>六</th><th>日</th></tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td></td><td></td>
                                <td><div class="calendar-cell-inner"><span class="calendar-cell-num">28</span><span class="calendar-dot dot-normal"></span></div></td>
                                <td><div class="calendar-cell-inner"><span class="calendar-cell-num">29</span><span class="calendar-dot dot-normal"></span></div></td>
                                <td><div class="calendar-cell-inner"><span class="calendar-cell-num">30</span><span class="calendar-dot dot-exception"></span></div></td>
                                <td><div class="calendar-cell-inner"><span class="calendar-cell-num">31</span><span class="calendar-dot dot-leave"></span></div></td>
                                <td><div class="calendar-cell-inner"><span class="calendar-cell-num">1</span><span class="calendar-dot dot-normal"></span></div></td>
                            </tr>
                            <tr>
                                <td><div class="calendar-cell-inner"><span class="calendar-cell-num">2</span><span class="calendar-dot dot-normal"></span></div></td>
                                <td><div class="calendar-cell-inner"><span class="calendar-cell-num">3</span><span class="calendar-dot dot-exception"></span></div></td>
                                <td><div class="calendar-cell-inner"><span class="calendar-cell-num">4</span><span class="calendar-dot dot-normal"></span></div></td>
                                <td><div class="calendar-cell-inner"><span class="calendar-cell-num">5</span><span class="calendar-dot dot-normal"></span></div></td>
                                <td><div class="calendar-cell-inner"><span class="calendar-cell-num">6</span><span class="calendar-dot dot-normal"></span></div></td>
                                <td><div class="calendar-cell-inner"><span class="calendar-cell-num">7</span><span class="calendar-dot dot-leave"></span></div></td>
                                <td><div class="calendar-cell-inner"><span class="calendar-cell-num">8</span><span class="calendar-dot dot-normal"></span></div></td>
                            </tr>
                            <tr>
                                <td><div class="calendar-cell-inner"><span class="calendar-cell-num">9</span><span class="calendar-dot dot-normal"></span></div></td>
                                <td><div class="calendar-cell-inner"><span class="calendar-cell-num">10</span><span class="calendar-dot dot-exception"></span></div></td>
                                <td><div class="calendar-cell-inner"><span class="calendar-cell-num">11</span><span class="calendar-dot dot-normal"></span></div></td>
                                <td><div class="calendar-cell-inner"><span class="calendar-cell-num">12</span><span class="calendar-dot dot-normal"></span></div></td>
                                <td><div class="calendar-cell-inner"><span class="calendar-cell-num">13</span><span class="calendar-dot dot-normal"></span></div></td>
                                <td><div class="calendar-cell-inner"><span class="calendar-cell-num">14</span><span class="calendar-dot dot-normal"></span></div></td>
                                <td><div class="calendar-cell-inner"><span class="calendar-cell-num">15</span><span class="calendar-dot dot-normal"></span></div></td>
                            </tr>
                            <tr>
                                <td><div class="calendar-cell-inner"><span class="calendar-cell-num">16</span><span class="calendar-dot dot-normal"></span></div></td>
                                <td><div class="calendar-cell-inner"><span class="calendar-cell-num">17</span><span class="calendar-dot dot-normal"></span></div></td>
                                <td><div class="calendar-cell-inner"><span class="calendar-cell-num">18</span><span class="calendar-dot dot-normal"></span></div></td>
                                <td><div class="calendar-cell-inner"><span class="calendar-cell-num">19</span><span class="calendar-dot dot-normal"></span></div></td>
                                <td><div class="calendar-cell-inner"><span class="calendar-cell-num">20</span><span class="calendar-dot dot-normal"></span></div></td>
                                <td><div class="calendar-cell-inner"><span class="calendar-cell-num">21</span><span class="calendar-dot dot-normal"></span></div></td>
                                <td><div class="calendar-cell-inner"><span class="calendar-cell-num">22</span><span class="calendar-dot dot-normal"></span></div></td>
                            </tr>
                            <tr>
                                <td><div class="calendar-cell-inner"><span class="calendar-cell-num">23</span><span class="calendar-dot dot-normal"></span></div></td>
                                <td><div class="calendar-cell-inner"><span class="calendar-cell-num">24</span><span class="calendar-dot dot-normal"></span></div></td>
                                <td><div class="calendar-cell-inner"><span class="calendar-cell-num">25</span><span class="calendar-dot dot-normal"></span></div></td>
                                <td class="active"><div class="calendar-cell-inner"><span class="calendar-active-circle"></span><span class="calendar-cell-num">26</span><span class="calendar-dot dot-normal"></span></div></td>
                                <td><div class="calendar-cell-inner"><span class="calendar-cell-num">27</span></div></td>
                                <td><div class="calendar-cell-inner"><span class="calendar-cell-num">28</span></div></td>
                                <td><div class="calendar-cell-inner"><span class="calendar-cell-num">29</span></div></td>
                            </tr>
                            <tr>
                                <td><div class="calendar-cell-inner"><span class="calendar-cell-num">30</span></div></td>
                                <td><div class="calendar-cell-inner"><span class="calendar-cell-num">1</span></div></td>
                                <td><div class="calendar-cell-inner"><span class="calendar-cell-num">2</span></div></td>
                                <td><div class="calendar-cell-inner"><span class="calendar-cell-num">3</span></div></td>
                                <td><div class="calendar-cell-inner"><span class="calendar-cell-num">4</span></div></td>
                                <td><div class="calendar-cell-inner"><span class="calendar-cell-num">5</span></div></td>
                                <td><div class="calendar-cell-inner"><span class="calendar-cell-num">6</span></div></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            <!-- 上报 -->
            <div id="reportPage" class="page">
                <div class="section-title">
                    <i class="fas fa-industry"></i>
                    <h2>生产</h2>
                </div>
                <div class="card-container production-report">
                    <div class="card">
                        <i class="fas fa-cubes"></i>
                        <h3>产量上报</h3>
                    </div>
                    <div class="card">
                        <i class="fas fa-check-circle"></i>
                        <h3>质量上报</h3>
                    </div>
                    <div class="card">
                        <i class="fas fa-exclamation-triangle"></i>
                        <h3>故障上报</h3>
                    </div>
                    <div class="card">
                        <i class="fas fa-barcode"></i>
                        <h3>批号上报</h3>
                    </div>
                </div>
                <div class="section-title">
                    <i class="fas fa-history"></i>
                    <h2>最近上报记录</h2>
                </div>
                <div class="card">
                    <p><i class="fas fa-boxes" style="color: var(--primary); margin-right: 8px;"></i> <strong>产量上报</strong> · 今日 08:30</p>
                    <p style="margin-top: 10px; padding-left: 26px; font-size: 14px; color: var(--text-secondary);">产品A：1200件 | 完成率 98%</p>
                </div>
                <div class="card" style="margin-top: 15px;">
                    <p><i class="fas fa-exclamation-triangle" style="color: var(--warning); margin-right: 8px;"></i> <strong>故障上报</strong> · 昨天 14:20</p>
                    <p style="margin-top: 10px; padding-left: 26px; font-size: 14px; color: var(--text-secondary);">设备B-02异常 | 处理中</p>
                </div>
            </div>
            <!-- 申请 -->
            <div id="applyPage" class="page">
                <div class="section-title">
                    <i class="fas fa-file-signature"></i>
                    <h2>申请服务</h2>
                </div>
                <div class="card-container apply-services">
                    <div class="card">
                        <i class="fas fa-business-time"></i>
                        <h3>加班申请</h3>
                    </div>
                    <div class="card">
                        <i class="fas fa-umbrella-beach"></i>
                        <h3>请假申请</h3>
                    </div>
                    <div class="card">
                        <i class="fas fa-sign-out-alt"></i>
                        <h3>离职申请</h3>
                    </div>
                    <div class="card">
                        <i class="fas fa-exchange-alt"></i>
                        <h3>调岗申请</h3>
                    </div>
                    <div class="card">
                        <i class="fas fa-shopping-cart"></i>
                        <h3>采购申请</h3>
                    </div>
                    <div class="card">
                        <i class="fas fa-box-open"></i>
                        <h3>领用申请</h3>
                    </div>
                </div>
                <div class="section-title">
                    <i class="fas fa-tasks"></i>
                    <h2>申请进度</h2>
                </div>
                <div class="card">
                    <p><i class="fas fa-business-time" style="color: var(--primary); margin-right: 8px;"></i> 加班申请 <span class="status-tag status-pending">审批中</span></p>
                    <p style="margin-top: 10px; padding-left: 26px; font-size: 14px; color: var(--text-secondary);">2023-06-16 · 2小时 · 部门主管审核</p>
                </div>
                <div class="card" style="margin-top: 15px;">
                    <p><i class="fas fa-umbrella-beach" style="color: var(--primary); margin-right: 8px;"></i> 请假申请 <span class="status-tag status-approved">已通过</span></p>
                    <p style="margin-top: 10px; padding-left: 26px; font-size: 14px; color: var(--text-secondary);">2023-06-15 · 事假2天 · 已批准</p>
                </div>
            </div>
            <!-- 审批 -->
            <div id="approvalPage" class="page">
                <div class="section-title">
                    <i class="fas fa-stamp"></i>
                    <h2>待我审批</h2>
                </div>
                <div class="card" style="display:flex;align-items:center;justify-content:space-between;margin-bottom:12px;">
                    <div>
                        <div style="font-weight:600;font-size:15px;"><i class="fas fa-business-time" style="color:var(--primary);margin-right:8px;"></i>加班申请</div>
                        <div style="font-size:13px;color:var(--text-secondary);margin-top:2px;">申请人：李明 &nbsp; 2023-06-18 18:00</div>
                    </div>
                    <span class="status-tag status-pending">待审批</span>
                </div>
                <div class="card" style="display:flex;align-items:center;justify-content:space-between;margin-bottom:12px;">
                    <div>
                        <div style="font-weight:600;font-size:15px;"><i class="fas fa-umbrella-beach" style="color:var(--primary);margin-right:8px;"></i>请假申请</div>
                        <div style="font-size:13px;color:var(--text-secondary);margin-top:2px;">申请人：王芳 &nbsp; 2023-06-17 09:30</div>
                    </div>
                    <span class="status-tag status-pending">待审批</span>
                </div>
                <div class="card" style="display:flex;align-items:center;justify-content:space-between;margin-bottom:12px;">
                    <div>
                        <div style="font-weight:600;font-size:15px;"><i class="fas fa-exchange-alt" style="color:var(--primary);margin-right:8px;"></i>调岗申请</div>
                        <div style="font-size:13px;color:var(--text-secondary);margin-top:2px;">申请人：赵强 &nbsp; 2023-06-16 15:20</div>
                    </div>
                    <span class="status-tag status-pending">待审批</span>
                </div>
            </div>
            <!-- 我的 -->
            <div id="profilePage" class="page">
                <div class="profile">
                    <div class="profile-avatar">
                        <img src="https://randomuser.me/api/portraits/men/32.jpg" alt="用户头像">
                    </div>
                    <div class="profile-info">
                        <h2>张伟</h2>
                        <p>生产部 · 高级工程师 | 工号: E20230085</p>
                        <span class="badge">技术骨干</span>
                    </div>
                </div>
                <div class="section-title">
                    <i class="fas fa-user-cog"></i>
                    <h2>个人中心</h2>
                </div>
                <div class="card-container profile-settings">
                    <div class="card">
                        <i class="fas fa-bell"></i>
                        <h3>通知</h3>
                    </div>
                    <div class="card">
                        <i class="fas fa-edit"></i>
                        <h3>修改昵称</h3>
                    </div>
                    <div class="card">
                        <i class="fas fa-file-contract"></i>
                        <h3>入职资料</h3>
                    </div>
                    <div class="card">
                        <i class="fas fa-money-check-alt"></i>
                        <h3>薪资查看</h3>
                    </div>
                </div>
                <div class="salary-card">
                    <div class="salary-header">
                        <h3>本月薪资</h3>
                        <p>2023年6月</p>
                    </div>
                    <div class="salary-amount">¥15,850.00</div>
                    <div class="salary-details">
                        <div class="detail-item">
                            <p>基本工资</p>
                            <p>¥10,500.00</p>
                        </div>
                        <div class="detail-item">
                            <p>绩效奖金</p>
                            <p>¥3,500.00</p>
                        </div>
                        <div class="detail-item">
                            <p>其他补贴</p>
                            <p>¥1,850.00</p>
                        </div>
                    </div>
                </div>
                <div class="section-title">
                    <i class="fas fa-file-invoice-dollar"></i>
                    <h2>薪资记录</h2>
                </div>
                <div class="salary-record-list">
                    <div class="card">
                        <div class="record-left">
                            <i class="fas fa-file-alt record-icon" style="margin-bottom: 0px;"></i>
                            <span class="record-title">2023年5月工资单</span>
                        </div>
                        <span class="record-amount">¥15,200.00</span>
                    </div>
                    <div class="card">
                        <div class="record-left">
                            <i class="fas fa-file-alt record-icon" style="margin-bottom: 0px;"></i>
                            <span class="record-title">2023年4月工资单</span>
                        </div>
                        <span class="record-amount">¥14,950.00</span>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 底部导航 -->
        <div class="tab-bar">
            <div class="tab-item active" data-page="attendancePage">
                <i class="fas fa-calendar-check"></i>
                <span>考勤</span>
            </div>
            <div class="tab-item" data-page="reportPage">
                <i class="fas fa-industry"></i>
                <span>生产</span>
            </div>
            <div class="tab-item" data-page="applyPage">
                <i class="fas fa-file-signature"></i>
                <span>申请</span>
            </div>
            <div class="tab-item" data-page="approvalPage">
                <i class="fas fa-stamp"></i>
                <span>审批</span>
            </div>
            <div class="tab-item" data-page="profilePage">
                <i class="fas fa-user"></i>
                <span>我的</span>
            </div>
        </div>
    </div>
    
    <script>
        // 页面切换功能
        const tabItems = document.querySelectorAll('.tab-item');
        const pages = [
            document.getElementById('attendancePage'),
            document.getElementById('reportPage'),
            document.getElementById('applyPage'),
            document.getElementById('approvalPage'),
            document.getElementById('profilePage')
        ];
        
        tabItems.forEach((item, idx) => {
            item.addEventListener('click', function() {
                tabItems.forEach(tab => tab.classList.remove('active'));
                this.classList.add('active');
                pages.forEach((page, i) => {
                    if (i === idx) {
                        page.classList.add('active');
                    } else {
                        page.classList.remove('active');
                    }
                });
            });
        });
        
        // 添加卡片点击效果
        const cards = document.querySelectorAll('.card');
        cards.forEach(card => {
            card.addEventListener('click', function() {
                this.style.transform = 'scale(0.95)';
                setTimeout(() => {
                    this.style.transform = '';
                }, 200);
            });
        });
    </script>
</body>
</html>