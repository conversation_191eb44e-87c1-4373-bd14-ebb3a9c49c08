{"auditReportVersion": 2, "vulnerabilities": {"@vueup/vue-quill": {"name": "@vueup/vue-quill", "severity": "moderate", "isDirect": true, "via": ["quill"], "effects": [], "range": "*", "nodes": ["node_modules/@vueup/vue-quill"], "fixAvailable": false}, "quill": {"name": "quill", "severity": "moderate", "isDirect": false, "via": [{"source": 1098574, "name": "quill", "dependency": "quill", "title": "Cross-site Scripting in quill", "url": "https://github.com/advisories/GHSA-4943-9vgg-gr5r", "severity": "moderate", "cwe": ["CWE-79"], "cvss": {"score": 4.2, "vectorString": "CVSS:3.1/AV:N/AC:H/PR:L/UI:N/S:U/C:L/I:L/A:N"}, "range": "<=1.3.7"}], "effects": ["@vueup/vue-quill"], "range": "<=1.3.7", "nodes": ["node_modules/quill"], "fixAvailable": false}}, "metadata": {"vulnerabilities": {"info": 0, "low": 0, "moderate": 2, "high": 0, "critical": 0, "total": 2}, "dependencies": {"prod": 171, "dev": 324, "optional": 3, "peer": 0, "peerOptional": 0, "total": 495}}}