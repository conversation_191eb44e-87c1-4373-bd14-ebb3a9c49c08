{"auditReportVersion": 2, "vulnerabilities": {"@vueup/vue-quill": {"name": "@vueup/vue-quill", "severity": "moderate", "isDirect": true, "via": ["quill"], "effects": [], "range": "*", "nodes": ["node_modules/@vueup/vue-quill"], "fixAvailable": false}, "axios": {"name": "axios", "severity": "high", "isDirect": true, "via": [{"source": 1097679, "name": "axios", "dependency": "axios", "title": "Axios Cross-Site Request Forgery Vulnerability", "url": "https://github.com/advisories/GHSA-wf5p-g6vw-rhxx", "severity": "moderate", "cwe": ["CWE-352"], "cvss": {"score": 6.5, "vectorString": "CVSS:3.1/AV:N/AC:L/PR:N/UI:R/S:U/C:H/I:N/A:N"}, "range": ">=0.8.1 <0.28.0"}, {"source": 1103617, "name": "axios", "dependency": "axios", "title": "axios Requests Vulnerable To Possible SSRF and Credential Leakage via Absolute URL", "url": "https://github.com/advisories/GHSA-jr5f-v2jv-69x6", "severity": "high", "cwe": ["CWE-918"], "cvss": {"score": 0, "vectorString": null}, "range": "<0.30.0"}], "effects": [], "range": "<=0.29.0", "nodes": ["node_modules/axios"], "fixAvailable": {"name": "axios", "version": "1.10.0", "isSemVerMajor": true}}, "quill": {"name": "quill", "severity": "moderate", "isDirect": false, "via": [{"source": 1098574, "name": "quill", "dependency": "quill", "title": "Cross-site Scripting in quill", "url": "https://github.com/advisories/GHSA-4943-9vgg-gr5r", "severity": "moderate", "cwe": ["CWE-79"], "cvss": {"score": 4.2, "vectorString": "CVSS:3.1/AV:N/AC:H/PR:L/UI:N/S:U/C:L/I:L/A:N"}, "range": "<=1.3.7"}], "effects": ["@vueup/vue-quill"], "range": "<=1.3.7", "nodes": ["node_modules/quill"], "fixAvailable": false}}, "metadata": {"vulnerabilities": {"info": 0, "low": 0, "moderate": 2, "high": 1, "critical": 0, "total": 3}, "dependencies": {"prod": 170, "dev": 324, "optional": 3, "peer": 0, "peerOptional": 0, "total": 494}}}