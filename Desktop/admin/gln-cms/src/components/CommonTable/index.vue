<template>
  <div>
    <el-table :data="data" v-loading="loading" @selection-change="$emit('selection-change', $event)">
      <el-table-column v-if="showSelection" type="selection" width="55" align="center" />
      <el-table-column
        v-for="col in columns"
        :key="col.prop"
        v-bind="col"
        :show-overflow-tooltip="col.showOverflowTooltip !== false"
      >
        <template v-if="col.slot" #default="scope">
          <slot :name="col.slot" v-bind="scope" />
        </template>
      </el-table-column>
      <el-table-column v-if="$slots.operate" label="操作" align="center" class-name="small-padding fixed-width">
        <template #default="scope">
          <slot name="operate" v-bind="scope" />
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      :page="pageNum"
      :limit="pageSize"
      @update:page="val => $emit('update:pageNum', val)"
      @update:limit="val => $emit('update:pageSize', val)"
      @pagination="$emit('pagination')"
    />
  </div>
</template>

<script setup>
const props = defineProps({
  columns: { type: Array, required: true },
  data: { type: Array, required: true },
  loading: { type: Boolean, default: false },
  total: { type: Number, default: 0 },
  pageNum: { type: Number, default: 1 },
  pageSize: { type: Number, default: 10 },
  showSelection: { type: Boolean, default: true }
})
</script> 