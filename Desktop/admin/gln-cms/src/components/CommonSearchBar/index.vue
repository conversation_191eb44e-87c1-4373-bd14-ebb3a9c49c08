<template>
  <el-form :model="modelValue" :inline="inline" :label-width="labelWidth" ref="formRef">
    <slot />
    <el-form-item>
      <el-button type="primary" icon="Search" @click="$emit('search')">搜索</el-button>
      <el-button icon="Refresh" @click="$emit('reset')">重置</el-button>
    </el-form-item>
  </el-form>
</template>

<script setup>
const props = defineProps({
  modelValue: { type: Object, required: true },
  labelWidth: { type: String, default: '68px' },
  inline: { type: Boolean, default: true }
})
const formRef = ref()
</script> 