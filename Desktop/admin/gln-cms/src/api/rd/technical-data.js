import request from '@/utils/request'

// 查询技术资料列表
export function listTechnicalData(query) {
  return request({
    url: '/rd/technical-data/list',
    method: 'get',
    params: query
  })
}

// 查询技术资料详细
export function getTechnicalData(id) {
  return request({
    url: '/rd/technical-data/' + id,
    method: 'get'
  })
}

// 新增技术资料
export function addTechnicalData(data) {
  return request({
    url: '/rd/technical-data',
    method: 'post',
    data: data
  })
}

// 修改技术资料
export function updateTechnicalData(data) {
  return request({
    url: '/rd/technical-data',
    method: 'put',
    data: data
  })
}

// 删除技术资料
export function delTechnicalData(id) {
  return request({
    url: '/rd/technical-data/' + id,
    method: 'delete'
  })
}

// 下载技术资料文件
export function downloadTechnicalData(id) {
  return request({
    url: '/rd/technical-data/download/' + id,
    method: 'get',
    responseType: 'blob'
  })
} 