import request from '@/utils/request'

// 查询产品列表
export function listProduct(query) {
  return request({
    url: '/rd/product/list',
    method: 'get',
    params: query
  })
}

// 查询产品详细
export function getProduct(id) {
  return request({
    url: '/rd/product/' + id,
    method: 'get'
  })
}

// 新增产品
export function addProduct(data) {
  return request({
    url: '/rd/product',
    method: 'post',
    data: data
  })
}

// 修改产品
export function updateProduct(data) {
  return request({
    url: '/rd/product',
    method: 'put',
    data: data
  })
}

// 删除产品
export function delProduct(id) {
  return request({
    url: '/rd/product/' + id,
    method: 'delete'
  })
}

// 获取产品配件列表
export function getProductAccessories(productId) {
  return request({
    url: '/rd/product/accessories/' + productId,
    method: 'get'
  })
}

// 更新产品配件列表
export function updateProductAccessories(productId, data) {
  return request({
    url: '/rd/product/accessories/' + productId,
    method: 'put',
    data: data
  })
} 