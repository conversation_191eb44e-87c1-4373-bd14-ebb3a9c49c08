import request from '@/utils/request'

// 查询BOM子件列表
export function listBomSubItem(query) {
  return request({
    url: '/rd/bom-sub-item/list',
    method: 'get',
    params: query
  })
}

// 查询BOM子件详细
export function getBomSubItem(id) {
  return request({
    url: '/rd/bom-sub-item/' + id,
    method: 'get'
  })
}

// 新增BOM子件
export function addBomSubItem(data) {
  return request({
    url: '/rd/bom-sub-item',
    method: 'post',
    data: data
  })
}

// 修改BOM子件
export function updateBomSubItem(data) {
  return request({
    url: '/rd/bom-sub-item',
    method: 'put',
    data: data
  })
}

// 删除BOM子件
export function delBomSubItem(id) {
  return request({
    url: '/rd/bom-sub-item/' + id,
    method: 'delete'
  })
} 