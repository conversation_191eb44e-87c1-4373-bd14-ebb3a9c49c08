import request from '@/utils/request'

// 分页&条件查询客户信息
export function listCustomer(query) {
  return request({
    url: '/marketing/customer/list',
    method: 'get',
    params: query
  })
}

// 新增客户信息
export function addCustomer(data) {
  return request({
    url: '/marketing/customer',
    method: 'post',
    data
  })
}

// 编辑客户信息
export function updateCustomer(data) {
  return request({
    url: '/marketing/customer',
    method: 'put',
    data
  })
}

// 删除客户信息
export function delCustomer(id) {
  return request({
    url: `/marketing/customer/${id}`,
    method: 'delete'
  })
}

// 查询单个客户详情
export function getCustomer(id) {
  return request({
    url: `/marketing/customer/${id}`,
    method: 'get'
  })
}

// 冻结/解冻客户
export function toggleCustomerStatus(id, status) {
  return request({
    url: `/marketing/customer/status/${id}`,
    method: 'put',
    data: { status }
  })
}

// 导出客户信息
export function exportCustomer(query) {
  return request({
    url: '/marketing/customer/export',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
} 