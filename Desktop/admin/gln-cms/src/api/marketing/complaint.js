import request from '@/utils/request'

// 分页&条件查询客户投诉
export function listComplaint(query) {
  return request({
    url: '/marketing/complaint/list',
    method: 'get',
    params: query
  })
}

// 新增客户投诉
export function addComplaint(data) {
  return request({
    url: '/marketing/complaint',
    method: 'post',
    data
  })
}

// 编辑客户投诉
export function updateComplaint(data) {
  return request({
    url: '/marketing/complaint',
    method: 'put',
    data
  })
}

// 删除客户投诉
export function delComplaint(id) {
  return request({
    url: `/marketing/complaint/${id}`,
    method: 'delete'
  })
}

// 查询单个投诉详情
export function getComplaint(id) {
  return request({
    url: `/marketing/complaint/${id}`,
    method: 'get'
  })
}

// 提交投诉反馈
export function submitComplaintFeedback(id, feedback) {
  return request({
    url: `/marketing/complaint/feedback/${id}`,
    method: 'put',
    data: { complaintFeedback: feedback }
  })
}

// 获取投诉统计数据
export function getComplaintStatistics(query) {
  return request({
    url: '/marketing/complaint/statistics',
    method: 'get',
    params: query
  })
}

// 导出投诉信息
export function exportComplaint(query) {
  return request({
    url: '/marketing/complaint/export',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
} 