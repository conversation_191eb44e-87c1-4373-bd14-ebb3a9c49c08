import request from '@/utils/request'

// 分页&条件查询客户财务信息
export function listFinance(query) {
  return request({
    url: '/marketing/finance/list',
    method: 'get',
    params: query
  })
}

// 查询单个客户财务详情
export function getFinance(id) {
  return request({
    url: `/marketing/finance/${id}`,
    method: 'get'
  })
}

// 获取客户财务统计数据
export function getFinanceStatistics(query) {
  return request({
    url: '/marketing/finance/statistics',
    method: 'get',
    params: query
  })
}

// 获取客户付款记录
export function getPaymentRecords(customerId, query) {
  return request({
    url: `/marketing/finance/payment/${customerId}`,
    method: 'get',
    params: query
  })
}

// 获取客户发票记录
export function getInvoiceRecords(customerId, query) {
  return request({
    url: `/marketing/finance/invoice/${customerId}`,
    method: 'get',
    params: query
  })
}

// 获取客户订单记录
export function getOrderRecords(customerId, query) {
  return request({
    url: `/marketing/finance/order/${customerId}`,
    method: 'get',
    params: query
  })
}

// 导出客户财务信息
export function exportFinance(query) {
  return request({
    url: '/marketing/finance/export',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
} 