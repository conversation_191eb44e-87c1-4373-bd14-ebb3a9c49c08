import request from '@/utils/request'

// 分页&条件查询订单
export function listOrder(query) {
  return request({
    url: '/marketing/order/list',
    method: 'get',
    params: query
  })
}

// 新增订单
export function addOrder(data) {
  return request({
    url: '/marketing/order',
    method: 'post',
    data
  })
}

// 编辑订单
export function updateOrder(data) {
  return request({
    url: '/marketing/order',
    method: 'put',
    data
  })
}

// 删除订单
export function delOrder(id) {
  return request({
    url: `/marketing/order/${id}`,
    method: 'delete'
  })
}

// 查询单个订单详情
export function getOrder(id) {
  return request({
    url: `/marketing/order/${id}`,
    method: 'get'
  })
}

// 更新订单状态
export function updateOrderStatus(id, status) {
  return request({
    url: `/marketing/order/status/${id}`,
    method: 'put',
    data: { status }
  })
}

// 导出订单信息
export function exportOrder(query) {
  return request({
    url: '/marketing/order/export',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}

// 获取订单统计数据
export function getOrderStatistics(query) {
  return request({
    url: '/marketing/order/statistics',
    method: 'get',
    params: query
  })
} 