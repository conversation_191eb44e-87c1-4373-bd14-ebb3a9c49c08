import request from '@/utils/request'

// 查询培训列表
export function listTraining(query) {
  return request({
    url: '/admin/training/list',
    method: 'get',
    params: query
  })
}

// 新增培训
export function addTraining(data) {
  return request({
    url: '/admin/training',
    method: 'post',
    data: data
  })
}

// 修改培训
export function updateTraining(data) {
  return request({
    url: '/admin/training',
    method: 'put',
    data: data
  })
}

// 删除培训
export function deleteTraining(id) {
  return request({
    url: '/admin/training/' + id,
    method: 'delete'
  })
}

// 导入培训
export function importTraining() {
  return request({
    url: '/admin/training/import',
    method: 'post'
  })
}

// 导出培训
export function exportTraining(query) {
  return request({
    url: '/admin/training/export',
    method: 'get',
    params: query
  })
}