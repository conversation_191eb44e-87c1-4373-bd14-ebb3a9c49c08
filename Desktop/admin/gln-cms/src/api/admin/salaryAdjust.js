import request from '@/utils/request'

// 查询薪资调整列表
export function listSalaryAdjust(query) {
  return request({
    url: '/admin/salaryAdjust/list',
    method: 'get',
    params: query
  })
}

// 新增薪资调整
export function addSalaryAdjust(data) {
  return request({
    url: '/admin/salaryAdjust',
    method: 'post',
    data: data
  })
}

// 修改薪资调整
export function updateSalaryAdjust(data) {
  return request({
    url: '/admin/salaryAdjust',
    method: 'put',
    data: data
  })
}

// 删除薪资调整
export function deleteSalaryAdjust(id) {
  return request({
    url: '/admin/salaryAdjust/' + id,
    method: 'delete'
  })
}

// 导入薪资调整
export function importSalaryAdjust() {
  return request({
    url: '/admin/salaryAdjust/import',
    method: 'post'
  })
}

// 导出薪资调整
export function exportSalaryAdjust(query) {
  return request({
    url: '/admin/salaryAdjust/export',
    method: 'get',
    params: query
  })
} 