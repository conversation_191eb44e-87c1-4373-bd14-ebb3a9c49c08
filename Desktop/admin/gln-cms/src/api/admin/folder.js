import request from '@/utils/request'

// 查询文件夹列表
export function listFolder(query) {
  return request({
    url: '/admin/folder/list',
    method: 'get',
    params: query
  })
}

// 新增文件夹
export function addFolder(data) {
  return request({
    url: '/admin/folder',
    method: 'post',
    data: data
  })
}

// 修改文件夹
export function updateFolder(data) {
  return request({
    url: '/admin/folder',
    method: 'put',
    data: data
  })
}

// 删除文件夹
export function deleteFolder(id) {
  return request({
    url: '/admin/folder/' + id,
    method: 'delete'
  })
}

// 导入文件夹
export function importFolder() {
  return request({
    url: '/admin/folder/import',
    method: 'post'
  })
}

// 导出文件夹
export function exportFolder(query) {
  return request({
    url: '/admin/folder/export',
    method: 'get',
    params: query
  })
}

// 获取文件夹树
export function getFolderTree() {
  return request({
    url: '/admin/folder/tree',
    method: 'get'
  })
} 