import request from '@/utils/request'

// 查询文件发放列表
export function listFileGrant(query) {
  return request({
    url: '/admin/fileGrant/list',
    method: 'get',
    params: query
  })
}

// 新增文件发放
export function addFileGrant(data) {
  return request({
    url: '/admin/fileGrant',
    method: 'post',
    data: data
  })
}

// 修改文件发放
export function updateFileGrant(data) {
  return request({
    url: '/admin/fileGrant',
    method: 'put',
    data: data
  })
}

// 删除文件发放
export function deleteFileGrant(id) {
  return request({
    url: '/admin/fileGrant/' + id,
    method: 'delete'
  })
}

// 导入文件发放
export function importFileGrant() {
  return request({
    url: '/admin/fileGrant/import',
    method: 'post'
  })
}

// 导出文件发放
export function exportFileGrant(query) {
  return request({
    url: '/admin/fileGrant/export',
    method: 'get',
    params: query
  })
} 