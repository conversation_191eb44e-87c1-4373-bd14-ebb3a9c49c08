import request from '@/utils/request'

// 查询员工调岗列表
export function listEmployeeTransfer(query) {
  return request({
    url: '/admin/employeeTransfer/list',
    method: 'get',
    params: query
  })
}

// 新增员工调岗
export function addEmployeeTransfer(data) {
  return request({
    url: '/admin/employeeTransfer',
    method: 'post',
    data: data
  })
}

// 修改员工调岗
export function updateEmployeeTransfer(data) {
  return request({
    url: '/admin/employeeTransfer',
    method: 'put',
    data: data
  })
}

// 删除员工调岗
export function deleteEmployeeTransfer(id) {
  return request({
    url: '/admin/employeeTransfer/' + id,
    method: 'delete'
  })
}

// 导入员工调岗
export function importEmployeeTransfer() {
  return request({
    url: '/admin/employeeTransfer/import',
    method: 'post'
  })
}

// 导出员工调岗
export function exportEmployeeTransfer(query) {
  return request({
    url: '/admin/employeeTransfer/export',
    method: 'get',
    params: query
  })
} 