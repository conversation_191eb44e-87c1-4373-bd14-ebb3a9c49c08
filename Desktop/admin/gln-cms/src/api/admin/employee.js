import request from '@/utils/request'

// 分页&条件查询员工
export function listEmployee(query) {
  return request({
    url: '/employee/list',
    method: 'get',
    params: query
  })
}

// 新增员工
export function addEmployee(data) {
  return request({
    url: '/employee',
    method: 'post',
    data
  })
}

// 编辑员工
export function updateEmployee(data) {
  return request({
    url: '/employee',
    method: 'put',
    data
  })
}

// 删除员工
export function delEmployee(id) {
  return request({
    url: `/employee/${id}`,
    method: 'delete'
  })
}

// 离职操作
export function leaveEmployee(id, leave_date) {
  return request({
    url: `/employee/leave/${id}`,
    method: 'put',
    data: { leave_date }
  })
}

// 查询单个员工详情
export function getEmployee(id) {
  return request({
    url: `/employee/${id}`,
    method: 'get'
  })
}

// 导出用户
export function importTemplate() {
  return request({
    url: '/employee/importTemplate',
    method: 'get',
    responseType: 'blob'
  })
} 