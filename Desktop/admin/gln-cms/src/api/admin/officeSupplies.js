import request from '@/utils/request'

// 查询办公用品列表
export function listOfficeSupplies(query) {
  return request({
    url: '/admin/officeSupplies/list',
    method: 'get',
    params: query
  })
}

// 新增办公用品
export function addOfficeSupplies(data) {
  return request({
    url: '/admin/officeSupplies',
    method: 'post',
    data: data
  })
}

// 修改办公用品
export function updateOfficeSupplies(data) {
  return request({
    url: '/admin/officeSupplies',
    method: 'put',
    data: data
  })
}

// 删除办公用品
export function deleteOfficeSupplies(id) {
  return request({
    url: '/admin/officeSupplies/' + id,
    method: 'delete'
  })
}

// 导入办公用品
export function importOfficeSupplies() {
  return request({
    url: '/admin/officeSupplies/import',
    method: 'post'
  })
}

// 导出办公用品
export function exportOfficeSupplies(query) {
  return request({
    url: '/admin/officeSupplies/export',
    method: 'get',
    params: query
  })
} 