import request from '@/utils/request'

// 查询请假列表
export function listLeave(query) {
  return request({
    url: '/admin/leave/list',
    method: 'get',
    params: query
  })
}

// 新增请假
export function addLeave(data) {
  return request({
    url: '/admin/leave',
    method: 'post',
    data: data
  })
}

// 修改请假
export function updateLeave(data) {
  return request({
    url: '/admin/leave',
    method: 'put',
    data: data
  })
}

// 删除请假
export function deleteLeave(id) {
  return request({
    url: '/admin/leave/' + id,
    method: 'delete'
  })
}

// 导入请假
export function importLeave() {
  return request({
    url: '/admin/leave/import',
    method: 'post'
  })
}

// 导出请假
export function exportLeave(query) {
  return request({
    url: '/admin/leave/export',
    method: 'get',
    params: query
  })
} 