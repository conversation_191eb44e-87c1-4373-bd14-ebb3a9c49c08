<template>
  <div class="app-container">
    <el-row :gutter="20">
      <el-col :span="24">
        <el-card>
          <template #header>
            <div class="card-header">
              <span class="card-title">计划物控部</span>
              <el-tag type="warning">计划控制</el-tag>
            </div>
          </template>
          
          <div class="department-intro">
            <h3>部门简介</h3>
            <p>计划物控部负责公司生产计划制定、物料需求计划、库存控制、生产调度等职能，确保生产有序进行。</p>
          </div>

          <el-divider />

          <el-row :gutter="20">
            <el-col :span="8">
              <el-card class="feature-card" shadow="hover">
                <template #header>
                  <div class="feature-header">
                    <el-icon><Calendar /></el-icon>
                    <span>生产计划</span>
                  </div>
                </template>
                <div class="feature-content">
                  <p>生产计划制定、产能规划、生产调度等计划工作</p>
                  <el-button type="primary" size="small" @click="goToModule('production')">进入模块</el-button>
                </div>
              </el-card>
            </el-col>
            
            <el-col :span="8">
              <el-card class="feature-card" shadow="hover">
                <template #header>
                  <div class="feature-header">
                    <el-icon><Box /></el-icon>
                    <span>物料控制</span>
                  </div>
                </template>
                <div class="feature-content">
                  <p>物料需求计划、库存控制、物料配送等控制工作</p>
                  <el-button type="primary" size="small" @click="goToModule('material')">进入模块</el-button>
                </div>
              </el-card>
            </el-col>
            
            <el-col :span="8">
              <el-card class="feature-card" shadow="hover">
                <template #header>
                  <div class="feature-header">
                    <el-icon><DataAnalysis /></el-icon>
                    <span>数据分析</span>
                  </div>
                </template>
                <div class="feature-content">
                  <p>生产数据分析、效率评估、成本分析等分析工作</p>
                  <el-button type="primary" size="small" @click="goToModule('analysis')">进入模块</el-button>
                </div>
              </el-card>
            </el-col>
          </el-row>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup name="Planning">
import { Calendar, Box, DataAnalysis } from '@element-plus/icons-vue'

const goToModule = (module) => {
  console.log('进入模块:', module)
}
</script>

<style scoped lang="scss">
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-title {
  font-size: 18px;
  font-weight: bold;
  color: #303133;
}

.department-intro {
  margin-bottom: 20px;
  
  h3 {
    color: #409EFF;
    margin-bottom: 10px;
  }
  
  p {
    color: #606266;
    line-height: 1.6;
  }
}

.feature-card {
  margin-bottom: 20px;
  
  .feature-header {
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: bold;
    color: #409EFF;
  }
  
  .feature-content {
    text-align: center;
    
    p {
      color: #606266;
      margin-bottom: 15px;
      line-height: 1.5;
    }
  }
}
</style> 