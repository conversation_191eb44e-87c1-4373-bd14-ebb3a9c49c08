<template>
  <div class="app-container">
    <el-row :gutter="20">
      <el-col :span="24">
        <el-card>
          <template #header>
            <div class="card-header">
              <span class="card-title">设备研发部</span>
              <el-tag type="primary">设备研发</el-tag>
            </div>
          </template>
          
          <div class="department-intro">
            <h3>部门简介</h3>
            <p>设备研发部负责公司生产设备研发、设备升级改造、自动化设备开发等职能，提升生产效率和设备技术水平。</p>
          </div>

          <el-divider />

          <el-row :gutter="20">
            <el-col :span="8">
              <el-card class="feature-card" shadow="hover">
                <template #header>
                  <div class="feature-header">
                    <el-icon><Cpu /></el-icon>
                    <span>设备研发</span>
                  </div>
                </template>
                <div class="feature-content">
                  <p>新设备开发、设备设计、设备测试等研发工作</p>
                  <el-button type="primary" size="small" @click="goToModule('development')">进入模块</el-button>
                </div>
              </el-card>
            </el-col>
            
            <el-col :span="8">
              <el-card class="feature-card" shadow="hover">
                <template #header>
                  <div class="feature-header">
                    <el-icon><Refresh /></el-icon>
                    <span>设备改造</span>
                  </div>
                </template>
                <div class="feature-content">
                  <p>现有设备升级、技术改造、自动化改造等工作</p>
                  <el-button type="primary" size="small" @click="goToModule('upgrade')">进入模块</el-button>
                </div>
              </el-card>
            </el-col>
            
            <el-col :span="8">
              <el-card class="feature-card" shadow="hover">
                <template #header>
                  <div class="feature-header">
                    <el-icon><Monitor /></el-icon>
                    <span>自动化系统</span>
                  </div>
                </template>
                <div class="feature-content">
                  <p>自动化系统开发、智能设备集成、控制系统设计等工作</p>
                  <el-button type="primary" size="small" @click="goToModule('automation')">进入模块</el-button>
                </div>
              </el-card>
            </el-col>
          </el-row>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup name="Equipment">
import { Cpu, Refresh, Monitor } from '@element-plus/icons-vue'

const goToModule = (module) => {
  console.log('进入模块:', module)
}
</script>

<style scoped lang="scss">
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-title {
  font-size: 18px;
  font-weight: bold;
  color: #303133;
}

.department-intro {
  margin-bottom: 20px;
  
  h3 {
    color: #409EFF;
    margin-bottom: 10px;
  }
  
  p {
    color: #606266;
    line-height: 1.6;
  }
}

.feature-card {
  margin-bottom: 20px;
  
  .feature-header {
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: bold;
    color: #409EFF;
  }
  
  .feature-content {
    text-align: center;
    
    p {
      color: #606266;
      margin-bottom: 15px;
      line-height: 1.5;
    }
  }
}
</style> 