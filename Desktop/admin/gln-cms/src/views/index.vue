<template>
  <div class="app-container home">
    <el-row :gutter="20">
      <el-col :sm="24" :lg="12" style="padding-left: 20px">
        <h2>格莱恩科技有限公司</h2>
        <p>
          格莱恩科技有限公司是一家专注于企业管理系统开发的技术公司，基于现代化的技术栈为客户提供高效、稳定、安全的管理解决方案。我们的系统可以用于所有的Web应用程序，如企业后台管理、客户关系管理、内容管理系统、办公自动化等，同时支持深度定制以满足不同企业的特殊需求。
        </p>
        <p>
          <b>当前版本:</b>
          <span>v{{ version }}</span>
        </p>
        <p>
          <el-tag type="success">专业服务</el-tag>
        </p>
        <p>
          <el-button type="primary" size="mini" icon="MostlyCloudy" plain @click="goTarget('https://www.gln-tech.com')">访问官网</el-button>
          <el-button size="mini" icon="HomeFilled" plain @click="goTarget('/docs')">技术文档</el-button>
        </p>
      </el-col>

      <el-col :sm="24" :lg="12" style="padding-left: 50px">
        <el-row>
          <el-col :span="12">
            <h2>技术选型</h2>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="6">
            <h4>后端技术</h4>
            <ul>
              <li>node</li>
              <li>nestjs</li>
              <li>typeorm</li>
              <li>redis</li>
              <li>mysql</li>
              <li>...</li>
            </ul>
          </el-col>
          <el-col :span="6">
            <h4>前端技术</h4>
            <ul>
              <li>Vue3</li>
              <li>Element-Plus</li>
              <li>Axios</li>
              <li>Sass</li>
              <li>Quill</li>
              <li>...</li>
            </ul>
          </el-col>
        </el-row>
      </el-col>
    </el-row>
    <el-divider />
    <el-row :gutter="20">
      <el-col :xs="24" :sm="24" :md="12" :lg="8">
        <el-card class="update-log">
          <div slot="header" class="clearfix">
            <span>联系信息</span>
          </div>
          <div class="body">
            <p>
              <svg-icon icon-class="guide" class-name="icon" />
              官网：
              <el-link href="https://www.gln-tech.com" target="_blank">https://www.gln-tech.com</el-link>
            </p>
            <p>
              <svg-icon icon-class="wechat" class-name="icon" />
              微信：
              <a href="javascript:;">gln-tech</a>
            </p>
          </div>
        </el-card>
      </el-col>
      <el-col :xs="24" :sm="24" :md="12" :lg="8">
        <el-card class="update-log">
          <div slot="header" class="clearfix">
            <span>更多优质产品</span>
          </div>
          <div class="body">
            <p>
              <el-link href="https://www.gln-tech.com/products/crm" target="_blank">客户关系管理系统</el-link>
            </p>
            <p>
              <el-link href="https://www.gln-tech.com/products/oa" target="_blank">办公自动化系统</el-link>
            </p>
            <p>
              <el-link href="https://www.gln-tech.com/products/cms" target="_blank">内容管理系统</el-link>
            </p>
          </div>
        </el-card>
      </el-col>
      <el-col :xs="24" :sm="24" :md="12" :lg="8">
        <el-card class="update-log">
          <div slot="header" class="clearfix">
            <span>技术团队</span>
          </div>
          <div class="body">
            <p>
              <svg-icon icon-class="github" class-name="icon" />
              技术总监：
              <el-link href="https://github.com/gln-tech" target="_blank">张工</el-link>
            </p>
            <p>
              <svg-icon icon-class="github" class-name="icon" />
              前端工程师：
              <el-link href="https://github.com/gln-tech" target="_blank">李工</el-link>
            </p>
            <p>
              <svg-icon icon-class="github" class-name="icon" />
              后端工程师：
              <el-link href="https://github.com/gln-tech" target="_blank">王工</el-link>
            </p>
          </div>
        </el-card>
      </el-col>
      <!-- <el-col :xs="24" :sm="24" :md="12" :lg="8">
				<el-card class="update-log">
					<div slot="header" class="clearfix">
						<span>捐赠支持</span>
					</div>
					<div class="body">
						<img src="@/assets/images/pay.png" alt="donate" width="100%" />
						<span style="display: inline-block; height: 30px; line-height: 30px">你可以请作者喝杯咖啡表示鼓励</span>
					</div>
				</el-card>
			</el-col> -->
    </el-row>
  </div>
</template>

<script setup name="Index">
const version = ref('1.0.0')

function goTarget(url) {
  window.open(url, '__blank')
}
</script>

<style scoped lang="scss">
.home {
  blockquote {
    padding: 10px 20px;
    margin: 0 0 20px;
    font-size: 17.5px;
    border-left: 5px solid #eee;
  }
  hr {
    margin-top: 20px;
    margin-bottom: 20px;
    border: 0;
    border-top: 1px solid #eee;
  }
  .col-item {
    margin-bottom: 20px;
  }

  ul {
    padding: 0;
    margin: 0;
  }

  font-family: 'open sans', 'Helvetica Neue', Helvetica, Arial, sans-serif;
  font-size: 13px;
  color: #676a6c;
  overflow-x: hidden;

  ul {
    list-style-type: none;
  }

  h4 {
    margin-top: 0px;
  }

  h2 {
    margin-top: 10px;
    font-size: 26px;
    font-weight: 100;
  }

  p {
    margin-top: 10px;

    b {
      font-weight: 700;
    }
  }

  .update-log {
    ol {
      display: block;
      list-style-type: decimal;
      margin-block-start: 1em;
      margin-block-end: 1em;
      margin-inline-start: 0;
      margin-inline-end: 0;
      padding-inline-start: 40px;
    }
  }
}
</style>
