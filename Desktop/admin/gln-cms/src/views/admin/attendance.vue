<template>
  <div class="app-container">
    <CommonSearchBar :model-value="queryParams" @search="handleQuery" @reset="resetQuery">
      <el-form-item label="员工ID" prop="employee_id">
        <el-input v-model="queryParams.employee_id" placeholder="请输入员工ID" clearable style="width: 180px" />
      </el-form-item>
      <el-form-item label="打卡日期" prop="dateRange">
        <el-date-picker v-model="queryParams.dateRange" value-format="YYYY-MM-DD" type="daterange" range-separator="-" start-placeholder="开始日期" end-placeholder="结束日期" style="width: 308px" />
      </el-form-item>
      <el-form-item label="类型" prop="type">
        <el-select v-model="queryParams.type" placeholder="请选择类型" clearable style="width: 180px">
          <el-option label="上班" value="上班" />
          <el-option label="下班" value="下班" />
          <el-option label="外出" value="外出" />
        </el-select>
      </el-form-item>
    </CommonSearchBar>
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="Search" @click="handleQuery">查询</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" plain icon="Warning" :disabled="single" @click="handleMarkAbnormal">异常标记</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="success" plain icon="Download" @click="handleExport">导出考勤</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>
    <CommonTable
      :columns="columns"
      :data="attendanceList"
      :loading="loading"
      :total="total"
      v-model:pageNum="pageNum"
      v-model:pageSize="pageSize"
      @pagination="getList"
      @selection-change="handleSelectionChange"
    >
      <template #operate="{ row }">
        <el-tooltip content="详情" placement="top">
          <el-button link type="info" icon="Document" @click="handleDetail(row)"></el-button>
        </el-tooltip>
        <el-tooltip content="异常标记" placement="top">
          <el-button link type="warning" icon="Warning" @click="handleMarkAbnormalRow(row)"></el-button>
        </el-tooltip>
      </template>
    </CommonTable>
    <!-- 详情弹窗 -->
    <el-dialog title="考勤详情" v-model="detailDialogVisible" width="600px" append-to-body>
      <el-descriptions :column="1" border>
        <el-descriptions-item label="员工ID">{{ detail.employee_id }}</el-descriptions-item>
        <el-descriptions-item label="打卡时间">{{ detail.clockin_time }}</el-descriptions-item>
        <el-descriptions-item label="类型">{{ detail.type }}</el-descriptions-item>
        <el-descriptions-item label="地点">{{ detail.location }}</el-descriptions-item>
        <el-descriptions-item label="异常">{{ detail.abnormal }}</el-descriptions-item>
      </el-descriptions>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="detailDialogVisible = false">关 闭</el-button>
        </div>
      </template>
    </el-dialog>
    <!-- 异常标记弹窗 -->
    <el-dialog title="异常标记" v-model="abnormalDialogVisible" width="500px" append-to-body>
      <el-form :model="abnormalForm" :rules="abnormalRules" ref="abnormalFormRef" label-width="110px">
        <el-form-item label="异常说明" prop="abnormal">
          <el-input v-model="abnormalForm.abnormal" type="textarea" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitAbnormal">确 定</el-button>
          <el-button @click="abnormalDialogVisible = false">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'

// 查询参数
const queryParams = reactive({
  employee_id: '',
  dateRange: [],
  type: ''
})

// 列表数据
const attendanceList = ref([
  {
    employee_id: '1001',
    clockin_time: '2024-06-01 08:30:00',
    type: '上班',
    location: '公司大门',
    abnormal: '正常'
  },
  {
    employee_id: '1002',
    clockin_time: '2024-06-01 08:35:00',
    type: '上班',
    location: '公司大门',
    abnormal: '迟到'
  }
])
const loading = ref(false)
const total = ref(2)
const pageNum = ref(1)
const pageSize = ref(10)
const showSearch = ref(true)
const single = ref(true)

// 表格列
const columns = [
  { label: '员工ID', prop: 'employee_id' },
  { label: '打卡时间', prop: 'clockin_time' },
  { label: '类型', prop: 'type' },
  { label: '地点', prop: 'location' },
  { label: '异常', prop: 'abnormal' },
  { label: '操作', slot: 'operate', width: 160 }
]

// 详情弹窗
const detailDialogVisible = ref(false)
const detail = reactive({
  employee_id: '',
  clockin_time: '',
  type: '',
  location: '',
  abnormal: ''
})

// 异常标记弹窗
const abnormalDialogVisible = ref(false)
const abnormalForm = reactive({ abnormal: '' })
const abnormalRules = { abnormal: [{ required: true, message: '请输入异常说明', trigger: 'blur' }] }
const abnormalFormRef = ref()

function handleQuery() {
  // 查询逻辑，实际应调用接口
  ElMessage.success('查询成功（mock）')
}
function resetQuery() {
  queryParams.employee_id = ''
  queryParams.dateRange = []
  queryParams.type = ''
  handleQuery()
}
function getList() {
  // 分页逻辑，实际应调用接口
}
function handleSelectionChange(val) {
  single.value = val.length !== 1
}
function handleDetail(row) {
  Object.assign(detail, row)
  detailDialogVisible.value = true
}
function handleMarkAbnormal() {
  if (!single.value) {
    abnormalDialogVisible.value = true
  }
}
function handleMarkAbnormalRow(row) {
  abnormalForm.abnormal = ''
  abnormalDialogVisible.value = true
}
function handleExport() {
  ElMessage.success('导出成功（mock）')
}
function submitAbnormal() {
  abnormalDialogVisible.value = false
  ElMessage.success('异常标记成功（mock）')
}
</script> 