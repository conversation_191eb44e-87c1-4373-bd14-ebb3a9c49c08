<template>
  <div class="folder-container">
    <CommonSearchBar :fields="searchFields" v-model="searchForm" @search="handleSearch" />
    <div class="toolbar">
      <el-button type="primary" icon="el-icon-plus" @click="openDialog('add')">新建文件夹</el-button>
      <el-button icon="el-icon-upload" @click="handleImport">导入</el-button>
      <el-button icon="el-icon-download" @click="handleExport">导出</el-button>
    </div>
    <CommonTable
      :columns="tableColumns"
      :data="tableData"
      :pagination="pagination"
      @edit="openDialog('edit', $event)"
      @delete="handleDelete"
      @page-change="handlePageChange"
    />
    <el-dialog :title="dialogTitle" :visible.sync="dialogVisible" width="600px">
      <el-form :model="form" :rules="rules" ref="formRef" label-width="100px">
        <el-form-item label="文件夹名称" prop="folderName">
          <el-input v-model="form.folderName" placeholder="请输入文件夹名称" />
        </el-form-item>
        <el-form-item label="上级文件夹" prop="parentId">
          <el-select v-model="form.parentId" placeholder="请选择上级文件夹" allow-create filterable clearable>
            <el-option v-for="item in folderOptions" :key="item.id" :label="item.folderName" :value="item.id" />
          </el-select>
        </el-form-item>
        <el-form-item label="排序" prop="orderNum">
          <el-input-number v-model="form.orderNum" :min="1" :max="999" />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="handleSubmit">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import CommonSearchBar from '@/components/CommonSearchBar/index.vue';
import CommonTable from '@/components/CommonTable/index.vue';
import { listFolder, addFolder, updateFolder, deleteFolder, importFolder, exportFolder, getFolderTree } from '@/api/admin/folder';

export default {
  name: 'Folder',
  components: { CommonSearchBar, CommonTable },
  data() {
    return {
      searchForm: {
        folderName: '',
        parentId: ''
      },
      searchFields: [
        { label: '文件夹名称', prop: 'folderName', type: 'input', placeholder: '请输入文件夹名称' },
        { label: '上级文件夹', prop: 'parentId', type: 'select', options: [], placeholder: '请选择上级文件夹' }
      ],
      tableColumns: [
        { label: '文件夹名称', prop: 'folderName' },
        { label: '上级文件夹', prop: 'parentName' },
        { label: '排序', prop: 'orderNum' },
        { label: '备注', prop: 'remark' },
        { label: '操作', prop: 'action', slot: 'action', fixed: 'right', width: 180 }
      ],
      tableData: [],
      pagination: { pageNum: 1, pageSize: 10, total: 0 },
      dialogVisible: false,
      dialogTitle: '',
      form: {
        id: null,
        folderName: '',
        parentId: '',
        orderNum: 1,
        remark: ''
      },
      rules: {
        folderName: [{ required: true, message: '请输入文件夹名称', trigger: 'blur' }],
        orderNum: [{ required: true, message: '请输入排序', trigger: 'blur' }]
      },
      folderOptions: []
    };
  },
  created() {
    this.getFolderOptions();
    this.getList();
  },
  methods: {
    getFolderOptions() {
      getFolderTree().then(res => {
        this.folderOptions = res.data || [];
        this.searchFields.find(f => f.prop === 'parentId').options = this.folderOptions;
      });
    },
    getList() {
      listFolder({ ...this.searchForm, ...this.pagination }).then(res => {
        this.tableData = res.rows;
        this.pagination.total = res.total;
      });
    },
    handleSearch() {
      this.pagination.pageNum = 1;
      this.getList();
    },
    handlePageChange(page) {
      this.pagination.pageNum = page;
      this.getList();
    },
    openDialog(type, row) {
      this.dialogTitle = type === 'add' ? '新建文件夹' : '编辑文件夹';
      this.dialogVisible = true;
      if (type === 'edit' && row) {
        this.form = { ...row };
      } else {
        this.form = { id: null, folderName: '', parentId: '', orderNum: 1, remark: '' };
      }
    },
    handleSubmit() {
      this.$refs.formRef.validate(valid => {
        if (!valid) return;
        const submitApi = this.form.id ? updateFolder : addFolder;
        submitApi(this.form).then(() => {
          this.$message.success('操作成功');
          this.dialogVisible = false;
          this.getList();
        });
      });
    },
    handleDelete(row) {
      this.$confirm('确定删除该文件夹吗？', '提示', { type: 'warning' }).then(() => {
        deleteFolder(row.id).then(() => {
          this.$message.success('删除成功');
          this.getList();
        });
      });
    },
    handleImport() {
      importFolder().then(() => {
        this.$message.success('导入成功');
        this.getList();
      });
    },
    handleExport() {
      exportFolder(this.searchForm);
    }
  }
};
</script>

<style scoped>
.folder-container {
  padding: 24px;
}
.toolbar {
  margin-bottom: 16px;
}
</style> 