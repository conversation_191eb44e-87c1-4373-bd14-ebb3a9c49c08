<template>
  <div class="app-container">
    <!-- 查询栏 -->
    <el-form :inline="true" :model="queryParams" class="search-form">
      <el-form-item label="姓名">
        <el-input v-model="queryParams.name" placeholder="请输入姓名" clearable style="width: 180px" />
      </el-form-item>
      <el-form-item label="部门">
        <el-input v-model="queryParams.dept" placeholder="请输入部门" clearable style="width: 180px" />
      </el-form-item>
      <el-form-item label="离职日期">
        <el-date-picker v-model="queryParams.leaveDateRange" value-format="YYYY-MM-DD" type="daterange" range-separator="-" start-placeholder="开始日期" end-placeholder="结束日期" style="width: 240px" />
      </el-form-item>
      <el-form-item label="状态">
        <el-select v-model="queryParams.status" placeholder="请选择状态" clearable style="width: 140px">
          <el-option label="待审批" value="待审批" />
          <el-option label="已通过" value="已通过" />
          <el-option label="已驳回" value="已驳回" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>
    <!-- 按钮组 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" icon="el-icon-plus" @click="handleAdd">发起离职</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="success" icon="el-icon-edit" :disabled="single" @click="handleApproveSelected">审批</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="info" icon="el-icon-document" :disabled="single" @click="handleDetailSelected">详情</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" icon="el-icon-download" @click="handleExport">导出</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>
    <!-- 表格 -->
    <CommonTable
      :columns="columns"
      :data="leaveList"
      :loading="loading"
      :total="total"
      v-model:pageNum="pageNum"
      v-model:pageSize="pageSize"
      @pagination="getList"
      @selection-change="handleSelectionChange"
    >
      <template #operate="{ row }">
        <el-tooltip content="审批" placement="top" v-if="row.status === '待审批'">
          <el-button link type="success" icon="el-icon-edit" @click="handleApprove(row)">审批</el-button>
        </el-tooltip>
        <el-tooltip content="详情" placement="top">
          <el-button link type="info" icon="el-icon-document" @click="handleDetail(row)">详情</el-button>
        </el-tooltip>
      </template>
      <template #attachment="{ row }">
        <el-link v-if="row.attachment" :href="row.attachment" target="_blank">查看附件</el-link>
      </template>
    </CommonTable>
    <!-- 新增/编辑弹窗 -->
    <el-dialog :title="dialogTitle" v-model="dialogVisible" width="600px" append-to-body>
      <el-form :model="form" :rules="rules" ref="formRef" label-width="110px">
        <el-form-item label="员工ID" prop="employee_id">
          <el-input v-model="form.employee_id" />
        </el-form-item>
        <el-form-item label="离职原因" prop="leave_reason">
          <el-input v-model="form.leave_reason" type="textarea" />
        </el-form-item>
        <el-form-item label="离职日期" prop="leave_date">
          <el-date-picker v-model="form.leave_date" type="date" value-format="YYYY-MM-DD" />
        </el-form-item>
        <el-form-item label="附件" prop="attachment">
          <el-upload
            class="upload-demo"
            action="/dev-api/common/upload"
            :show-file-list="false"
            :headers="{ Authorization: 'Bearer ' + (token || '') }"
            :on-success="(res) => form.attachment = res.data.url"
          >
            <el-button type="primary">上传附件</el-button>
          </el-upload>
          <el-link v-if="form.attachment" :href="form.attachment" target="_blank">查看附件</el-link>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="dialogVisible = false">取 消</el-button>
        </div>
      </template>
    </el-dialog>
    <!-- 审批弹窗 -->
    <el-dialog title="离职审批" v-model="approveDialogVisible" width="500px" append-to-body>
      <el-form :model="approveForm" :rules="approveRules" ref="approveFormRef" label-width="110px">
        <el-form-item label="审批意见" prop="opinion">
          <el-input v-model="approveForm.opinion" type="textarea" />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-select v-model="approveForm.status" placeholder="请选择">
            <el-option label="同意" value="已通过" />
            <el-option label="驳回" value="已驳回" />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitApprove">确 定</el-button>
          <el-button @click="approveDialogVisible = false">取 消</el-button>
        </div>
      </template>
    </el-dialog>
    <!-- 详情弹窗 -->
    <el-dialog title="离职详情" v-model="detailDialogVisible" width="600px" append-to-body>
      <el-descriptions :column="1" border>
        <el-descriptions-item label="员工ID">{{ detail.employee_id }}</el-descriptions-item>
        <el-descriptions-item label="离职原因">{{ detail.leave_reason }}</el-descriptions-item>
        <el-descriptions-item label="离职日期">{{ detail.leave_date }}</el-descriptions-item>
        <el-descriptions-item label="附件">
          <el-link v-if="detail.attachment" :href="detail.attachment" target="_blank">查看附件</el-link>
        </el-descriptions-item>
        <el-descriptions-item label="审批人">{{ detail.approver }}</el-descriptions-item>
        <el-descriptions-item label="审批意见">{{ detail.opinion }}</el-descriptions-item>
        <el-descriptions-item label="状态">{{ detail.status }}</el-descriptions-item>
      </el-descriptions>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="detailDialogVisible = false">关 闭</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import CommonTable from '@/components/CommonTable/index.vue'
import { ElMessage, ElMessageBox } from 'element-plus'
// 假设有如下API，实际请根据你的接口路径调整
// import { listEmployeeLeave, addEmployeeLeave, updateEmployeeLeave, deleteEmployeeLeave, approveEmployeeLeave, exportEmployeeLeave } from '@/api/admin/employeeLeave'

// 查询参数
const queryParams = reactive({
  name: '',
  dept: '',
  leaveDateRange: [],
  status: ''
})

// 列表数据
const leaveList = ref([])
const loading = ref(false)
const total = ref(0)
const pageNum = ref(1)
const pageSize = ref(10)
const showSearch = ref(true)
const single = ref(true)

// 弹窗相关
const dialogVisible = ref(false)
const dialogTitle = ref('')
const form = reactive({
  employee_id: '',
  leave_reason: '',
  leave_date: '',
  attachment: ''
})
const rules = {
  employee_id: [{ required: true, message: '请输入员工ID', trigger: 'blur' }],
  leave_reason: [{ required: true, message: '请输入离职原因', trigger: 'blur' }],
  leave_date: [{ required: true, message: '请选择离职日期', trigger: 'change' }]
}
const formRef = ref()

// 审批弹窗
const approveDialogVisible = ref(false)
const approveForm = reactive({
  opinion: '',
  status: ''
})
const approveRules = {
  opinion: [{ required: true, message: '请输入审批意见', trigger: 'blur' }],
  status: [{ required: true, message: '请选择状态', trigger: 'change' }]
}
const approveFormRef = ref()

// 详情弹窗
const detailDialogVisible = ref(false)
const detail = reactive({
  employee_id: '',
  leave_reason: '',
  leave_date: '',
  attachment: '',
  approver: '',
  opinion: '',
  status: ''
})

// 表格列
const columns = [
  { label: '员工ID', prop: 'employee_id' },
  { label: '离职原因', prop: 'leave_reason' },
  { label: '离职日期', prop: 'leave_date' },
  { label: '状态', prop: 'status' },
  { label: '操作', slot: 'operate', width: 160 },
  { label: '附件', slot: 'attachment', width: 100 }
]

// 选中行
const selectedRows = ref([])

function getList() {
  loading.value = true
  // 这里应调用实际API
  setTimeout(() => {
    // mock数据
    leaveList.value = [
      { employee_id: '1001', leave_reason: '个人原因', leave_date: '2024-06-01', status: '待审批', attachment: '', approver: '', opinion: '' },
      { employee_id: '1002', leave_reason: '家庭原因', leave_date: '2024-06-02', status: '已通过', attachment: '', approver: '张三', opinion: '同意' }
    ]
    total.value = 2
    loading.value = false
  }, 500)
}

function handleQuery() {
  pageNum.value = 1
  getList()
}
function resetQuery() {
  queryParams.name = ''
  queryParams.dept = ''
  queryParams.leaveDateRange = []
  queryParams.status = ''
  handleQuery()
}
function handleAdd() {
  dialogTitle.value = '发起离职'
  dialogVisible.value = true
  Object.assign(form, { employee_id: '', leave_reason: '', leave_date: '', attachment: '' })
}
function handleApprove(row) {
  approveDialogVisible.value = true
  approveForm.opinion = ''
  approveForm.status = ''
}
function handleDetail(row) {
  detailDialogVisible.value = true
  Object.assign(detail, row)
}
function handleExport() {
  ElMessage.success('导出成功（mock）')
}
function handleSelectionChange(val) {
  selectedRows.value = val
  single.value = val.length !== 1
}
function handleApproveSelected() {
  if (selectedRows.value.length === 1) {
    handleApprove(selectedRows.value[0])
  }
}
function handleDetailSelected() {
  if (selectedRows.value.length === 1) {
    handleDetail(selectedRows.value[0])
  }
}
function submitForm() {
  formRef.value.validate((valid) => {
    if (!valid) return
    dialogVisible.value = false
    ElMessage.success('提交成功（mock）')
    getList()
  })
}
function submitApprove() {
  approveFormRef.value.validate((valid) => {
    if (!valid) return
    approveDialogVisible.value = false
    ElMessage.success('审批成功（mock）')
    getList()
  })
}

// 初始化
getList()
</script> 