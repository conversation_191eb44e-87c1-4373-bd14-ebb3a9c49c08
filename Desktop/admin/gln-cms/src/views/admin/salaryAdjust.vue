<template>
  <div class="salary-adjust-container">
    <CommonSearchBar :fields="searchFields" v-model="searchForm" @search="handleSearch" />
    <div class="toolbar">
      <el-button type="primary" icon="el-icon-plus" @click="openDialog('add')">新增调整</el-button>
      <el-button icon="el-icon-upload" @click="handleImport">导入</el-button>
      <el-button icon="el-icon-download" @click="handleExport">导出</el-button>
    </div>
    <CommonTable
      :columns="tableColumns"
      :data="tableData"
      :pagination="pagination"
      @edit="openDialog('edit', $event)"
      @delete="handleDelete"
      @page-change="handlePageChange"
    />
    <el-dialog :title="dialogTitle" :visible.sync="dialogVisible" width="600px">
      <el-form :model="form" :rules="rules" ref="formRef" label-width="100px">
        <el-form-item label="员工姓名" prop="employeeName">
          <el-input v-model="form.employeeName" placeholder="请输入员工姓名" />
        </el-form-item>
        <el-form-item label="工号" prop="employeeNo">
          <el-input v-model="form.employeeNo" placeholder="请输入工号" />
        </el-form-item>
        <el-form-item label="原薪资" prop="oldSalary">
          <el-input-number v-model="form.oldSalary" :min="0" :max="100000" />
        </el-form-item>
        <el-form-item label="新薪资" prop="newSalary">
          <el-input-number v-model="form.newSalary" :min="0" :max="100000" />
        </el-form-item>
        <el-form-item label="调整日期" prop="adjustDate">
          <el-date-picker v-model="form.adjustDate" type="date" placeholder="选择日期" style="width: 100%;" />
        </el-form-item>
        <el-form-item label="调整原因" prop="reason">
          <el-input v-model="form.reason" type="textarea" placeholder="请输入调整原因" />
        </el-form-item>
        <el-form-item label="附件" prop="attachment">
          <el-upload
            class="upload-demo"
            :action="uploadUrl"
            :headers="uploadHeaders"
            :on-success="handleUploadSuccess"
            :file-list="form.attachmentList"
            :data="{ module: 'salaryAdjust' }"
            :limit="3"
            list-type="text"
          >
            <el-button size="small" type="primary">上传文件</el-button>
          </el-upload>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="handleSubmit">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import CommonSearchBar from '@/components/CommonSearchBar/index.vue';
import CommonTable from '@/components/CommonTable/index.vue';
import { listSalaryAdjust, addSalaryAdjust, updateSalaryAdjust, deleteSalaryAdjust, importSalaryAdjust, exportSalaryAdjust } from '@/api/admin/salaryAdjust';
import { getToken } from '@/utils/auth';

export default {
  name: 'SalaryAdjust',
  components: { CommonSearchBar, CommonTable },
  data() {
    return {
      searchForm: {
        employeeName: '',
        employeeNo: '',
        adjustDate: ''
      },
      searchFields: [
        { label: '员工姓名', prop: 'employeeName', type: 'input', placeholder: '请输入员工姓名' },
        { label: '工号', prop: 'employeeNo', type: 'input', placeholder: '请输入工号' },
        { label: '调整日期', prop: 'adjustDate', type: 'date', placeholder: '请选择调整日期' }
      ],
      tableColumns: [
        { label: '员工姓名', prop: 'employeeName' },
        { label: '工号', prop: 'employeeNo' },
        { label: '原薪资', prop: 'oldSalary' },
        { label: '新薪资', prop: 'newSalary' },
        { label: '调整日期', prop: 'adjustDate' },
        { label: '调整原因', prop: 'reason' },
        { label: '附件', prop: 'attachment', slot: 'attachment' },
        { label: '操作', prop: 'action', slot: 'action', fixed: 'right', width: 180 }
      ],
      tableData: [],
      pagination: { pageNum: 1, pageSize: 10, total: 0 },
      dialogVisible: false,
      dialogTitle: '',
      form: {
        id: null,
        employeeName: '',
        employeeNo: '',
        oldSalary: 0,
        newSalary: 0,
        adjustDate: '',
        reason: '',
        attachment: '',
        attachmentList: []
      },
      rules: {
        employeeName: [{ required: true, message: '请输入员工姓名', trigger: 'blur' }],
        employeeNo: [{ required: true, message: '请输入工号', trigger: 'blur' }],
        oldSalary: [{ required: true, message: '请输入原薪资', trigger: 'blur' }],
        newSalary: [{ required: true, message: '请输入新薪资', trigger: 'blur' }],
        adjustDate: [{ required: true, message: '请选择调整日期', trigger: 'change' }],
        reason: [{ required: true, message: '请输入调整原因', trigger: 'blur' }]
      },
      uploadUrl: '/dev-api/upload',
      uploadHeaders: { Authorization: 'Bearer ' + getToken() }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    getList() {
      listSalaryAdjust({ ...this.searchForm, ...this.pagination }).then(res => {
        this.tableData = res.rows;
        this.pagination.total = res.total;
      });
    },
    handleSearch() {
      this.pagination.pageNum = 1;
      this.getList();
    },
    handlePageChange(page) {
      this.pagination.pageNum = page;
      this.getList();
    },
    openDialog(type, row) {
      this.dialogTitle = type === 'add' ? '新增调整' : '编辑调整';
      this.dialogVisible = true;
      if (type === 'edit' && row) {
        this.form = { ...row, attachmentList: row.attachment ? [{ name: row.attachment, url: row.attachment }] : [] };
      } else {
        this.form = { id: null, employeeName: '', employeeNo: '', oldSalary: 0, newSalary: 0, adjustDate: '', reason: '', attachment: '', attachmentList: [] };
      }
    },
    handleSubmit() {
      this.$refs.formRef.validate(valid => {
        if (!valid) return;
        const submitApi = this.form.id ? updateSalaryAdjust : addSalaryAdjust;
        const payload = { ...this.form, attachment: this.form.attachmentList.length ? this.form.attachmentList[0].url : '' };
        submitApi(payload).then(() => {
          this.$message.success('操作成功');
          this.dialogVisible = false;
          this.getList();
        });
      });
    },
    handleDelete(row) {
      this.$confirm('确定删除该调整记录吗？', '提示', { type: 'warning' }).then(() => {
        deleteSalaryAdjust(row.id).then(() => {
          this.$message.success('删除成功');
          this.getList();
        });
      });
    },
    handleImport() {
      importSalaryAdjust().then(() => {
        this.$message.success('导入成功');
        this.getList();
      });
    },
    handleExport() {
      exportSalaryAdjust(this.searchForm);
    },
    handleUploadSuccess(res, file, fileList) {
      this.form.attachmentList = fileList;
      this.form.attachment = res.url;
    }
  }
};
</script>

<style scoped>
.salary-adjust-container {
  padding: 24px;
}
.toolbar {
  margin-bottom: 16px;
}
</style> 