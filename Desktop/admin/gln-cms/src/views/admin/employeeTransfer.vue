<template>
  <div class="employee-transfer-container">
    <CommonSearchBar :fields="searchFields" v-model="searchForm" @search="handleSearch" />
    <div class="toolbar">
      <el-button type="primary" icon="el-icon-plus" @click="openDialog('add')">新增调岗</el-button>
      <el-button icon="el-icon-upload" @click="handleImport">导入</el-button>
      <el-button icon="el-icon-download" @click="handleExport">导出</el-button>
    </div>
    <CommonTable
      :columns="tableColumns"
      :data="tableData"
      :pagination="pagination"
      @edit="openDialog('edit', $event)"
      @delete="handleDelete"
      @page-change="handlePageChange"
    />
    <el-dialog :title="dialogTitle" :visible.sync="dialogVisible" width="600px">
      <el-form :model="form" :rules="rules" ref="formRef" label-width="100px">
        <el-form-item label="员工姓名" prop="employeeName">
          <el-input v-model="form.employeeName" placeholder="请输入员工姓名" />
        </el-form-item>
        <el-form-item label="工号" prop="employeeNo">
          <el-input v-model="form.employeeNo" placeholder="请输入工号" />
        </el-form-item>
        <el-form-item label="原部门" prop="oldDept">
          <el-select v-model="form.oldDept" placeholder="请选择原部门">
            <el-option v-for="item in deptOptions" :key="item.id" :label="item.name" :value="item.id" />
          </el-select>
        </el-form-item>
        <el-form-item label="原岗位" prop="oldPost">
          <el-input v-model="form.oldPost" placeholder="请输入原岗位" />
        </el-form-item>
        <el-form-item label="新部门" prop="newDept">
          <el-select v-model="form.newDept" placeholder="请选择新部门">
            <el-option v-for="item in deptOptions" :key="item.id" :label="item.name" :value="item.id" />
          </el-select>
        </el-form-item>
        <el-form-item label="新岗位" prop="newPost">
          <el-input v-model="form.newPost" placeholder="请输入新岗位" />
        </el-form-item>
        <el-form-item label="调岗日期" prop="transferDate">
          <el-date-picker v-model="form.transferDate" type="date" placeholder="选择日期" style="width: 100%;" />
        </el-form-item>
        <el-form-item label="调岗原因" prop="reason">
          <el-input v-model="form.reason" type="textarea" placeholder="请输入调岗原因" />
        </el-form-item>
        <el-form-item label="附件" prop="attachment">
          <el-upload
            class="upload-demo"
            :action="uploadUrl"
            :headers="uploadHeaders"
            :on-success="handleUploadSuccess"
            :file-list="form.attachmentList"
            :data="{ module: 'employeeTransfer' }"
            :limit="3"
            list-type="text"
          >
            <el-button size="small" type="primary">上传文件</el-button>
          </el-upload>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="handleSubmit">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import CommonSearchBar from '@/components/CommonSearchBar/index.vue';
import CommonTable from '@/components/CommonTable/index.vue';
import { deptTreeSelect } from '@/api/system/dept';
import { listEmployeeTransfer, addEmployeeTransfer, updateEmployeeTransfer, deleteEmployeeTransfer, importEmployeeTransfer, exportEmployeeTransfer } from '@/api/admin/employeeTransfer';
import { getToken } from '@/utils/auth';

export default {
  name: 'employeeTransfer',
  components: { CommonSearchBar, CommonTable },
  data() {
    return {
      searchForm: {
        employeeName: '',
        employeeNo: '',
        oldDept: '',
        newDept: '',
        transferDate: ''
      },
      searchFields: [
        { label: '员工姓名', prop: 'employeeName', type: 'input', placeholder: '请输入员工姓名' },
        { label: '工号', prop: 'employeeNo', type: 'input', placeholder: '请输入工号' },
        { label: '原部门', prop: 'oldDept', type: 'select', options: [], placeholder: '请选择原部门' },
        { label: '新部门', prop: 'newDept', type: 'select', options: [], placeholder: '请选择新部门' },
        { label: '调岗日期', prop: 'transferDate', type: 'date', placeholder: '请选择调岗日期' }
      ],
      tableColumns: [
        { label: '员工姓名', prop: 'employeeName' },
        { label: '工号', prop: 'employeeNo' },
        { label: '原部门', prop: 'oldDeptName' },
        { label: '原岗位', prop: 'oldPost' },
        { label: '新部门', prop: 'newDeptName' },
        { label: '新岗位', prop: 'newPost' },
        { label: '调岗日期', prop: 'transferDate' },
        { label: '调岗原因', prop: 'reason' },
        { label: '附件', prop: 'attachment', slot: 'attachment' },
        { label: '操作', prop: 'action', slot: 'action', fixed: 'right', width: 180 }
      ],
      tableData: [],
      pagination: { pageNum: 1, pageSize: 10, total: 0 },
      dialogVisible: false,
      dialogTitle: '',
      form: {
        id: null,
        employeeName: '',
        employeeNo: '',
        oldDept: '',
        oldPost: '',
        newDept: '',
        newPost: '',
        transferDate: '',
        reason: '',
        attachment: '',
        attachmentList: []
      },
      rules: {
        employeeName: [{ required: true, message: '请输入员工姓名', trigger: 'blur' }],
        employeeNo: [{ required: true, message: '请输入工号', trigger: 'blur' }],
        oldDept: [{ required: true, message: '请选择原部门', trigger: 'change' }],
        oldPost: [{ required: true, message: '请输入原岗位', trigger: 'blur' }],
        newDept: [{ required: true, message: '请选择新部门', trigger: 'change' }],
        newPost: [{ required: true, message: '请输入新岗位', trigger: 'blur' }],
        transferDate: [{ required: true, message: '请选择调岗日期', trigger: 'change' }],
        reason: [{ required: true, message: '请输入调岗原因', trigger: 'blur' }]
      },
      deptOptions: [],
      uploadUrl: '/dev-api/upload',
      uploadHeaders: { Authorization: 'Bearer ' + getToken() }
    };
  },
  created() {
    this.getDeptOptions();
    this.getList();
  },
  methods: {
    getDeptOptions() {
      deptTreeSelect().then(res => {
        this.deptOptions = res.data || [];
        this.searchFields.find(f => f.prop === 'oldDept').options = this.deptOptions;
        this.searchFields.find(f => f.prop === 'newDept').options = this.deptOptions;
      });
    },
    getList() {
      listEmployeeTransfer({ ...this.searchForm, ...this.pagination }).then(res => {
        this.tableData = res.rows;
        this.pagination.total = res.total;
      });
    },
    handleSearch() {
      this.pagination.pageNum = 1;
      this.getList();
    },
    handlePageChange(page) {
      this.pagination.pageNum = page;
      this.getList();
    },
    openDialog(type, row) {
      this.dialogTitle = type === 'add' ? '新增调岗' : '编辑调岗';
      this.dialogVisible = true;
      if (type === 'edit' && row) {
        this.form = { ...row, attachmentList: row.attachment ? [{ name: row.attachment, url: row.attachment }] : [] };
      } else {
        this.form = { id: null, employeeName: '', employeeNo: '', oldDept: '', oldPost: '', newDept: '', newPost: '', transferDate: '', reason: '', attachment: '', attachmentList: [] };
      }
    },
    handleSubmit() {
      this.$refs.formRef.validate(valid => {
        if (!valid) return;
        const submitApi = this.form.id ? updateEmployeeTransfer : addEmployeeTransfer;
        const payload = { ...this.form, attachment: this.form.attachmentList.length ? this.form.attachmentList[0].url : '' };
        submitApi(payload).then(() => {
          this.$message.success('操作成功');
          this.dialogVisible = false;
          this.getList();
        });
      });
    },
    handleDelete(row) {
      this.$confirm('确定删除该调岗记录吗？', '提示', { type: 'warning' }).then(() => {
        deleteEmployeeTransfer(row.id).then(() => {
          this.$message.success('删除成功');
          this.getList();
        });
      });
    },
    handleImport() {
      importEmployeeTransfer().then(() => {
        this.$message.success('导入成功');
        this.getList();
      });
    },
    handleExport() {
      exportEmployeeTransfer(this.searchForm);
    },
    handleUploadSuccess(res, file, fileList) {
      this.form.attachmentList = fileList;
      this.form.attachment = res.url;
    }
  }
};
</script>

<style scoped>
.employee-transfer-container {
  padding: 24px;
}
.toolbar {
  margin-bottom: 16px;
}
</style> 