<template>
  <div class="leave-container">
    <CommonSearchBar :fields="searchFields" v-model="searchForm" @search="handleSearch" />
    <div class="toolbar">
      <el-button type="primary" icon="el-icon-plus" @click="openDialog('add')">新增请假</el-button>
      <el-button icon="el-icon-upload" @click="handleImport">导入</el-button>
      <el-button icon="el-icon-download" @click="handleExport">导出</el-button>
    </div>
    <CommonTable
      :columns="tableColumns"
      :data="tableData"
      :pagination="pagination"
      @edit="openDialog('edit', $event)"
      @delete="handleDelete"
      @page-change="handlePageChange"
    />
    <el-dialog :title="dialogTitle" :visible.sync="dialogVisible" width="600px">
      <el-form :model="form" :rules="rules" ref="formRef" label-width="100px">
        <el-form-item label="员工姓名" prop="employeeName">
          <el-input v-model="form.employeeName" placeholder="请输入员工姓名" />
        </el-form-item>
        <el-form-item label="工号" prop="employeeNo">
          <el-input v-model="form.employeeNo" placeholder="请输入工号" />
        </el-form-item>
        <el-form-item label="请假类型" prop="leaveType">
          <el-select v-model="form.leaveType" placeholder="请选择请假类型">
            <el-option label="事假" value="事假" />
            <el-option label="病假" value="病假" />
            <el-option label="年假" value="年假" />
            <el-option label="婚假" value="婚假" />
            <el-option label="产假" value="产假" />
            <el-option label="丧假" value="丧假" />
            <el-option label="调休" value="调休" />
          </el-select>
        </el-form-item>
        <el-form-item label="开始日期" prop="startDate">
          <el-date-picker v-model="form.startDate" type="date" placeholder="选择开始日期" style="width: 100%;" />
        </el-form-item>
        <el-form-item label="结束日期" prop="endDate">
          <el-date-picker v-model="form.endDate" type="date" placeholder="选择结束日期" style="width: 100%;" />
        </el-form-item>
        <el-form-item label="请假天数" prop="days">
          <el-input-number v-model="form.days" :min="0.5" :max="30" :step="0.5" />
        </el-form-item>
        <el-form-item label="请假原因" prop="reason">
          <el-input v-model="form.reason" type="textarea" placeholder="请输入请假原因" />
        </el-form-item>
        <el-form-item label="附件" prop="attachment">
          <el-upload
            class="upload-demo"
            :action="uploadUrl"
            :headers="uploadHeaders"
            :on-success="handleUploadSuccess"
            :file-list="form.attachmentList"
            :data="{ module: 'leave' }"
            :limit="3"
            list-type="text"
          >
            <el-button size="small" type="primary">上传文件</el-button>
          </el-upload>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="handleSubmit">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import CommonSearchBar from '@/components/CommonSearchBar/index.vue';
import CommonTable from '@/components/CommonTable/index.vue';
import { listLeave, addLeave, updateLeave, deleteLeave, importLeave, exportLeave } from '@/api/admin/leave';
import { getToken } from '@/utils/auth';

export default {
  name: 'Leave',
  components: { CommonSearchBar, CommonTable },
  data() {
    return {
      searchForm: {
        employeeName: '',
        employeeNo: '',
        leaveType: '',
        startDate: '',
        endDate: ''
      },
      searchFields: [
        { label: '员工姓名', prop: 'employeeName', type: 'input', placeholder: '请输入员工姓名' },
        { label: '工号', prop: 'employeeNo', type: 'input', placeholder: '请输入工号' },
        { label: '请假类型', prop: 'leaveType', type: 'select', options: [
          { label: '事假', value: '事假' },
          { label: '病假', value: '病假' },
          { label: '年假', value: '年假' },
          { label: '婚假', value: '婚假' },
          { label: '产假', value: '产假' },
          { label: '丧假', value: '丧假' },
          { label: '调休', value: '调休' }
        ], placeholder: '请选择请假类型' },
        { label: '开始日期', prop: 'startDate', type: 'date', placeholder: '请选择开始日期' },
        { label: '结束日期', prop: 'endDate', type: 'date', placeholder: '请选择结束日期' }
      ],
      tableColumns: [
        { label: '员工姓名', prop: 'employeeName' },
        { label: '工号', prop: 'employeeNo' },
        { label: '请假类型', prop: 'leaveType' },
        { label: '开始日期', prop: 'startDate' },
        { label: '结束日期', prop: 'endDate' },
        { label: '请假天数', prop: 'days' },
        { label: '请假原因', prop: 'reason' },
        { label: '附件', prop: 'attachment', slot: 'attachment' },
        { label: '操作', prop: 'action', slot: 'action', fixed: 'right', width: 180 }
      ],
      tableData: [],
      pagination: { pageNum: 1, pageSize: 10, total: 0 },
      dialogVisible: false,
      dialogTitle: '',
      form: {
        id: null,
        employeeName: '',
        employeeNo: '',
        leaveType: '',
        startDate: '',
        endDate: '',
        days: 1,
        reason: '',
        attachment: '',
        attachmentList: []
      },
      rules: {
        employeeName: [{ required: true, message: '请输入员工姓名', trigger: 'blur' }],
        employeeNo: [{ required: true, message: '请输入工号', trigger: 'blur' }],
        leaveType: [{ required: true, message: '请选择请假类型', trigger: 'change' }],
        startDate: [{ required: true, message: '请选择开始日期', trigger: 'change' }],
        endDate: [{ required: true, message: '请选择结束日期', trigger: 'change' }],
        days: [{ required: true, message: '请输入请假天数', trigger: 'blur' }],
        reason: [{ required: true, message: '请输入请假原因', trigger: 'blur' }]
      },
      uploadUrl: '/dev-api/upload',
      uploadHeaders: { Authorization: 'Bearer ' + getToken() }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    getList() {
      listLeave({ ...this.searchForm, ...this.pagination }).then(res => {
        this.tableData = res.rows;
        this.pagination.total = res.total;
      });
    },
    handleSearch() {
      this.pagination.pageNum = 1;
      this.getList();
    },
    handlePageChange(page) {
      this.pagination.pageNum = page;
      this.getList();
    },
    openDialog(type, row) {
      this.dialogTitle = type === 'add' ? '新增请假' : '编辑请假';
      this.dialogVisible = true;
      if (type === 'edit' && row) {
        this.form = { ...row, attachmentList: row.attachment ? [{ name: row.attachment, url: row.attachment }] : [] };
      } else {
        this.form = { id: null, employeeName: '', employeeNo: '', leaveType: '', startDate: '', endDate: '', days: 1, reason: '', attachment: '', attachmentList: [] };
      }
    },
    handleSubmit() {
      this.$refs.formRef.validate(valid => {
        if (!valid) return;
        const submitApi = this.form.id ? updateLeave : addLeave;
        const payload = { ...this.form, attachment: this.form.attachmentList.length ? this.form.attachmentList[0].url : '' };
        submitApi(payload).then(() => {
          this.$message.success('操作成功');
          this.dialogVisible = false;
          this.getList();
        });
      });
    },
    handleDelete(row) {
      this.$confirm('确定删除该请假记录吗？', '提示', { type: 'warning' }).then(() => {
        deleteLeave(row.id).then(() => {
          this.$message.success('删除成功');
          this.getList();
        });
      });
    },
    handleImport() {
      importLeave().then(() => {
        this.$message.success('导入成功');
        this.getList();
      });
    },
    handleExport() {
      exportLeave(this.searchForm);
    },
    handleUploadSuccess(res, file, fileList) {
      this.form.attachmentList = fileList;
      this.form.attachment = res.url;
    }
  }
};
</script>

<style scoped>
.leave-container {
  padding: 24px;
}
.toolbar {
  margin-bottom: 16px;
}
</style> 