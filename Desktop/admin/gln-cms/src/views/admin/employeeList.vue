<template>
  <div class="app-container">
    <CommonSearchBar :model-value="queryParams" @search="handleQuery" @reset="resetQuery">
      <el-form-item label="姓名" prop="name">
        <el-input v-model="queryParams.name" placeholder="请输入姓名" clearable style="width: 240px" @keyup.enter="handleQuery" />
      </el-form-item>
      <el-form-item label="部门" prop="dept">
        <el-select v-model="queryParams.dept" placeholder="请选择部门" clearable filterable style="width: 240px">
          <el-option v-for="item in deptOptions" :key="item.id" :label="item.label" :value="item.label" />
        </el-select>
      </el-form-item>
      <el-form-item label="入职日期" style="width: 308px">
        <el-date-picker v-model="queryParams.entryDateRange" value-format="YYYY-MM-DD" type="daterange" range-separator="-" start-placeholder="开始日期" end-placeholder="结束日期"></el-date-picker>
      </el-form-item>
    </CommonSearchBar>
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="Plus" @click="handleAdd">新增员工</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="success" plain icon="Edit" :disabled="single" @click="handleEditSelected">编辑</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDeleteSelected">删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="info" plain icon="Upload" @click="handleImport">批量导入</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" plain icon="Download" @click="handleExport">导出</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>
    <CommonTable
      :columns="columns"
      :data="employeeList"
      :loading="loading"
      :total="total"
      v-model:pageNum="pageNum"
      v-model:pageSize="pageSize"
      @pagination="getList"
      @selection-change="handleSelectionChange"
    >
      <template #operate="{ row }">
        <el-tooltip content="编辑" placement="top">
          <el-button link type="primary" icon="Edit" @click="handleEdit(row)"></el-button>
        </el-tooltip>
        <el-tooltip content="删除" placement="top">
          <el-button link type="primary" icon="Delete" @click="handleDelete(row)"></el-button>
        </el-tooltip>
      </template>
      <template #id_card_img="{ row }">
        <img v-if="row.id_card_img" :src="getImgUrl(row.id_card_img)" style="max-width: 60px;" />
      </template>
      <template #health_cert_img="{ row }">
        <img v-if="row.health_cert_img" :src="getImgUrl(row.health_cert_img)" style="max-width: 60px;" />
      </template>
    </CommonTable>
    <!-- 新增/编辑弹窗 -->
    <el-dialog :title="dialogTitle" v-model="dialogVisible" width="1200px" append-to-body>
      <el-form :model="form" :rules="rules" ref="formRef" label-width="110px">
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="员工编号" prop="emp_no">
              <el-input v-model="form.emp_no" maxlength="32" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="姓名" prop="name">
              <el-input v-model="form.name" maxlength="32" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="部门" prop="dept">
              <el-select v-model="form.dept" placeholder="请选择部门" filterable>
                <el-option v-for="item in deptOptions" :key="item.id" :label="item.label" :value="item.label" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="入职日期" prop="entry_date">
              <el-date-picker v-model="form.entry_date" type="date" value-format="YYYY-MM-DD" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="职位" prop="position">
              <el-input v-model="form.position" maxlength="64" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="性别" prop="gender">
              <el-select v-model="form.gender" placeholder="请选择">
                <el-option label="男" value="0" />
                <el-option label="女" value="1" />
                <el-option label="未知" value="2" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="政治面貌" prop="political">
              <el-input v-model="form.political" maxlength="32" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="年龄" prop="age">
              <el-input-number v-model="form.age" :min="0" :max="150" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="民族" prop="nation">
              <el-input v-model="form.nation" maxlength="32" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="学历" prop="education">
              <el-input v-model="form.education" maxlength="32" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="毕业院校" prop="graduate_school">
              <el-input v-model="form.graduate_school" maxlength="64" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="户籍地址" prop="address">
              <el-input v-model="form.address" maxlength="128" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="身份证号" prop="id_card">
              <el-input v-model="form.id_card" maxlength="32" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="手机号" prop="phone">
              <el-input v-model="form.phone" maxlength="20" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="银行信息" prop="bank_info">
              <el-input v-model="form.bank_info" maxlength="64" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="银行卡号" prop="bank_card">
              <el-input v-model="form.bank_card" maxlength="32" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="紧急联系人" prop="emergency_phone">
              <el-input v-model="form.emergency_phone" maxlength="20" placeholder="请输入紧急联系人以及联系电话" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="现居住地址" prop="current_addr">
              <el-input v-model="form.current_addr" maxlength="128" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="用工性质" prop="emp_type">
              <el-input v-model="form.emp_type" maxlength="32" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="是否缴纳社保" prop="social_security">
              <el-switch v-model="form.social_security" :active-value="1" :inactive-value="0" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="是否签劳动合同" prop="labor_contract">
              <el-switch v-model="form.labor_contract" :active-value="1" :inactive-value="0" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="员工状态" prop="emp_status">
              <el-input v-model="form.emp_status" maxlength="16" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="招聘信息渠道" prop="recruit_source">
              <el-input v-model="form.recruit_source" maxlength="64" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="离职日期" prop="leave_date">
              <el-date-picker v-model="form.leave_date" type="date" value-format="YYYY-MM-DD" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="是否办理健康证" prop="health_cert">
              <el-switch v-model="form.health_cert" :active-value="1" :inactive-value="0" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="身份证图片" prop="id_card_img">
              <el-upload
                class="upload-demo"
                action="/dev-api/common/upload"
                :show-file-list="false"
                :headers="{ Authorization: 'Bearer ' + (token || '') }"
                :on-success="(res) => form.id_card_img = res.data.url"
              >
                <el-button type="primary">上传身份证</el-button>
              </el-upload>
              <img v-if="form.id_card_img" :src="getImgUrl(form.id_card_img)" style="max-width: 60px; margin-left: 8px;" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="健康证图片" prop="health_cert_img">
              <el-upload
                class="upload-demo"
                action="/dev-api/common/upload"
                :show-file-list="false"
                :headers="{ Authorization: 'Bearer ' + (token || '') }"
                :on-success="(res) => form.health_cert_img = res.data.url"
              >
                <el-button type="primary">上传健康证</el-button>
              </el-upload>
              <img v-if="form.health_cert_img" :src="getImgUrl(form.health_cert_img)" style="max-width: 60px; margin-left: 8px;" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="备注" prop="remark">
              <el-input v-model="form.remark" maxlength="255" type="textarea" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="dialogVisible = false">取 消</el-button>
        </div>
      </template>
    </el-dialog>
    <!-- 导入对话框 -->
    <el-dialog :title="upload.title" v-model="upload.open" width="400px" append-to-body>
      <el-upload
        ref="uploadRef"
        :limit="1"
        accept=".xlsx, .xls"
        :headers="upload.headers"
        :action="upload.url + '?updateSupport=' + upload.updateSupport"
        :disabled="upload.isUploading"
        :on-progress="handleFileUploadProgress"
        :on-success="handleFileSuccess"
        :auto-upload="false"
        drag
      >
        <el-icon class="el-icon--upload"><upload-filled /></el-icon>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <template #tip>
          <div class="el-upload__tip text-center">
            <div class="el-upload__tip">
              <el-checkbox v-model="upload.updateSupport" true-value="1" false-value="0" />是否更新已经存在的员工数据
            </div>
            <span>仅允许导入xls、xlsx格式文件。</span>
            <el-link type="primary" :underline="false" style="font-size:12px;vertical-align: baseline;" @click="importTemplate">下载模板</el-link>
          </div>
        </template>
      </el-upload>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitFileForm">确 定</el-button>
          <el-button @click="upload.open = false">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed, getCurrentInstance } from 'vue'
import CommonSearchBar from '@/components/CommonSearchBar/index.vue'
import CommonTable from '@/components/CommonTable/index.vue'
import { listEmployee, addEmployee, updateEmployee, delEmployee, leaveEmployee, getEmployee } from '@/api/admin/employee'
import { deptTreeSelect as getDeptList } from '@/api/system/dept'
import { ElMessage, ElMessageBox } from 'element-plus'
import { isvalidatemobile, isvalidateID } from '@/utils/validate';
import { UploadFilled } from '@element-plus/icons-vue'

const { proxy } = getCurrentInstance();

const showSearch = ref(true)
const employeeList = ref([])
const total = ref(0)
const loading = ref(false)
const deptOptions = ref([])
const dialogVisible = ref(false)
const dialogTitle = ref('')
const form = reactive({})
const rules = reactive({
  emp_no: [{ required: true, message: '请输入员工编号', trigger: 'blur' }],
  name: [{ required: true, message: '请输入姓名', trigger: 'blur' }],
  dept: [{ required: true, message: '请选择部门', trigger: 'change' }],
  entry_date: [{ required: true, message: '请选择入职日期', trigger: 'change' }],
  position: [{ required: true, message: '请输入职位', trigger: 'blur' }],
  gender: [{ required: true, message: '请选择性别', trigger: 'change' }],
  political: [{ required: true, message: '请输入政治面貌', trigger: 'blur' }],
  age: [{ required: true, message: '请输入年龄', trigger: 'blur' }],
  nation: [{ required: true, message: '请输入民族', trigger: 'blur' }],
  education: [{ required: true, message: '请输入学历', trigger: 'blur' }],
  graduate_school: [{ required: true, message: '请输入毕业院校', trigger: 'blur' }],
  address: [{ required: true, message: '请输入户籍地址', trigger: 'blur' }],
  id_card: [
    { required: true, message: '请输入身份证号', trigger: 'blur' },
    { validator: (rule, value, callback) => {
      if (value && !isvalidateID(value)) {
        callback(new Error('请输入正确的身份证号'));
      } else {
        callback();
      }
    }, trigger: 'blur' }
  ],
  phone: [
    { required: true, message: '请输入手机号', trigger: 'blur' },
    { validator: (rule, value, callback) => {
      if (value && !isvalidatemobile(value)) {
        callback(new Error('请输入正确的手机号'));
      } else {
        callback();
      }
    }, trigger: 'blur' }
  ],
  bank_info: [{ required: true, message: '请输入银行信息', trigger: 'blur' }],
  bank_card: [{ required: true, message: '请输入银行卡号', trigger: 'blur' }],
  emergency_phone: [
    { required: true, message: '请输入紧急联系人电话', trigger: 'blur' },
     { validator: (rule, value, callback) => {
      if (value && !isvalidatemobile(value)) {
        callback(new Error('请输入正确的手机号'));
      } else {
        callback();
      }
    }, trigger: 'blur' }
  ],
  current_addr: [{ required: true, message: '请输入现居住地址', trigger: 'blur' }],
  emp_type: [{ required: true, message: '请输入用工性质', trigger: 'blur' }],
  social_security: [{ required: true, message: '请选择是否缴纳社保', trigger: 'change' }],
  labor_contract: [{ required: true, message: '请选择是否签劳动合同', trigger: 'change' }],
  emp_status: [{ required: true, message: '请输入员工状态', trigger: 'blur' }],
  recruit_source: [{ required: true, message: '请输入招聘信息渠道', trigger: 'blur' }],
  leave_date: [],
  health_cert: [{ required: true, message: '请选择是否办理健康证', trigger: 'change' }],
  id_card_img: [{ required: true, message: '请上传身份证图片', trigger: 'change' }],
  health_cert_img: [{ required: true, message: '请上传健康证图片', trigger: 'change' }],
  remark: [{ required: true, message: '请输入备注', trigger: 'blur' }],
})
const formRef = ref()
const ids = ref([])
const single = ref(true)
const multiple = ref(true)
const token = typeof window !== 'undefined' && window.localStorage ? window.localStorage.getItem('token') : '';

const queryParams = reactive({
  name: '',
  dept: '',
  entryDateRange: [],
  pageNum: 1,
  pageSize: 10
})

const pageNum = computed({
  get: () => Number(queryParams.pageNum),
  set: val => { queryParams.pageNum = Number(val) }
})
const pageSize = computed({
  get: () => Number(queryParams.pageSize),
  set: val => { queryParams.pageSize = Number(val) }
})

const columns = [
  { prop: 'emp_no', label: '员工编号', width: 120 },
  { prop: 'name', label: '姓名', width: 100 },
  { prop: 'entry_date', label: '入职日期', width: 120 },
  { prop: 'dept', label: '部门', width: 120 },
  { prop: 'position', label: '职位', width: 120 },
  { prop: 'gender', label: '性别', width: 80 },
  { prop: 'political', label: '政治面貌', width: 100 },
  { prop: 'age', label: '年龄', width: 80 },
  { prop: 'nation', label: '民族', width: 80 },
  { prop: 'education', label: '学历', width: 100 },
  { prop: 'graduate_school', label: '毕业院校', width: 120 },
  { prop: 'address', label: '户籍地址', width: 150 },
  { prop: 'id_card', label: '身份证号', width: 150 },
  { prop: 'phone', label: '手机号', width: 120 },
  { prop: 'bank_info', label: '银行信息', width: 120 },
  { prop: 'bank_card', label: '银行卡号', width: 120 },
  { prop: 'emergency_phone', label: '紧急联系人电话', width: 140 },
  { prop: 'current_addr', label: '现居住地址', width: 150 },
  { prop: 'emp_type', label: '用工性质', width: 100 },
  { prop: 'social_security', label: '社保', width: 80, slot: 'social_security' },
  { prop: 'labor_contract', label: '劳动合同', width: 100, slot: 'labor_contract' },
  { prop: 'emp_status', label: '员工状态', width: 100 },
  { prop: 'recruit_source', label: '招聘信息渠道', width: 120 },
  { prop: 'leave_date', label: '离职日期', width: 120 },
  { prop: 'health_cert', label: '健康证', width: 100, slot: 'health_cert' },
  { prop: 'id_card_img', label: '身份证图片', width: 120, slot: 'id_card_img' },
  { prop: 'health_cert_img', label: '健康证图片', width: 120, slot: 'health_cert_img' },
  { prop: 'remark', label: '备注', width: 150 },
  { label: '操作', width: 180, slot: 'operate', fixed: 'right' }
]

const upload = reactive({
  open: false,
  title: "",
  isUploading: false,
  updateSupport: 0,
  url: import.meta.env.VITE_APP_BASE_API + "/api/employee/importData",
  headers: { Authorization: "Bearer " + token }
});

function getList() {
  loading.value = true
  const params = {
    ...queryParams,
    pageNum: Number(queryParams.pageNum),
    pageSize: Number(queryParams.pageSize),
    entryDateStart: queryParams.entryDateRange?.[0] || '',
    entryDateEnd: queryParams.entryDateRange?.[1] || ''
  }
  listEmployee(params).then(res => {
    employeeList.value = res.list
    total.value = res.total
    loading.value = false
  }).catch(() => loading.value = false)
}

function handleQuery() {
  queryParams.pageNum = 1
  queryParams.pageNum = Number(queryParams.pageNum)
  queryParams.pageSize = Number(queryParams.pageSize)
  getList()
}
function resetQuery() {
  Object.assign(queryParams, { name: '', dept: '', entryDateRange: [], pageNum: 1, pageSize: 10 })
  queryParams.pageNum = Number(queryParams.pageNum)
  queryParams.pageSize = Number(queryParams.pageSize)
  getList()
}
function handleAdd() {
  dialogTitle.value = '新增员工'
  Object.assign(form, { emp_no: '', name: '', entry_date: '', dept: '', position: '', gender: '', political: '', age: '', nation: '', education: '', graduate_school: '', address: '', id_card: '', phone: '', bank_info: '', bank_card: '', emergency_phone: '', current_addr: '', emp_type: '', social_security: 0, labor_contract: 0, emp_status: '', recruit_source: '', leave_date: '', health_cert: 0, id_card_img: '', health_cert_img: '', remark: '' })
  dialogVisible.value = true
}
function handleEdit(row) {
  dialogTitle.value = '编辑员工'
  getEmployee(row.id).then(res => {
    Object.assign(form, res)
    dialogVisible.value = true
  })
}
function handleEditSelected() {
  if (ids.value.length === 1) {
    handleEdit({ id: ids.value[0] })
  }
}
function handleDelete(row) {
  ElMessageBox.confirm('确定要删除该员工吗？', '提示', { type: 'warning' }).then(() => {
    delEmployee(row.id).then(() => {
      ElMessage.success('删除成功')
      getList()
    })
  })
}
function handleDeleteSelected() {
  if (ids.value.length > 0) {
    ElMessageBox.confirm('确定要删除选中的员工吗？', '提示', { type: 'warning' }).then(() => {
      Promise.all(ids.value.map(id => delEmployee(id))).then(() => {
        ElMessage.success('批量删除成功')
        getList()
      })
    })
  }
}
function handleLeave(row) {
  ElMessageBox.confirm('确定将该员工设为离职吗？', '提示', { type: 'warning' }).then(() => {
    leaveEmployee(row.id, new Date().toISOString().slice(0, 10)).then(() => {
      ElMessage.success('操作成功')
      getList()
    })
  })
}
function submitForm() {
  formRef.value.validate(valid => {
    if (!valid) return
    const submitData = { ...form };
    if (submitData.leave_date === '') delete submitData.leave_date;
    if (form.id) {
      updateEmployee(submitData).then(() => {
        ElMessage.success('编辑成功')
        dialogVisible.value = false
        getList()
      })
    } else {
      addEmployee(submitData).then(() => {
        ElMessage.success('新增成功')
        dialogVisible.value = false
        getList()
      })
    }
  })
}
function getDeptOptions() {
  getDeptList().then(res => {
    const tree = res.data || []
    let secondLevel = []
    tree.forEach(root => {
      if (Array.isArray(root.children) && root.children.length > 0) {
        secondLevel.push(...root.children)
      }
    })
    deptOptions.value = secondLevel
  })
}
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.id)
  single.value = selection.length !== 1
  multiple.value = !selection.length
  queryParams.pageNum = Number(queryParams.pageNum)
  queryParams.pageSize = Number(queryParams.pageSize)
}

function handleImport() {
  upload.title = "员工导入";
  upload.open = true;
}

function importTemplate() {
  proxy.download("/employee/importTemplate", {}, `employee_template_${new Date().getTime()}.xlsx`);
}

const uploadRef = ref();
function submitFileForm() {
  uploadRef.value.submit();
}

function handleFileUploadProgress() {
  upload.isUploading = true;
}

function handleFileSuccess(response) {
  upload.open = false;
  upload.isUploading = false;
  uploadRef.value.clearFiles();
  ElMessageBox.alert("<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0 20px;'>" + response.msg + "</div>", "导入结果", { dangerouslyUseHTMLString: true });
  getList();
}

function randomString(len = 6) {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  let str = '';
  for (let i = 0; i < len; i++) {
    str += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return str;
}
function randomPhone() {
  return '1' + Math.floor(1000000000 + Math.random() * 9000000000);
}
function randomID() {
  return String(Math.floor(100000199999999999 + Math.random() * 800000000000000000));
}
function randomDate(start = '1990-01-01', end = '2022-12-31') {
  const startDate = new Date(start).getTime();
  const endDate = new Date(end).getTime();
  const date = new Date(startDate + Math.random() * (endDate - startDate));
  return date.toISOString().slice(0, 10);
}
function handleRandomAdd() {
  const dept = deptOptions.value.length ? deptOptions.value[Math.floor(Math.random() * deptOptions.value.length)].label : '测试部门';
  const genders = ['0', '1', '2'];
  const formData = {
    emp_no: randomString(8),
    name: '测试' + randomString(2),
    entry_date: randomDate('2018-01-01', '2023-12-31'),
    dept,
    position: '职员',
    gender: genders[Math.floor(Math.random() * 3)],
    political: '群众',
    age: Math.floor(20 + Math.random() * 30),
    nation: '汉族',
    education: '本科',
    graduate_school: '测试大学',
    address: '测试省测试市',
    id_card: randomID(),
    phone: randomPhone(),
    bank_info: '中国银行',
    bank_card: String(622202 + Math.floor(Math.random() * *************)),
    emergency_phone: randomPhone(),
    current_addr: '现居住地' + randomString(4),
    emp_type: '正式',
    social_security: 1,
    labor_contract: 1,
    emp_status: '在职',
    recruit_source: '网络招聘',
    leave_date: '',
    health_cert: 1,
    id_card_img: 'http:/localhost:8080/profile/5701750872917_.pic.jpg_1751435748973.jpeg',
    health_cert_img: 'http:/localhost:8080/profile/5701750872917_.pic.jpg_1751435748973.jpeg',
    remark: '自动生成测试数据'
  };
  if (formData.leave_date === '') delete formData.leave_date;
  addEmployee(formData).then(() => {
    ElMessage.success('随机员工添加成功');
    getList();
  }).catch(err => {
    ElMessage.error('添加失败：' + (err?.msg || err));
  });
}

function getImgUrl(url) {
  if (!url) return '';
  // 自动修正 http:/localhost:8080/xxx 为 http://localhost:8080/xxx
  url = url.replace(/^http:\/([^/])/, 'http://$1');
  if (/^https?:\/\//.test(url)) return url;
  return url;
}

onMounted(() => {
  getDeptOptions()
  getList()
})
</script> 