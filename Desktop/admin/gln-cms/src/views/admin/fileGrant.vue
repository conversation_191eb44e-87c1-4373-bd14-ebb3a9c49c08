<template>
  <div class="file-grant-container">
    <CommonSearchBar :fields="searchFields" v-model="searchForm" @search="handleSearch" />
    <div class="toolbar">
      <el-button type="primary" icon="el-icon-plus" @click="openDialog('add')">新增发放</el-button>
      <el-button icon="el-icon-upload" @click="handleImport">导入</el-button>
      <el-button icon="el-icon-download" @click="handleExport">导出</el-button>
    </div>
    <CommonTable
      :columns="tableColumns"
      :data="tableData"
      :pagination="pagination"
      @edit="openDialog('edit', $event)"
      @delete="handleDelete"
      @page-change="handlePageChange"
    />
    <el-dialog :title="dialogTitle" :visible.sync="dialogVisible" width="600px">
      <el-form :model="form" :rules="rules" ref="formRef" label-width="100px">
        <el-form-item label="文件名称" prop="fileName">
          <el-input v-model="form.fileName" placeholder="请输入文件名称" />
        </el-form-item>
        <el-form-item label="发放对象" prop="grantTo">
          <el-select v-model="form.grantTo" placeholder="请选择发放对象">
            <el-option v-for="item in userOptions" :key="item.id" :label="item.name" :value="item.id" />
          </el-select>
        </el-form-item>
        <el-form-item label="发放日期" prop="grantDate">
          <el-date-picker v-model="form.grantDate" type="date" placeholder="选择日期" style="width: 100%;" />
        </el-form-item>
        <el-form-item label="文件" prop="fileUrl">
          <el-upload
            class="upload-demo"
            :action="uploadUrl"
            :headers="uploadHeaders"
            :on-success="handleUploadSuccess"
            :file-list="form.fileList"
            :data="{ module: 'fileGrant' }"
            :limit="1"
            list-type="text"
          >
            <el-button size="small" type="primary">上传文件</el-button>
          </el-upload>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="handleSubmit">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import CommonSearchBar from '@/components/CommonSearchBar/index.vue';
import CommonTable from '@/components/CommonTable/index.vue';
import { listUser } from '@/api/system/user';
import { listFileGrant, addFileGrant, updateFileGrant, deleteFileGrant, importFileGrant, exportFileGrant } from '@/api/admin/fileGrant';
import { getToken } from '@/utils/auth';

export default {
  name: 'FileGrant',
  components: { CommonSearchBar, CommonTable },
  data() {
    return {
      searchForm: {
        fileName: '',
        grantTo: '',
        grantDate: ''
      },
      searchFields: [
        { label: '文件名称', prop: 'fileName', type: 'input', placeholder: '请输入文件名称' },
        { label: '发放对象', prop: 'grantTo', type: 'select', options: [], placeholder: '请选择发放对象' },
        { label: '发放日期', prop: 'grantDate', type: 'date', placeholder: '请选择发放日期' }
      ],
      tableColumns: [
        { label: '文件名称', prop: 'fileName' },
        { label: '发放对象', prop: 'grantToName' },
        { label: '发放日期', prop: 'grantDate' },
        { label: '文件', prop: 'fileUrl', slot: 'fileUrl' },
        { label: '备注', prop: 'remark' },
        { label: '操作', prop: 'action', slot: 'action', fixed: 'right', width: 180 }
      ],
      tableData: [],
      pagination: { pageNum: 1, pageSize: 10, total: 0 },
      dialogVisible: false,
      dialogTitle: '',
      form: {
        id: null,
        fileName: '',
        grantTo: '',
        grantDate: '',
        fileUrl: '',
        fileList: [],
        remark: ''
      },
      rules: {
        fileName: [{ required: true, message: '请输入文件名称', trigger: 'blur' }],
        grantTo: [{ required: true, message: '请选择发放对象', trigger: 'change' }],
        grantDate: [{ required: true, message: '请选择发放日期', trigger: 'change' }],
        fileUrl: [{ required: true, message: '请上传文件', trigger: 'change' }]
      },
      userOptions: [],
      uploadUrl: '/dev-api/upload',
      uploadHeaders: { Authorization: 'Bearer ' + getToken() }
    };
  },
  created() {
    this.getUserOptions();
    this.getList();
  },
  methods: {
    getUserOptions() {
      listUser().then(res => {
        this.userOptions = res.rows || [];
        this.searchFields.find(f => f.prop === 'grantTo').options = this.userOptions;
      });
    },
    getList() {
      listFileGrant({ ...this.searchForm, ...this.pagination }).then(res => {
        this.tableData = res.rows;
        this.pagination.total = res.total;
      });
    },
    handleSearch() {
      this.pagination.pageNum = 1;
      this.getList();
    },
    handlePageChange(page) {
      this.pagination.pageNum = page;
      this.getList();
    },
    openDialog(type, row) {
      this.dialogTitle = type === 'add' ? '新增发放' : '编辑发放';
      this.dialogVisible = true;
      if (type === 'edit' && row) {
        this.form = { ...row, fileList: row.fileUrl ? [{ name: row.fileName, url: row.fileUrl }] : [] };
      } else {
        this.form = { id: null, fileName: '', grantTo: '', grantDate: '', fileUrl: '', fileList: [], remark: '' };
      }
    },
    handleSubmit() {
      this.$refs.formRef.validate(valid => {
        if (!valid) return;
        const submitApi = this.form.id ? updateFileGrant : addFileGrant;
        const payload = { ...this.form, fileUrl: this.form.fileList.length ? this.form.fileList[0].url : '' };
        submitApi(payload).then(() => {
          this.$message.success('操作成功');
          this.dialogVisible = false;
          this.getList();
        });
      });
    },
    handleDelete(row) {
      this.$confirm('确定删除该发放记录吗？', '提示', { type: 'warning' }).then(() => {
        deleteFileGrant(row.id).then(() => {
          this.$message.success('删除成功');
          this.getList();
        });
      });
    },
    handleImport() {
      importFileGrant().then(() => {
        this.$message.success('导入成功');
        this.getList();
      });
    },
    handleExport() {
      exportFileGrant(this.searchForm);
    },
    handleUploadSuccess(res, file, fileList) {
      this.form.fileList = fileList;
      this.form.fileUrl = res.url;
    }
  }
};
</script>

<style scoped>
.file-grant-container {
  padding: 24px;
}
.toolbar {
  margin-bottom: 16px;
}
</style> 