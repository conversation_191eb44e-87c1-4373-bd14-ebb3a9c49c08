<template>
  <div class="office-supplies-container">
    <CommonSearchBar :fields="searchFields" v-model="searchForm" @search="handleSearch" />
    <div class="toolbar">
      <el-button type="primary" icon="el-icon-plus" @click="openDialog('add')">新增用品</el-button>
      <el-button icon="el-icon-upload" @click="handleImport">导入</el-button>
      <el-button icon="el-icon-download" @click="handleExport">导出</el-button>
    </div>
    <CommonTable
      :columns="tableColumns"
      :data="tableData"
      :pagination="pagination"
      @edit="openDialog('edit', $event)"
      @delete="handleDelete"
      @page-change="handlePageChange"
    />
    <el-dialog :title="dialogTitle" :visible.sync="dialogVisible" width="600px">
      <el-form :model="form" :rules="rules" ref="formRef" label-width="100px">
        <el-form-item label="用品名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入用品名称" />
        </el-form-item>
        <el-form-item label="类别" prop="category">
          <el-select v-model="form.category" placeholder="请选择类别">
            <el-option label="办公耗材" value="办公耗材" />
            <el-option label="办公设备" value="办公设备" />
            <el-option label="其他" value="其他" />
          </el-select>
        </el-form-item>
        <el-form-item label="数量" prop="quantity">
          <el-input-number v-model="form.quantity" :min="1" :max="9999" />
        </el-form-item>
        <el-form-item label="单位" prop="unit">
          <el-input v-model="form.unit" placeholder="请输入单位" />
        </el-form-item>
        <el-form-item label="领用人" prop="receiver">
          <el-input v-model="form.receiver" placeholder="请输入领用人" />
        </el-form-item>
        <el-form-item label="领用日期" prop="receiveDate">
          <el-date-picker v-model="form.receiveDate" type="date" placeholder="选择日期" style="width: 100%;" />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="handleSubmit">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import CommonSearchBar from '@/components/CommonSearchBar/index.vue';
import CommonTable from '@/components/CommonTable/index.vue';
import { listOfficeSupplies, addOfficeSupplies, updateOfficeSupplies, deleteOfficeSupplies, importOfficeSupplies, exportOfficeSupplies } from '@/api/admin/officeSupplies';

export default {
  name: 'OfficeSupplies',
  components: { CommonSearchBar, CommonTable },
  data() {
    return {
      searchForm: {
        name: '',
        category: '',
        receiver: '',
        receiveDate: ''
      },
      searchFields: [
        { label: '用品名称', prop: 'name', type: 'input', placeholder: '请输入用品名称' },
        { label: '类别', prop: 'category', type: 'select', options: [
          { label: '办公耗材', value: '办公耗材' },
          { label: '办公设备', value: '办公设备' },
          { label: '其他', value: '其他' }
        ], placeholder: '请选择类别' },
        { label: '领用人', prop: 'receiver', type: 'input', placeholder: '请输入领用人' },
        { label: '领用日期', prop: 'receiveDate', type: 'date', placeholder: '请选择领用日期' }
      ],
      tableColumns: [
        { label: '用品名称', prop: 'name' },
        { label: '类别', prop: 'category' },
        { label: '数量', prop: 'quantity' },
        { label: '单位', prop: 'unit' },
        { label: '领用人', prop: 'receiver' },
        { label: '领用日期', prop: 'receiveDate' },
        { label: '备注', prop: 'remark' },
        { label: '操作', prop: 'action', slot: 'action', fixed: 'right', width: 180 }
      ],
      tableData: [],
      pagination: { pageNum: 1, pageSize: 10, total: 0 },
      dialogVisible: false,
      dialogTitle: '',
      form: {
        id: null,
        name: '',
        category: '',
        quantity: 1,
        unit: '',
        receiver: '',
        receiveDate: '',
        remark: ''
      },
      rules: {
        name: [{ required: true, message: '请输入用品名称', trigger: 'blur' }],
        category: [{ required: true, message: '请选择类别', trigger: 'change' }],
        quantity: [{ required: true, message: '请输入数量', trigger: 'blur' }],
        unit: [{ required: true, message: '请输入单位', trigger: 'blur' }],
        receiver: [{ required: true, message: '请输入领用人', trigger: 'blur' }],
        receiveDate: [{ required: true, message: '请选择领用日期', trigger: 'change' }]
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    getList() {
      listOfficeSupplies({ ...this.searchForm, ...this.pagination }).then(res => {
        this.tableData = res.rows;
        this.pagination.total = res.total;
      });
    },
    handleSearch() {
      this.pagination.pageNum = 1;
      this.getList();
    },
    handlePageChange(page) {
      this.pagination.pageNum = page;
      this.getList();
    },
    openDialog(type, row) {
      this.dialogTitle = type === 'add' ? '新增用品' : '编辑用品';
      this.dialogVisible = true;
      if (type === 'edit' && row) {
        this.form = { ...row };
      } else {
        this.form = { id: null, name: '', category: '', quantity: 1, unit: '', receiver: '', receiveDate: '', remark: '' };
      }
    },
    handleSubmit() {
      this.$refs.formRef.validate(valid => {
        if (!valid) return;
        const submitApi = this.form.id ? updateOfficeSupplies : addOfficeSupplies;
        submitApi(this.form).then(() => {
          this.$message.success('操作成功');
          this.dialogVisible = false;
          this.getList();
        });
      });
    },
    handleDelete(row) {
      this.$confirm('确定删除该用品记录吗？', '提示', { type: 'warning' }).then(() => {
        deleteOfficeSupplies(row.id).then(() => {
          this.$message.success('删除成功');
          this.getList();
        });
      });
    },
    handleImport() {
      importOfficeSupplies().then(() => {
        this.$message.success('导入成功');
        this.getList();
      });
    },
    handleExport() {
      exportOfficeSupplies(this.searchForm);
    }
  }
};
</script>

<style scoped>
.office-supplies-container {
  padding: 24px;
}
.toolbar {
  margin-bottom: 16px;
}
</style> 