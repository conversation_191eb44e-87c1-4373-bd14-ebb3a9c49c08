<template>
  <div class="training-container">
    <CommonSearchBar :fields="searchFields" v-model="searchForm" @search="handleSearch" />
    <div class="toolbar">
      <el-button type="primary" icon="el-icon-plus" @click="openDialog('add')">新增培训</el-button>
      <el-button icon="el-icon-upload" @click="handleImport">导入</el-button>
      <el-button icon="el-icon-download" @click="handleExport">导出</el-button>
    </div>
    <CommonTable
      :columns="tableColumns"
      :data="tableData"
      :pagination="pagination"
      @edit="openDialog('edit', $event)"
      @delete="handleDelete"
      @page-change="handlePageChange"
    />
    <el-dialog :title="dialogTitle" :visible.sync="dialogVisible" width="600px">
      <el-form :model="form" :rules="rules" ref="formRef" label-width="100px">
        <el-form-item label="培训主题" prop="topic">
          <el-input v-model="form.topic" placeholder="请输入培训主题" />
        </el-form-item>
        <el-form-item label="培训类型" prop="type">
          <el-select v-model="form.type" placeholder="请选择培训类型">
            <el-option label="岗前培训" value="岗前培训" />
            <el-option label="技能提升" value="技能提升" />
            <el-option label="安全教育" value="安全教育" />
            <el-option label="其他" value="其他" />
          </el-select>
        </el-form-item>
        <el-form-item label="培训讲师" prop="teacher">
          <el-input v-model="form.teacher" placeholder="请输入讲师姓名" />
        </el-form-item>
        <el-form-item label="培训对象" prop="target">
          <el-input v-model="form.target" placeholder="请输入培训对象" />
        </el-form-item>
        <el-form-item label="培训时间" prop="date">
          <el-date-picker v-model="form.date" type="date" placeholder="选择日期" style="width: 100%;" />
        </el-form-item>
        <el-form-item label="培训时长(小时)" prop="duration">
          <el-input-number v-model="form.duration" :min="1" :max="48" />
        </el-form-item>
        <el-form-item label="培训内容" prop="content">
          <el-input v-model="form.content" type="textarea" placeholder="请输入培训内容" />
        </el-form-item>
        <el-form-item label="附件" prop="attachment">
          <el-upload
            class="upload-demo"
            :action="uploadUrl"
            :headers="uploadHeaders"
            :on-success="handleUploadSuccess"
            :file-list="form.attachmentList"
            :data="{ module: 'training' }"
            :limit="3"
            list-type="text"
          >
            <el-button size="small" type="primary">上传文件</el-button>
          </el-upload>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="handleSubmit">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import CommonSearchBar from '@/components/CommonSearchBar/index.vue';
import CommonTable from '@/components/CommonTable/index.vue';
import { listTraining, addTraining, updateTraining, deleteTraining, importTraining, exportTraining } from '@/api/admin/training';
import { getToken } from '@/utils/auth';

export default {
  name: 'Training',
  components: { CommonSearchBar, CommonTable },
  data() {
    return {
      searchForm: {
        topic: '',
        type: '',
        teacher: '',
        date: ''
      },
      searchFields: [
        { label: '培训主题', prop: 'topic', type: 'input', placeholder: '请输入培训主题' },
        { label: '培训类型', prop: 'type', type: 'select', options: [
          { label: '岗前培训', value: '岗前培训' },
          { label: '技能提升', value: '技能提升' },
          { label: '安全教育', value: '安全教育' },
          { label: '其他', value: '其他' }
        ], placeholder: '请选择培训类型' },
        { label: '讲师', prop: 'teacher', type: 'input', placeholder: '请输入讲师姓名' },
        { label: '培训时间', prop: 'date', type: 'date', placeholder: '请选择培训时间' }
      ],
      tableColumns: [
        { label: '培训主题', prop: 'topic' },
        { label: '培训类型', prop: 'type' },
        { label: '讲师', prop: 'teacher' },
        { label: '培训对象', prop: 'target' },
        { label: '培训时间', prop: 'date' },
        { label: '时长(小时)', prop: 'duration' },
        { label: '培训内容', prop: 'content' },
        { label: '附件', prop: 'attachment', slot: 'attachment' },
        { label: '操作', prop: 'action', slot: 'action', fixed: 'right', width: 180 }
      ],
      tableData: [],
      pagination: { pageNum: 1, pageSize: 10, total: 0 },
      dialogVisible: false,
      dialogTitle: '',
      form: {
        id: null,
        topic: '',
        type: '',
        teacher: '',
        target: '',
        date: '',
        duration: 1,
        content: '',
        attachment: '',
        attachmentList: []
      },
      rules: {
        topic: [{ required: true, message: '请输入培训主题', trigger: 'blur' }],
        type: [{ required: true, message: '请选择培训类型', trigger: 'change' }],
        teacher: [{ required: true, message: '请输入讲师姓名', trigger: 'blur' }],
        date: [{ required: true, message: '请选择培训时间', trigger: 'change' }],
        duration: [{ required: true, message: '请输入培训时长', trigger: 'blur' }],
        content: [{ required: true, message: '请输入培训内容', trigger: 'blur' }]
      },
      uploadUrl: '/dev-api/upload',
      uploadHeaders: { Authorization: 'Bearer ' + getToken() }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    getList() {
      listTraining({ ...this.searchForm, ...this.pagination }).then(res => {
        this.tableData = res.rows;
        this.pagination.total = res.total;
      });
    },
    handleSearch() {
      this.pagination.pageNum = 1;
      this.getList();
    },
    handlePageChange(page) {
      this.pagination.pageNum = page;
      this.getList();
    },
    openDialog(type, row) {
      this.dialogTitle = type === 'add' ? '新增培训' : '编辑培训';
      this.dialogVisible = true;
      if (type === 'edit' && row) {
        this.form = { ...row, attachmentList: row.attachment ? [{ name: row.attachment, url: row.attachment }] : [] };
      } else {
        this.form = { id: null, topic: '', type: '', teacher: '', target: '', date: '', duration: 1, content: '', attachment: '', attachmentList: [] };
      }
    },
    handleSubmit() {
      this.$refs.formRef.validate(valid => {
        if (!valid) return;
        const submitApi = this.form.id ? updateTraining : addTraining;
        const payload = { ...this.form, attachment: this.form.attachmentList.length ? this.form.attachmentList[0].url : '' };
        submitApi(payload).then(() => {
          this.$message.success('操作成功');
          this.dialogVisible = false;
          this.getList();
        });
      });
    },
    handleDelete(row) {
      this.$confirm('确定删除该培训记录吗？', '提示', { type: 'warning' }).then(() => {
        deleteTraining(row.id).then(() => {
          this.$message.success('删除成功');
          this.getList();
        });
      });
    },
    handleImport() {
      importTraining().then(() => {
        this.$message.success('导入成功');
        this.getList();
      });
    },
    handleExport() {
      exportTraining(this.searchForm);
    },
    handleUploadSuccess(res, file, fileList) {
      this.form.attachmentList = fileList;
      this.form.attachment = res.url;
    }
  }
};
</script>

<style scoped>
.training-container {
  padding: 24px;
}
.toolbar {
  margin-bottom: 16px;
}
</style> 