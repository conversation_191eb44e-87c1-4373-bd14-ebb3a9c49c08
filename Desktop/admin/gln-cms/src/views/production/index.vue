<template>
  <div class="app-container">
    <el-row :gutter="20">
      <el-col :span="24">
        <el-card>
          <template #header>
            <div class="card-header">
              <span class="card-title">生产制造部</span>
              <el-tag type="info">生产制造</el-tag>
            </div>
          </template>
          
          <div class="department-intro">
            <h3>部门简介</h3>
            <p>生产制造部负责公司产品生产制造、工艺管理、设备维护、生产安全等职能，确保产品质量和生产效率。</p>
          </div>

          <el-divider />

          <el-row :gutter="20">
            <el-col :span="8">
              <el-card class="feature-card" shadow="hover">
                <template #header>
                  <div class="feature-header">
                    <el-icon><Operation /></el-icon>
                    <span>生产管理</span>
                  </div>
                </template>
                <div class="feature-content">
                  <p>生产计划执行、生产调度、工艺管理等生产工作</p>
                  <el-button type="primary" size="small" @click="goToModule('management')">进入模块</el-button>
                </div>
              </el-card>
            </el-col>
            
            <el-col :span="8">
              <el-card class="feature-card" shadow="hover">
                <template #header>
                  <div class="feature-header">
                    <el-icon><Tools /></el-icon>
                    <span>设备维护</span>
                  </div>
                </template>
                <div class="feature-content">
                  <p>设备保养、维修管理、设备改造等维护工作</p>
                  <el-button type="primary" size="small" @click="goToModule('maintenance')">进入模块</el-button>
                </div>
              </el-card>
            </el-col>
            
            <el-col :span="8">
              <el-card class="feature-card" shadow="hover">
                <template #header>
                  <div class="feature-header">
                    <el-icon><Warning /></el-icon>
                    <span>安全管理</span>
                  </div>
                </template>
                <div class="feature-content">
                  <p>安全生产管理、安全检查、安全培训等安全工作</p>
                  <el-button type="primary" size="small" @click="goToModule('safety')">进入模块</el-button>
                </div>
              </el-card>
            </el-col>
          </el-row>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup name="Production">
import { Operation, Tools, Warning } from '@element-plus/icons-vue'

const goToModule = (module) => {
  console.log('进入模块:', module)
}
</script>

<style scoped lang="scss">
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-title {
  font-size: 18px;
  font-weight: bold;
  color: #303133;
}

.department-intro {
  margin-bottom: 20px;
  
  h3 {
    color: #409EFF;
    margin-bottom: 10px;
  }
  
  p {
    color: #606266;
    line-height: 1.6;
  }
}

.feature-card {
  margin-bottom: 20px;
  
  .feature-header {
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: bold;
    color: #409EFF;
  }
  
  .feature-content {
    text-align: center;
    
    p {
      color: #606266;
      margin-bottom: 15px;
      line-height: 1.5;
    }
  }
}
</style> 