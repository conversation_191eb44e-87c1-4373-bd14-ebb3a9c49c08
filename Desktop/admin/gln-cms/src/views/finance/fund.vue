<template>
  <div class="app-container">
    <el-card>
      <template #header>
        <div class="card-header">
          <span class="card-title">资金管理</span>
          <el-tag type="success">财务资金部</el-tag>
        </div>
      </template>
      
      <div class="module-content">
        <h3>资金管理模块</h3>
        <p>负责公司资金计划、资金调度、银行关系维护等核心资金管理工作。</p>
        
        <el-divider />
        
        <el-row :gutter="20">
          <el-col :span="6">
            <el-statistic title="总资金余额" :value="12345678" prefix="¥" />
          </el-col>
          <el-col :span="6">
            <el-statistic title="可用资金" :value="8765432" prefix="¥" />
          </el-col>
          <el-col :span="6">
            <el-statistic title="冻结资金" :value="1234567" prefix="¥" />
          </el-col>
          <el-col :span="6">
            <el-statistic title="今日收支" :value="234567" prefix="¥" />
          </el-col>
        </el-row>
        
        <el-divider />
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-card shadow="hover">
              <template #header>
                <span>资金计划</span>
              </template>
              <p>制定年度、季度、月度资金计划，确保资金合理配置</p>
              <el-button type="primary" size="small">查看详情</el-button>
            </el-card>
          </el-col>
          <el-col :span="12">
            <el-card shadow="hover">
              <template #header>
                <span>资金调度</span>
              </template>
              <p>根据业务需要，合理调度资金，提高资金使用效率</p>
              <el-button type="primary" size="small">查看详情</el-button>
            </el-card>
          </el-col>
        </el-row>
      </div>
    </el-card>
  </div>
</template>

<script setup name="FinanceFund">
// 资金管理模块逻辑
</script>

<style scoped lang="scss">
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-title {
  font-size: 18px;
  font-weight: bold;
  color: #303133;
}

.module-content {
  h3 {
    color: #409EFF;
    margin-bottom: 10px;
  }
  
  p {
    color: #606266;
    line-height: 1.6;
    margin-bottom: 20px;
  }
}
</style> 