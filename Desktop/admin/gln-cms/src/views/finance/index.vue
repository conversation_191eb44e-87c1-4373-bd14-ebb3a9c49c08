<template>
  <div class="app-container">
    <el-row :gutter="20">
      <el-col :span="24">
        <el-card>
          <template #header>
            <div class="card-header">
              <span class="card-title">财务资金部</span>
              <el-tag type="success">资金管理</el-tag>
            </div>
          </template>
          
          <div class="department-intro">
            <h3>部门简介</h3>
            <p>财务资金部负责公司财务管理、资金运作、成本控制、财务分析等核心财务职能，确保公司财务健康稳定发展。</p>
          </div>

          <el-divider />

          <el-row :gutter="20">
            <el-col :span="8">
              <el-card class="feature-card" shadow="hover">
                <template #header>
                  <div class="feature-header">
                    <el-icon><Money /></el-icon>
                    <span>资金管理</span>
                  </div>
                </template>
                <div class="feature-content">
                  <p>负责公司资金计划、资金调度、银行关系维护等工作</p>
                  <el-button type="primary" size="small" @click="goToModule('fund')">进入模块</el-button>
                </div>
              </el-card>
            </el-col>
            
            <el-col :span="8">
              <el-card class="feature-card" shadow="hover">
                <template #header>
                  <div class="feature-header">
                    <el-icon><Document /></el-icon>
                    <span>会计核算</span>
                  </div>
                </template>
                <div class="feature-content">
                  <p>负责日常会计核算、财务报表编制、税务申报等工作</p>
                  <el-button type="primary" size="small" @click="goToModule('accounting')">进入模块</el-button>
                </div>
              </el-card>
            </el-col>
            
            <el-col :span="8">
              <el-card class="feature-card" shadow="hover">
                <template #header>
                  <div class="feature-header">
                    <el-icon><TrendCharts /></el-icon>
                    <span>财务分析</span>
                  </div>
                </template>
                <div class="feature-content">
                  <p>负责财务数据分析、成本控制、预算管理等分析工作</p>
                  <el-button type="primary" size="small" @click="goToModule('analysis')">进入模块</el-button>
                </div>
              </el-card>
            </el-col>
          </el-row>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup name="Finance">
import { Money, Document, TrendCharts } from '@element-plus/icons-vue'

const goToModule = (module) => {
  console.log('进入模块:', module)
  // 这里可以添加路由跳转逻辑
}
</script>

<style scoped lang="scss">
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-title {
  font-size: 18px;
  font-weight: bold;
  color: #303133;
}

.department-intro {
  margin-bottom: 20px;
  
  h3 {
    color: #409EFF;
    margin-bottom: 10px;
  }
  
  p {
    color: #606266;
    line-height: 1.6;
  }
}

.feature-card {
  margin-bottom: 20px;
  
  .feature-header {
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: bold;
    color: #409EFF;
  }
  
  .feature-content {
    text-align: center;
    
    p {
      color: #606266;
      margin-bottom: 15px;
      line-height: 1.5;
    }
  }
}
</style> 