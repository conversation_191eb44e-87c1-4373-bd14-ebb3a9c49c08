<template>
  <div class="app-container">
    <el-row :gutter="20">
      <el-col :span="24">
        <el-card>
          <template #header>
            <div class="card-header">
              <span class="card-title">物资采购部</span>
              <el-tag type="info">采购管理</el-tag>
            </div>
          </template>
          
          <div class="department-intro">
            <h3>部门简介</h3>
            <p>物资采购部负责公司各类物资、设备、服务的采购工作，确保采购质量、控制采购成本、优化供应链管理。</p>
          </div>

          <el-divider />

          <el-row :gutter="20">
            <el-col :span="8">
              <el-card class="feature-card" shadow="hover">
                <template #header>
                  <div class="feature-header">
                    <el-icon><ShoppingCart /></el-icon>
                    <span>采购计划</span>
                  </div>
                </template>
                <div class="feature-content">
                  <p>制定采购计划、供应商管理、采购预算控制等工作</p>
                  <el-button type="primary" size="small" @click="goToModule('plan')">进入模块</el-button>
                </div>
              </el-card>
            </el-col>
            
            <el-col :span="8">
              <el-card class="feature-card" shadow="hover">
                <template #header>
                  <div class="feature-header">
                    <el-icon><Goods /></el-icon>
                    <span>供应商管理</span>
                  </div>
                </template>
                <div class="feature-content">
                  <p>供应商评估、资质审核、合作关系维护等管理工作</p>
                  <el-button type="primary" size="small" @click="goToModule('supplier')">进入模块</el-button>
                </div>
              </el-card>
            </el-col>
            
            <el-col :span="8">
              <el-card class="feature-card" shadow="hover">
                <template #header>
                  <div class="feature-header">
                    <el-icon><Box /></el-icon>
                    <span>库存管理</span>
                  </div>
                </template>
                <div class="feature-content">
                  <p>物资入库、出库、盘点、库存预警等库存管理工作</p>
                  <el-button type="primary" size="small" @click="goToModule('inventory')">进入模块</el-button>
                </div>
              </el-card>
            </el-col>
          </el-row>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup name="Purchase">
import { ShoppingCart, Goods, Box } from '@element-plus/icons-vue'

const goToModule = (module) => {
  console.log('进入模块:', module)
}
</script>

<style scoped lang="scss">
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-title {
  font-size: 18px;
  font-weight: bold;
  color: #303133;
}

.department-intro {
  margin-bottom: 20px;
  
  h3 {
    color: #409EFF;
    margin-bottom: 10px;
  }
  
  p {
    color: #606266;
    line-height: 1.6;
  }
}

.feature-card {
  margin-bottom: 20px;
  
  .feature-header {
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: bold;
    color: #409EFF;
  }
  
  .feature-content {
    text-align: center;
    
    p {
      color: #606266;
      margin-bottom: 15px;
      line-height: 1.5;
    }
  }
}
</style> 