<template>
  <div class="app-container">
    <el-row :gutter="20">
      <el-col :span="24">
        <el-card>
          <template #header>
            <div class="card-header">
              <span class="card-title">质量管理部</span>
              <el-tag type="danger">质量管控</el-tag>
            </div>
          </template>
          
          <div class="department-intro">
            <h3>部门简介</h3>
            <p>质量管理部负责公司产品质量管理、质量体系建设、质量检验检测等职能，确保产品质量符合标准要求。</p>
          </div>

          <el-divider />

          <el-row :gutter="20">
            <el-col :span="8">
              <el-card class="feature-card" shadow="hover">
                <template #header>
                  <div class="feature-header">
                    <el-icon><Check /></el-icon>
                    <span>质量检验</span>
                  </div>
                </template>
                <div class="feature-content">
                  <p>产品检验、质量检测、不合格品处理等检验工作</p>
                  <el-button type="primary" size="small" @click="goToModule('inspection')">进入模块</el-button>
                </div>
              </el-card>
            </el-col>
            
            <el-col :span="8">
              <el-card class="feature-card" shadow="hover">
                <template #header>
                  <div class="feature-header">
                    <el-icon><Setting /></el-icon>
                    <span>质量体系</span>
                  </div>
                </template>
                <div class="feature-content">
                  <p>质量管理体系建立、维护、改进等体系管理工作</p>
                  <el-button type="primary" size="small" @click="goToModule('system')">进入模块</el-button>
                </div>
              </el-card>
            </el-col>
            
            <el-col :span="8">
              <el-card class="feature-card" shadow="hover">
                <template #header>
                  <div class="feature-header">
                    <el-icon><Warning /></el-icon>
                    <span>质量改进</span>
                  </div>
                </template>
                <div class="feature-content">
                  <p>质量问题分析、改进措施制定、预防措施实施等工作</p>
                  <el-button type="primary" size="small" @click="goToModule('improvement')">进入模块</el-button>
                </div>
              </el-card>
            </el-col>
          </el-row>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup name="Quality">
import { Check, Setting, Warning } from '@element-plus/icons-vue'

const goToModule = (module) => {
  console.log('进入模块:', module)
}
</script>

<style scoped lang="scss">
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-title {
  font-size: 18px;
  font-weight: bold;
  color: #303133;
}

.department-intro {
  margin-bottom: 20px;
  
  h3 {
    color: #409EFF;
    margin-bottom: 10px;
  }
  
  p {
    color: #606266;
    line-height: 1.6;
  }
}

.feature-card {
  margin-bottom: 20px;
  
  .feature-header {
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: bold;
    color: #409EFF;
  }
  
  .feature-content {
    text-align: center;
    
    p {
      color: #606266;
      margin-bottom: 15px;
      line-height: 1.5;
    }
  }
}
</style> 