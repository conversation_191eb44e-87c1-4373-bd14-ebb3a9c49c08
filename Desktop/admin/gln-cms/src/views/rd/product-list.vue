<template>
  <div class="app-container">
    <el-card>
      <template #header>
        <div class="card-header">
          <span class="card-title">产品列表管理</span>
          <el-button type="primary" @click="handleAdd">新增产品</el-button>
        </div>
      </template>

      <!-- 查询条件 -->
      <CommonSearchBar
        v-model="queryParams"
        v-show="showSearch"
        @search="handleQuery"
        @reset="resetQuery"
      >
        <el-form-item label="产品名称" prop="productName">
          <el-input
            v-model="queryParams.productName"
            placeholder="请输入产品名称"
            clearable
            style="width: 200px"
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item label="产品型号" prop="productModel">
          <el-input
            v-model="queryParams.productModel"
            placeholder="请输入产品型号"
            clearable
            style="width: 200px"
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item label="BOM状态" prop="bomStatus">
          <el-select v-model="queryParams.bomStatus" placeholder="请选择BOM状态" clearable style="width: 200px">
            <el-option label="样品" value="样品" />
            <el-option label="试制品" value="试制品" />
            <el-option label="正式品" value="正式品" />
          </el-select>
        </el-form-item>
      </CommonSearchBar>

      <!-- 操作按钮 -->
      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button
            type="primary"
            plain
            icon="Plus"
            @click="handleAdd"
            v-hasPermi="['rd:product:add']"
          >新增</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="success"
            plain
            icon="Edit"
            :disabled="single"
            @click="handleUpdate"
            v-hasPermi="['rd:product:edit']"
          >修改</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="danger"
            plain
            icon="Delete"
            :disabled="multiple"
            @click="handleDelete"
            v-hasPermi="['rd:product:remove']"
          >删除</el-button>
        </el-col>
        <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
      </el-row>

      <!-- 数据表格 -->
      <CommonTable
        :columns="columns"
        :data="productList"
        :loading="loading"
        :total="total"
        :page-num="queryParams.pageNum"
        :page-size="queryParams.pageSize"
        @selection-change="handleSelectionChange"
        @update:page-num="val => queryParams.pageNum = val"
        @update:page-size="val => queryParams.pageSize = val"
        @pagination="getList"
      >
        <template #bomStatus="{ row }">
          <el-tag :type="getBomStatusType(row.bomStatus)">
            {{ row.bomStatus }}
          </el-tag>
        </template>
        <template #drawing="{ row }">
          <el-image
            v-if="row.drawing"
            :src="row.drawing"
            :preview-src-list="[row.drawing]"
            fit="cover"
            style="width: 50px; height: 50px"
          />
          <span v-else>无图纸</span>
        </template>
        <template #operate="{ row }">
          <el-button
            size="small"
            type="text"
            icon="Edit"
            @click="handleUpdate(row)"
            v-hasPermi="['rd:product:edit']"
          >编辑</el-button>
          <el-button
            size="small"
            type="text"
            icon="Setting"
            @click="handleAccessories(row)"
            v-hasPermi="['rd:product:accessories']"
          >配件</el-button>
          <el-button
            size="small"
            type="text"
            icon="View"
            @click="handleView(row)"
            v-hasPermi="['rd:product:view']"
          >查看</el-button>
          <el-button
            size="small"
            type="text"
            icon="Delete"
            @click="handleDelete(row)"
            v-hasPermi="['rd:product:remove']"
          >删除</el-button>
        </template>
      </CommonTable>
    </el-card>

    <!-- 添加或修改产品对话框 -->
    <el-dialog :title="title" v-model="open" width="800px" append-to-body>
      <el-form ref="productFormRef" :model="form" :rules="rules" label-width="120px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="产品名称" prop="productName">
              <el-input v-model="form.productName" placeholder="请输入产品名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="产品型号" prop="productModel">
              <el-input v-model="form.productModel" placeholder="请输入产品型号" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="BOM状态" prop="bomStatus">
              <el-select v-model="form.bomStatus" placeholder="请选择BOM状态" style="width: 100%">
                <el-option label="样品" value="样品" />
                <el-option label="试制品" value="试制品" />
                <el-option label="正式品" value="正式品" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="产品重量" prop="weight">
              <el-input-number v-model="form.weight" :precision="2" :step="0.1" :min="0" style="width: 100%" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="单位" prop="unit">
              <el-select v-model="form.unit" placeholder="请选择单位" style="width: 100%">
                <el-option label="套" value="套" />
                <el-option label="个" value="个" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="图纸" prop="drawing">
              <el-upload
                ref="drawingUploadRef"
                :limit="1"
                accept="image/*"
                :action="uploadFileUrl"
                :headers="headers"
                :file-list="drawingFileList"
                :on-success="handleDrawingSuccess"
                :auto-upload="false"
                list-type="picture-card"
              >
                <el-icon><Plus /></el-icon>
              </el-upload>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 配件管理对话框 -->
    <el-dialog title="配件管理" v-model="accessoriesOpen" width="1200px" append-to-body>
      <div class="accessories-container">
        <!-- 上半部分：产品信息 -->
        <el-card class="product-info" shadow="never">
          <template #header>
            <div class="product-header">
              <span>产品信息</span>
            </div>
          </template>
          <el-descriptions :column="3" border>
            <el-descriptions-item label="产品名称">{{ currentProduct.productName }}</el-descriptions-item>
            <el-descriptions-item label="产品型号">{{ currentProduct.productModel }}</el-descriptions-item>
            <el-descriptions-item label="BOM状态">
              <el-tag :type="getBomStatusType(currentProduct.bomStatus)">
                {{ currentProduct.bomStatus }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="产品重量">{{ currentProduct.weight }} {{ currentProduct.unit }}</el-descriptions-item>
            <el-descriptions-item label="单位">{{ currentProduct.unit }}</el-descriptions-item>
            <el-descriptions-item label="创建时间">{{ currentProduct.createTime }}</el-descriptions-item>
          </el-descriptions>
        </el-card>

        <!-- 下半部分：配件列表 -->
        <el-card class="accessories-list" shadow="never">
          <template #header>
            <div class="accessories-header">
              <span>配件列表</span>
              <el-button type="primary" size="small" @click="handleAddAccessory">添加配件</el-button>
            </div>
          </template>
          
          <el-table :data="accessoriesList" border style="width: 100%">
            <el-table-column type="index" label="序号" width="60" />
            <el-table-column prop="subItemNumber" label="子件编号" width="150" />
            <el-table-column prop="isSubstitute" label="是否替代料件" width="120">
              <template #default="{ row }">
                <el-checkbox v-model="row.isSubstitute" disabled />
              </template>
            </el-table-column>
            <el-table-column prop="productName" label="产品名称" minWidth="150" />
            <el-table-column prop="specification" label="产品规格" width="120" />
            <el-table-column prop="otherInfo" label="其他/箱号" width="120" />
            <el-table-column prop="standardBatch" label="标准批量" width="100" />
            <el-table-column prop="batchUsage" label="批量用量" width="100" />
            <el-table-column prop="netUsage" label="净用量" width="100" />
            <el-table-column prop="basicUnit" label="基本单位" width="100" />
            <el-table-column prop="materialSource" label="材料来源" width="120" />
            <el-table-column label="操作" width="120">
              <template #default="{ row, $index }">
                <el-button size="small" type="text" icon="Edit" @click="handleEditAccessory(row, $index)">编辑</el-button>
                <el-button size="small" type="text" icon="Delete" @click="handleDeleteAccessory($index)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </div>

      <!-- 添加配件对话框 -->
      <el-dialog title="添加配件" v-model="accessoryDialogOpen" width="800px" append-to-body>
        <el-form ref="accessoryFormRef" :model="accessoryForm" :rules="accessoryRules" label-width="120px">
          <el-row>
            <el-col :span="12">
              <el-form-item label="子件编号" prop="subItemNumber">
                <el-input v-model="accessoryForm.subItemNumber" placeholder="请输入子件编号" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="是否替代料件" prop="isSubstitute">
                <el-checkbox v-model="accessoryForm.isSubstitute" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="12">
              <el-form-item label="产品名称" prop="productName">
                <el-input v-model="accessoryForm.productName" placeholder="请输入产品名称" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="产品规格" prop="specification">
                <el-input v-model="accessoryForm.specification" placeholder="请输入产品规格" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="12">
              <el-form-item label="其他/箱号" prop="otherInfo">
                <el-input v-model="accessoryForm.otherInfo" placeholder="请输入其他/箱号" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="标准批量" prop="standardBatch">
                <el-input-number v-model="accessoryForm.standardBatch" :precision="3" :step="0.001" :min="0" style="width: 100%" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="12">
              <el-form-item label="批量用量" prop="batchUsage">
                <el-input-number v-model="accessoryForm.batchUsage" :precision="3" :step="0.001" :min="0" style="width: 100%" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="净用量" prop="netUsage">
                <el-input-number v-model="accessoryForm.netUsage" :precision="3" :step="0.001" :min="0" style="width: 100%" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="12">
              <el-form-item label="基本单位" prop="basicUnit">
                <el-select v-model="accessoryForm.basicUnit" placeholder="请选择基本单位" style="width: 100%">
                  <el-option label="KG" value="KG" />
                  <el-option label="只" value="只" />
                  <el-option label="根" value="根" />
                  <el-option label="个" value="个" />
                  <el-option label="瓶" value="瓶" />
                  <el-option label="张" value="张" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="材料来源" prop="materialSource">
                <el-select v-model="accessoryForm.materialSource" placeholder="请选择材料来源" style="width: 100%">
                  <el-option label="自备料件" value="自备料件" />
                  <el-option label="外购料件" value="外购料件" />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
        <template #footer>
          <div class="dialog-footer">
            <el-button type="primary" @click="submitAccessoryForm">确 定</el-button>
            <el-button @click="cancelAccessory">取 消</el-button>
          </div>
        </template>
      </el-dialog>
    </el-dialog>

    <!-- 查看产品对话框 -->
    <el-dialog title="产品详情" v-model="viewOpen" width="1000px" append-to-body>
      <div class="product-detail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="产品名称">{{ currentProduct.productName }}</el-descriptions-item>
          <el-descriptions-item label="产品型号">{{ currentProduct.productModel }}</el-descriptions-item>
          <el-descriptions-item label="BOM状态">
            <el-tag :type="getBomStatusType(currentProduct.bomStatus)">
              {{ currentProduct.bomStatus }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="产品重量">{{ currentProduct.weight }} {{ currentProduct.unit }}</el-descriptions-item>
          <el-descriptions-item label="单位">{{ currentProduct.unit }}</el-descriptions-item>
          <el-descriptions-item label="创建时间">{{ currentProduct.createTime }}</el-descriptions-item>
        </el-descriptions>
        
        <div class="drawing-section">
          <h4>产品图纸</h4>
          <el-image
            v-if="currentProduct.drawing"
            :src="currentProduct.drawing"
            :preview-src-list="[currentProduct.drawing]"
            fit="contain"
            style="max-width: 100%; max-height: 400px"
          />
          <el-empty v-else description="暂无图纸" />
        </div>

        <div class="accessories-section">
          <h4>配件列表</h4>
          <el-table :data="currentProduct.accessories || []" border style="width: 100%">
            <el-table-column type="index" label="序号" width="60" />
            <el-table-column prop="subItemNumber" label="子件编号" width="150" />
            <el-table-column prop="isSubstitute" label="是否替代料件" width="120">
              <template #default="{ row }">
                <el-tag :type="row.isSubstitute ? 'warning' : 'info'">
                  {{ row.isSubstitute ? '是' : '否' }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="productName" label="产品名称" minWidth="150" />
            <el-table-column prop="specification" label="产品规格" width="120" />
            <el-table-column prop="otherInfo" label="其他/箱号" width="120" />
            <el-table-column prop="standardBatch" label="标准批量" width="100" />
            <el-table-column prop="batchUsage" label="批量用量" width="100" />
            <el-table-column prop="netUsage" label="净用量" width="100" />
            <el-table-column prop="basicUnit" label="基本单位" width="100" />
            <el-table-column prop="materialSource" label="材料来源" width="120" />
          </el-table>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup name="ProductList">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { getToken } from '@/utils/auth'
import { Plus } from '@element-plus/icons-vue'
import CommonSearchBar from '@/components/CommonSearchBar/index.vue'
import CommonTable from '@/components/CommonTable/index.vue'
import { 
  listProduct, 
  getProduct, 
  addProduct, 
  updateProduct, 
  delProduct,
  getProductAccessories,
  updateProductAccessories
} from '@/api/rd/product-list'

// 遮罩层
const loading = ref(true)
// 选中数组
const ids = ref([])
// 非单个禁用
const single = ref(true)
// 非多个禁用
const multiple = ref(true)
// 显示搜索条件
const showSearch = ref(true)
// 总条数
const total = ref(0)
// 产品列表数据
const productList = ref([])
// 弹出层标题
const title = ref("")
// 是否显示弹出层
const open = ref(false)
// 配件管理弹出层
const accessoriesOpen = ref(false)
// 查看产品弹出层
const viewOpen = ref(false)
// 当前产品信息
const currentProduct = ref({})
// 配件列表
const accessoriesList = ref([])
// 添加配件对话框
const accessoryDialogOpen = ref(false)
// 编辑配件索引
const editAccessoryIndex = ref(-1)
// 文件上传地址
const uploadFileUrl = ref(import.meta.env.VITE_APP_BASE_API + "/common/upload")
// 图纸文件列表
const drawingFileList = ref([])
// 上传文件请求头
const headers = ref({ Authorization: "Bearer " + getToken() })

// 查询参数
const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  productName: undefined,
  productModel: undefined,
  bomStatus: undefined
})

// 表单参数
const form = reactive({
  id: undefined,
  productName: undefined,
  productModel: undefined,
  bomStatus: undefined,
  weight: undefined,
  unit: undefined,
  drawing: undefined
})

// 配件表单参数
const accessoryForm = reactive({
  subItemNumber: undefined,
  isSubstitute: false,
  productName: undefined,
  specification: undefined,
  otherInfo: undefined,
  standardBatch: undefined,
  batchUsage: undefined,
  netUsage: undefined,
  basicUnit: undefined,
  materialSource: undefined
})

// 表单校验规则
const rules = reactive({
  productName: [
    { required: true, message: "产品名称不能为空", trigger: "blur" }
  ],
  productModel: [
    { required: true, message: "产品型号不能为空", trigger: "blur" }
  ],
  bomStatus: [
    { required: true, message: "BOM状态不能为空", trigger: "change" }
  ],
  weight: [
    { required: true, message: "产品重量不能为空", trigger: "blur" }
  ],
  unit: [
    { required: true, message: "单位不能为空", trigger: "change" }
  ]
})

// 配件表单校验规则
const accessoryRules = reactive({
  subItemNumber: [
    { required: true, message: "子件编号不能为空", trigger: "blur" }
  ],
  productName: [
    { required: true, message: "产品名称不能为空", trigger: "blur" }
  ],
  basicUnit: [
    { required: true, message: "基本单位不能为空", trigger: "change" }
  ],
  materialSource: [
    { required: true, message: "材料来源不能为空", trigger: "change" }
  ]
})

// 表格列配置
const columns = ref([
  { type: 'selection', width: 55 },
  { prop: 'id', label: '序号', width: 80 },
  { prop: 'productName', label: '产品名称', minWidth: 200 },
  { prop: 'productModel', label: '产品型号', width: 150 },
  { prop: 'bomStatus', label: 'BOM状态', width: 100, slot: true },
  { prop: 'drawing', label: '图纸', width: 100, slot: true },
  { prop: 'weight', label: '产品重量', width: 100 },
  { prop: 'unit', label: '单位', width: 80 },
  { prop: 'createTime', label: '创建时间', width: 180 },
  { prop: 'operate', label: '操作', width: 250, slot: true }
])

/** 获取BOM状态类型 */
function getBomStatusType(status) {
  const statusMap = {
    '样品': 'warning',
    '试制品': 'info',
    '正式品': 'success'
  }
  return statusMap[status] || 'info'
}

/** 查询产品列表 */
function getList() {
  loading.value = true
  listProduct(queryParams).then(response => {
    productList.value = response.rows
    total.value = response.total
    loading.value = false
  })
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.pageNum = 1
  getList()
}

/** 重置按钮操作 */
function resetQuery() {
  queryParams.productName = undefined
  queryParams.productModel = undefined
  queryParams.bomStatus = undefined
  handleQuery()
}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.id)
  single.value = selection.length !== 1
  multiple.value = !selection.length
}

/** 新增按钮操作 */
function handleAdd() {
  reset()
  open.value = true
  title.value = "添加产品"
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset()
  const id = row.id || ids.value[0]
  getProduct(id).then(response => {
    Object.assign(form, response.data)
    open.value = true
    title.value = "修改产品"
  })
}

/** 配件按钮操作 */
function handleAccessories(row) {
  currentProduct.value = row
  accessoriesList.value = row.accessories || []
  accessoriesOpen.value = true
}

/** 查看按钮操作 */
function handleView(row) {
  currentProduct.value = row
  viewOpen.value = true
}

/** 图纸上传成功处理 */
function handleDrawingSuccess(response, file, fileList) {
  form.drawing = response.url
  ElMessage.success("图纸上传成功")
}

/** 提交按钮 */
function submitForm() {
  productFormRef.value.validate((valid) => {
    if (valid) {
      if (form.id != null) {
        updateProduct(form).then(response => {
          ElMessage.success("修改成功")
          open.value = false
          getList()
        })
      } else {
        addProduct(form).then(response => {
          ElMessage.success("新增成功")
          open.value = false
          getList()
        })
      }
    }
  })
}

/** 删除按钮操作 */
function handleDelete(row) {
  const dataIds = row.id || ids.value
  ElMessageBox.confirm('是否确认删除产品编号为"' + dataIds + '"的数据项?', "警告", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning"
  }).then(function() {
    return delProduct(dataIds)
  }).then(() => {
    getList()
    ElMessage.success("删除成功")
  })
}

/** 取消按钮 */
function cancel() {
  open.value = false
  reset()
}

/** 表单重置 */
function reset() {
  form.id = undefined
  form.productName = undefined
  form.productModel = undefined
  form.bomStatus = undefined
  form.weight = undefined
  form.unit = undefined
  form.drawing = undefined
  drawingFileList.value = []
}

/** 添加配件 */
function handleAddAccessory() {
  resetAccessoryForm()
  accessoryDialogOpen.value = true
  editAccessoryIndex.value = -1
}

/** 编辑配件 */
function handleEditAccessory(row, index) {
  Object.assign(accessoryForm, row)
  accessoryDialogOpen.value = true
  editAccessoryIndex.value = index
}

/** 删除配件 */
function handleDeleteAccessory(index) {
  accessoriesList.value.splice(index, 1)
}

/** 提交配件表单 */
function submitAccessoryForm() {
  accessoryFormRef.value.validate((valid) => {
    if (valid) {
      if (editAccessoryIndex.value >= 0) {
        // 编辑配件
        Object.assign(accessoriesList.value[editAccessoryIndex.value], accessoryForm)
      } else {
        // 新增配件
        accessoriesList.value.push({ ...accessoryForm })
      }
      accessoryDialogOpen.value = false
      resetAccessoryForm()
    }
  })
}

/** 取消配件表单 */
function cancelAccessory() {
  accessoryDialogOpen.value = false
  resetAccessoryForm()
}

/** 重置配件表单 */
function resetAccessoryForm() {
  accessoryForm.subItemNumber = undefined
  accessoryForm.isSubstitute = false
  accessoryForm.productName = undefined
  accessoryForm.specification = undefined
  accessoryForm.otherInfo = undefined
  accessoryForm.standardBatch = undefined
  accessoryForm.batchUsage = undefined
  accessoryForm.netUsage = undefined
  accessoryForm.basicUnit = undefined
  accessoryForm.materialSource = undefined
}

onMounted(() => {
  getList()
})
</script>

<style scoped lang="scss">
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-title {
  font-size: 18px;
  font-weight: bold;
  color: #303133;
}

.accessories-container {
  .product-info {
    margin-bottom: 20px;
    
    .product-header {
      font-weight: bold;
      color: #409EFF;
    }
  }
  
  .accessories-list {
    .accessories-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-weight: bold;
      color: #409EFF;
    }
  }
}

.product-detail {
  .drawing-section {
    margin: 20px 0;
    
    h4 {
      color: #409EFF;
      margin-bottom: 10px;
    }
  }
  
  .accessories-section {
    margin-top: 20px;
    
    h4 {
      color: #409EFF;
      margin-bottom: 10px;
    }
  }
}
</style> 