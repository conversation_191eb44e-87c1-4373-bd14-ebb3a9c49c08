<template>
  <div class="app-container">
    <el-card>
      <template #header>
        <div class="card-header">
          <span class="card-title">技术资料管理</span>
          <el-button type="primary" @click="handleAdd">新增资料</el-button>
        </div>
      </template>

      <!-- 查询条件 -->
      <CommonSearchBar
        v-model="queryParams"
        v-show="showSearch"
        @search="handleQuery"
        @reset="resetQuery"
      >
        <el-form-item label="资料名称" prop="dataName">
          <el-input
            v-model="queryParams.dataName"
            placeholder="请输入资料名称"
            clearable
            style="width: 200px"
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item label="订单号" prop="orderNumber">
          <el-input
            v-model="queryParams.orderNumber"
            placeholder="请输入订单号"
            clearable
            style="width: 200px"
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item label="产品型号" prop="productModel">
          <el-input
            v-model="queryParams.productModel"
            placeholder="请输入产品型号"
            clearable
            style="width: 200px"
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item label="使用时间" prop="useTime">
          <el-date-picker
            v-model="queryParams.useTime"
            type="date"
            placeholder="请选择使用时间"
            clearable
            style="width: 200px"
          />
        </el-form-item>
      </CommonSearchBar>

      <!-- 操作按钮 -->
      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button
            type="primary"
            plain
            icon="Plus"
            @click="handleAdd"
            v-hasPermi="['rd:technical-data:add']"
          >新增</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="success"
            plain
            icon="Edit"
            :disabled="single"
            @click="handleUpdate"
            v-hasPermi="['rd:technical-data:edit']"
          >修改</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="danger"
            plain
            icon="Delete"
            :disabled="multiple"
            @click="handleDelete"
            v-hasPermi="['rd:technical-data:remove']"
          >删除</el-button>
        </el-col>
        <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
      </el-row>

      <!-- 数据表格 -->
      <CommonTable
        :columns="columns"
        :data="dataList"
        :loading="loading"
        :total="total"
        :page-num="queryParams.pageNum"
        :page-size="queryParams.pageSize"
        @selection-change="handleSelectionChange"
        @update:page-num="val => queryParams.pageNum = val"
        @update:page-size="val => queryParams.pageSize = val"
        @pagination="getList"
      >
        <template #operate="{ row }">
          <el-button
            size="small"
            type="text"
            icon="Edit"
            @click="handleUpdate(row)"
            v-hasPermi="['rd:technical-data:edit']"
          >编辑</el-button>
          <el-button
            size="small"
            type="text"
            icon="View"
            @click="handlePreview(row)"
            v-hasPermi="['rd:technical-data:preview']"
          >预览</el-button>
          <el-button
            size="small"
            type="text"
            icon="Download"
            @click="handleDownload(row)"
            v-hasPermi="['rd:technical-data:download']"
          >下载</el-button>
          <el-button
            size="small"
            type="text"
            icon="Delete"
            @click="handleDelete(row)"
            v-hasPermi="['rd:technical-data:remove']"
          >删除</el-button>
        </template>
      </CommonTable>
    </el-card>

    <!-- 添加或修改技术资料对话框 -->
    <el-dialog :title="title" v-model="open" width="800px" append-to-body>
      <el-form ref="dataFormRef" :model="form" :rules="rules" label-width="120px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="资料名称" prop="dataName">
              <el-input v-model="form.dataName" placeholder="请输入资料名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="订单号" prop="orderNumber">
              <el-select v-model="form.orderNumber" placeholder="请选择订单号" style="width: 100%">
                <el-option
                  v-for="item in orderOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="产品型号" prop="productModel">
              <el-select v-model="form.productModel" placeholder="请选择产品型号" style="width: 100%">
                <el-option
                  v-for="item in productOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="使用时间" prop="useTime">
              <el-date-picker
                v-model="form.useTime"
                type="date"
                placeholder="请选择使用时间"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="上传文件" prop="file">
              <el-upload
                ref="uploadRef"
                :limit="1"
                accept=".pdf,.doc,.docx,.xls,.xlsx,.jpg,.jpeg,.png"
                :action="uploadFileUrl"
                :headers="headers"
                :file-list="fileList"
                :on-progress="handleFileUploadProgress"
                :on-success="handleFileSuccess"
                :auto-upload="false"
                drag
              >
                <el-icon class="el-icon--upload"><upload-filled /></el-icon>
                <div class="el-upload__text">
                  将文件拖到此处，或<em>点击上传</em>
                </div>
                <template #tip>
                  <div class="el-upload__tip">
                    只能上传pdf/doc/docx/xls/xlsx/jpg/jpeg/png文件，且不超过10MB
                  </div>
                </template>
              </el-upload>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 预览对话框 -->
    <el-dialog title="文件预览" v-model="previewOpen" width="80%" append-to-body>
      <div class="preview-container">
        <iframe v-if="previewUrl" :src="previewUrl" width="100%" height="600px"></iframe>
        <div v-else class="preview-placeholder">
          <el-empty description="暂无预览内容" />
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup name="TechnicalData">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { getToken } from '@/utils/auth'
import { UploadFilled } from '@element-plus/icons-vue'
import CommonSearchBar from '@/components/CommonSearchBar/index.vue'
import CommonTable from '@/components/CommonTable/index.vue'
import { 
  listTechnicalData, 
  getTechnicalData, 
  addTechnicalData, 
  updateTechnicalData, 
  delTechnicalData,
  downloadTechnicalData
} from '@/api/rd/technical-data'

// 遮罩层
const loading = ref(true)
// 选中数组
const ids = ref([])
// 非单个禁用
const single = ref(true)
// 非多个禁用
const multiple = ref(true)
// 显示搜索条件
const showSearch = ref(true)
// 总条数
const total = ref(0)
// 技术资料表格数据
const dataList = ref([])
// 弹出层标题
const title = ref("")
// 是否显示弹出层
const open = ref(false)
// 预览弹出层
const previewOpen = ref(false)
// 预览URL
const previewUrl = ref("")
// 文件上传地址
const uploadFileUrl = ref(import.meta.env.VITE_APP_BASE_API + "/common/upload")
// 文件列表
const fileList = ref([])
// 上传文件请求头
const headers = ref({ Authorization: "Bearer " + getToken() })

// 订单选项
const orderOptions = ref([
  { label: "订单001", value: "ORDER001" },
  { label: "订单002", value: "ORDER002" },
  { label: "订单003", value: "ORDER003" }
])

// 产品选项
const productOptions = ref([
  { label: "产品A", value: "PRODUCT_A" },
  { label: "产品B", value: "PRODUCT_B" },
  { label: "产品C", value: "PRODUCT_C" }
])

// 查询参数
const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  dataName: undefined,
  orderNumber: undefined,
  productModel: undefined,
  useTime: undefined
})

// 表单参数
const form = reactive({
  id: undefined,
  dataName: undefined,
  orderNumber: undefined,
  productModel: undefined,
  useTime: undefined,
  fileName: undefined,
  fileUrl: undefined
})

// 表单校验规则
const rules = reactive({
  dataName: [
    { required: true, message: "资料名称不能为空", trigger: "blur" }
  ],
  orderNumber: [
    { required: true, message: "订单号不能为空", trigger: "change" }
  ],
  productModel: [
    { required: true, message: "产品型号不能为空", trigger: "change" }
  ],
  useTime: [
    { required: true, message: "使用时间不能为空", trigger: "change" }
  ]
})

// 表格列配置
const columns = ref([
  { type: 'selection', width: 55 },
  { prop: 'id', label: '序号', width: 80 },
  { prop: 'dataName', label: '资料名称', minWidth: 200 },
  { prop: 'orderNumber', label: '订单号', width: 150 },
  { prop: 'productModel', label: '产品型号', width: 150 },
  { prop: 'useTime', label: '使用时间', width: 120 },
  { prop: 'submitter', label: '提交人', width: 120 },
  { prop: 'createTime', label: '创建时间', width: 180 },
  { prop: 'operate', label: '操作', width: 200, slot: true }
])

/** 查询技术资料列表 */
function getList() {
  loading.value = true
  listTechnicalData(queryParams).then(response => {
    dataList.value = response.rows
    total.value = response.total
    loading.value = false
  })
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.pageNum = 1
  getList()
}

/** 重置按钮操作 */
function resetQuery() {
  queryParams.dataName = undefined
  queryParams.orderNumber = undefined
  queryParams.productModel = undefined
  queryParams.useTime = undefined
  handleQuery()
}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.id)
  single.value = selection.length !== 1
  multiple.value = !selection.length
}

/** 新增按钮操作 */
function handleAdd() {
  reset()
  open.value = true
  title.value = "添加技术资料"
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset()
  const id = row.id || ids.value[0]
  getTechnicalData(id).then(response => {
    Object.assign(form, response.data)
    open.value = true
    title.value = "修改技术资料"
  })
}

/** 预览按钮操作 */
function handlePreview(row) {
  previewUrl.value = row.fileUrl
  previewOpen.value = true
}

/** 下载按钮操作 */
function handleDownload(row) {
  downloadTechnicalData(row.id).then(response => {
    const blob = new Blob([response.data])
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = row.fileName || '技术资料'
    link.click()
    window.URL.revokeObjectURL(url)
  })
}

/** 提交按钮 */
function submitForm() {
  dataFormRef.value.validate((valid) => {
    if (valid) {
      if (form.id != null) {
        updateTechnicalData(form).then(response => {
          ElMessage.success("修改成功")
          open.value = false
          getList()
        })
      } else {
        addTechnicalData(form).then(response => {
          ElMessage.success("新增成功")
          open.value = false
          getList()
        })
      }
    }
  })
}

/** 删除按钮操作 */
function handleDelete(row) {
  const dataIds = row.id || ids.value
  ElMessageBox.confirm('是否确认删除技术资料编号为"' + dataIds + '"的数据项?', "警告", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning"
  }).then(function() {
    return delTechnicalData(dataIds)
  }).then(() => {
    getList()
    ElMessage.success("删除成功")
  })
}

/** 取消按钮 */
function cancel() {
  open.value = false
  reset()
}

/** 表单重置 */
function reset() {
  form.id = undefined
  form.dataName = undefined
  form.orderNumber = undefined
  form.productModel = undefined
  form.useTime = undefined
  form.fileName = undefined
  form.fileUrl = undefined
  fileList.value = []
}

/** 文件上传中处理 */
function handleFileUploadProgress(event, file, fileList) {
  // 文件上传进度处理
}

/** 文件上传成功处理 */
function handleFileSuccess(response, file, fileList) {
  form.fileName = file.name
  form.fileUrl = response.url
  ElMessage.success("文件上传成功")
}

onMounted(() => {
  getList()
})
</script>

<style scoped lang="scss">
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-title {
  font-size: 18px;
  font-weight: bold;
  color: #303133;
}

.preview-container {
  .preview-placeholder {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 400px;
  }
}
</style> 