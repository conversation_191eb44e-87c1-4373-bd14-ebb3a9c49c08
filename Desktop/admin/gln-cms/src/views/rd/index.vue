<template>
  <div class="app-container">
    <el-row :gutter="20">
      <el-col :span="24">
        <el-card>
          <template #header>
            <div class="card-header">
              <span class="card-title">技术研发部</span>
              <el-tag type="success">技术研发</el-tag>
            </div>
          </template>
          
          <div class="department-intro">
            <h3>部门简介</h3>
            <p>技术研发部负责公司产品技术研发、技术创新、技术标准制定等职能，推动公司技术进步和产品升级。</p>
          </div>

          <el-divider />

          <el-row :gutter="20">
            <el-col :span="8">
              <el-card class="feature-card" shadow="hover">
                <template #header>
                  <div class="feature-header">
                    <el-icon><Tools /></el-icon>
                    <span>产品研发</span>
                  </div>
                </template>
                <div class="feature-content">
                  <p>技术资料管理、产品列表管理、BOM子件管理等研发工作</p>
                  <el-button type="primary" size="small" @click="goToModule('technical-data')">技术资料</el-button>
                  <el-button type="primary" size="small" @click="goToModule('product')">产品列表</el-button>
                  <el-button type="primary" size="small" @click="goToModule('bom-sub-item')">BOM子件</el-button>
                </div>
              </el-card>
            </el-col>
            
            <el-col :span="8">
              <el-card class="feature-card" shadow="hover">
                <template #header>
                  <div class="feature-header">
                    <el-icon><Setting /></el-icon>
                    <span>技术创新</span>
                  </div>
                </template>
                <div class="feature-content">
                  <p>产品研发、技术创新、技术标准制定等创新工作</p>
                  <el-button type="primary" size="small" @click="goToModule('technical-data')">技术资料</el-button>
                  <el-button type="primary" size="small" @click="goToModule('product')">产品列表</el-button>
                  <el-button type="primary" size="small" @click="goToModule('bom-sub-item')">BOM子件</el-button>
                </div>
              </el-card>
            </el-col>
            
            <el-col :span="8">
              <el-card class="feature-card" shadow="hover">
                <template #header>
                  <div class="feature-header">
                    <el-icon><Document /></el-icon>
                    <span>技术标准</span>
                  </div>
                </template>
                <div class="feature-content">
                  <p>技术标准制定、技术文档管理、技术培训等工作</p>
                  <el-button type="primary" size="small" @click="goToModule('technical-data')">技术资料</el-button>
                  <el-button type="primary" size="small" @click="goToModule('product')">产品列表</el-button>
                  <el-button type="primary" size="small" @click="goToModule('bom-sub-item')">BOM子件</el-button>
                </div>
              </el-card>
            </el-col>
          </el-row>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup name="RD">
import { Tools, Document, Setting } from '@element-plus/icons-vue'
import { useRouter } from 'vue-router'

const router = useRouter()

const goToModule = (module) => {
  switch (module) {
    case 'technical-data':
      router.push('/rd/technical-data')
      break
    case 'product':
      router.push('/rd/product-list')
      break
    case 'bom-sub-item':
      router.push('/rd/bom-sub-item')
      break
    default:
      console.log('进入模块:', module)
  }
}
</script>

<style scoped lang="scss">
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-title {
  font-size: 18px;
  font-weight: bold;
  color: #303133;
}

.department-intro {
  margin-bottom: 20px;
  
  h3 {
    color: #409EFF;
    margin-bottom: 10px;
  }
  
  p {
    color: #606266;
    line-height: 1.6;
  }
}

.feature-card {
  margin-bottom: 20px;
  
  .feature-header {
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: bold;
    color: #409EFF;
  }
  
  .feature-content {
    text-align: center;
    
    p {
      color: #606266;
      margin-bottom: 15px;
      line-height: 1.5;
    }
  }
}
</style> 