<template>
  <div class="app-container">
    <el-card>
      <template #header>
        <div class="card-header">
          <span class="card-title">BOM子件管理</span>
          <el-button type="primary" @click="handleAdd">新增子件</el-button>
        </div>
      </template>

      <!-- 查询条件 -->
      <CommonSearchBar
        v-model="queryParams"
        v-show="showSearch"
        @search="handleQuery"
        @reset="resetQuery"
      >
        <el-form-item label="物料属性" prop="materialAttribute">
          <el-select v-model="queryParams.materialAttribute" placeholder="请选择物料属性" clearable style="width: 200px">
            <el-option
              v-for="item in materialAttributeOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="物料类别" prop="materialCategory">
          <el-select v-model="queryParams.materialCategory" placeholder="请选择物料类别" clearable style="width: 200px">
            <el-option
              v-for="item in filteredMaterialCategoryOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="子件编号" prop="subItemNumber">
          <el-input
            v-model="queryParams.subItemNumber"
            placeholder="请输入子件编号"
            clearable
            style="width: 200px"
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item label="产品名称" prop="productName">
          <el-input
            v-model="queryParams.productName"
            placeholder="请输入产品名称"
            clearable
            style="width: 200px"
            @keyup.enter="handleQuery"
          />
        </el-form-item>
      </CommonSearchBar>

      <!-- 操作按钮 -->
      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button
            type="primary"
            plain
            icon="Plus"
            @click="handleAdd"
            v-hasPermi="['rd:bom-sub-item:add']"
          >新增</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="success"
            plain
            icon="Edit"
            :disabled="single"
            @click="handleUpdate"
            v-hasPermi="['rd:bom-sub-item:edit']"
          >修改</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="danger"
            plain
            icon="Delete"
            :disabled="multiple"
            @click="handleDelete"
            v-hasPermi="['rd:bom-sub-item:remove']"
          >删除</el-button>
        </el-col>
        <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
      </el-row>

      <!-- 数据表格 -->
      <CommonTable
        :columns="columns"
        :data="subItemList"
        :loading="loading"
        :total="total"
        :page-num="queryParams.pageNum"
        :page-size="queryParams.pageSize"
        @selection-change="handleSelectionChange"
        @update:page-num="val => queryParams.pageNum = val"
        @update:page-size="val => queryParams.pageSize = val"
        @pagination="getList"
      >
        <template #operate="{ row }">
          <el-button
            size="small"
            type="text"
            icon="Edit"
            @click="handleUpdate(row)"
            v-hasPermi="['rd:bom-sub-item:edit']"
          >编辑</el-button>
          <el-button
            size="small"
            type="text"
            icon="Delete"
            @click="handleDelete(row)"
            v-hasPermi="['rd:bom-sub-item:remove']"
          >删除</el-button>
        </template>
      </CommonTable>
    </el-card>

    <!-- 添加或修改BOM子件对话框 -->
    <el-dialog :title="title" v-model="open" width="1000px" append-to-body>
      <el-form ref="subItemFormRef" :model="form" :rules="rules" label-width="120px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="物料属性" prop="materialAttribute">
              <el-select v-model="form.materialAttribute" placeholder="请选择物料属性" style="width: 100%" @change="handleMaterialAttributeChange">
                <el-option
                  v-for="item in materialAttributeOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="物料类别" prop="materialCategory">
              <el-select v-model="form.materialCategory" placeholder="请选择物料类别" style="width: 100%">
                <el-option
                  v-for="item in filteredMaterialCategoryOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="子件编号" prop="subItemNumber">
              <el-input v-model="form.subItemNumber" placeholder="请输入子件编号" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="产品名称" prop="productName">
              <el-input v-model="form.productName" placeholder="请输入产品名称" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="产品规格" prop="specification">
              <el-input v-model="form.specification" placeholder="请输入产品规格" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="其他/箱号" prop="otherInfo">
              <el-input v-model="form.otherInfo" placeholder="请输入其他/箱号" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="单位" prop="unit">
              <el-select v-model="form.unit" placeholder="请选择单位" style="width: 100%">
                <el-option label="KG" value="KG" />
                <el-option label="只" value="只" />
                <el-option label="根" value="根" />
                <el-option label="个" value="个" />
                <el-option label="瓶" value="瓶" />
                <el-option label="张" value="张" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="供应商" prop="supplier">
              <el-select v-model="form.supplier" placeholder="请选择供应商" style="width: 100%">
                <el-option
                  v-for="item in supplierOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="标准进价" prop="standardPrice">
              <el-input-number v-model="form.standardPrice" :precision="2" :step="0.01" :min="0" style="width: 100%" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="增购基数" prop="incrementalBase">
              <el-input-number v-model="form.incrementalBase" :precision="2" :step="0.01" :min="0" style="width: 100%" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="最低采购量" prop="minimumPurchase">
              <el-input-number v-model="form.minimumPurchase" :precision="2" :step="0.01" :min="0" style="width: 100%" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="BomSubItem">
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import CommonSearchBar from '@/components/CommonSearchBar/index.vue'
import CommonTable from '@/components/CommonTable/index.vue'
import { 
  listBomSubItem, 
  getBomSubItem, 
  addBomSubItem, 
  updateBomSubItem, 
  delBomSubItem
} from '@/api/rd/bom-sub-item'

// 遮罩层
const loading = ref(true)
// 选中数组
const ids = ref([])
// 非单个禁用
const single = ref(true)
// 非多个禁用
const multiple = ref(true)
// 显示搜索条件
const showSearch = ref(true)
// 总条数
const total = ref(0)
// BOM子件表格数据
const subItemList = ref([])
// 弹出层标题
const title = ref("")
// 是否显示弹出层
const open = ref(false)

// 物料属性选项
const materialAttributeOptions = ref([
  { label: "原材料", value: "原材料" },
  { label: "自制零部件", value: "自制零部件" },
  { label: "导管", value: "导管" },
  { label: "组件", value: "组件" },
  { label: "外购外协件", value: "外购外协件" },
  { label: "包装材料", value: "包装材料" },
  { label: "产成品", value: "产成品" }
])

// 物料类别选项映射
const materialCategoryMap = ref({
  "原材料": [
    { label: "塑料类-单材料", value: "塑料类-单材料" },
    { label: "塑料类-混合料", value: "塑料类-混合料" },
    { label: "颜料类", value: "颜料类" },
    { label: "液态类", value: "液态类" },
    { label: "金属类", value: "金属类" },
    { label: "气态类", value: "气态类" }
  ],
  "自制零部件": [
    { label: "自制零部件", value: "自制零部件" }
  ],
  "导管": [
    { label: "导管", value: "导管" }
  ],
  "组件": [
    { label: "组件", value: "组件" }
  ],
  "外购外协件": [
    { label: "外购外协件", value: "外购外协件" }
  ],
  "包装材料": [
    { label: "包装材料", value: "包装材料" }
  ],
  "产成品": [
    { label: "产成品", value: "产成品" }
  ]
})

// 供应商选项
const supplierOptions = ref([
  { label: "供应商A", value: "SUPPLIER_A" },
  { label: "供应商B", value: "SUPPLIER_B" },
  { label: "供应商C", value: "SUPPLIER_C" },
  { label: "供应商D", value: "SUPPLIER_D" }
])

// 查询参数
const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  materialAttribute: undefined,
  materialCategory: undefined,
  subItemNumber: undefined,
  productName: undefined
})

// 表单参数
const form = reactive({
  id: undefined,
  materialAttribute: undefined,
  materialCategory: undefined,
  subItemNumber: undefined,
  productName: undefined,
  specification: undefined,
  otherInfo: undefined,
  unit: undefined,
  supplier: undefined,
  standardPrice: undefined,
  incrementalBase: undefined,
  minimumPurchase: undefined
})

// 表单校验规则
const rules = reactive({
  materialAttribute: [
    { required: true, message: "物料属性不能为空", trigger: "change" }
  ],
  materialCategory: [
    { required: true, message: "物料类别不能为空", trigger: "change" }
  ],
  subItemNumber: [
    { required: true, message: "子件编号不能为空", trigger: "blur" }
  ],
  productName: [
    { required: true, message: "产品名称不能为空", trigger: "blur" }
  ],
  unit: [
    { required: true, message: "单位不能为空", trigger: "change" }
  ],
  supplier: [
    { required: true, message: "供应商不能为空", trigger: "change" }
  ],
  standardPrice: [
    { required: true, message: "标准进价不能为空", trigger: "blur" }
  ],
  incrementalBase: [
    { required: true, message: "增购基数不能为空", trigger: "blur" }
  ],
  minimumPurchase: [
    { required: true, message: "最低采购量不能为空", trigger: "blur" }
  ]
})

// 表格列配置
const columns = ref([
  { type: 'selection', width: 55 },
  { prop: 'id', label: '序号', width: 80 },
  { prop: 'materialAttribute', label: '物料属性', width: 120 },
  { prop: 'materialCategory', label: '物料类别', width: 150 },
  { prop: 'subItemNumber', label: '子件编号', width: 150 },
  { prop: 'productName', label: '产品名称', minWidth: 200 },
  { prop: 'specification', label: '产品规格', width: 120 },
  { prop: 'otherInfo', label: '其他/箱号', width: 120 },
  { prop: 'unit', label: '单位', width: 80 },
  { prop: 'supplier', label: '供应商', width: 120 },
  { prop: 'standardPrice', label: '标准进价', width: 100 },
  { prop: 'incrementalBase', label: '增购基数', width: 100 },
  { prop: 'minimumPurchase', label: '最低采购量', width: 100 },
  { prop: 'createTime', label: '创建时间', width: 180 },
  { prop: 'operate', label: '操作', width: 150, slot: true }
])

// 根据物料属性过滤物料类别选项
const filteredMaterialCategoryOptions = computed(() => {
  if (!queryParams.materialAttribute && !form.materialAttribute) {
    return []
  }
  const attribute = queryParams.materialAttribute || form.materialAttribute
  return materialCategoryMap.value[attribute] || []
})

/** 查询BOM子件列表 */
function getList() {
  loading.value = true
  listBomSubItem(queryParams).then(response => {
    subItemList.value = response.rows
    total.value = response.total
    loading.value = false
  })
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.pageNum = 1
  getList()
}

/** 重置按钮操作 */
function resetQuery() {
  queryParams.materialAttribute = undefined
  queryParams.materialCategory = undefined
  queryParams.subItemNumber = undefined
  queryParams.productName = undefined
  handleQuery()
}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.id)
  single.value = selection.length !== 1
  multiple.value = !selection.length
}

/** 新增按钮操作 */
function handleAdd() {
  reset()
  open.value = true
  title.value = "添加BOM子件"
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset()
  const id = row.id || ids.value[0]
  getBomSubItem(id).then(response => {
    Object.assign(form, response.data)
    open.value = true
    title.value = "修改BOM子件"
  })
}

/** 物料属性变化处理 */
function handleMaterialAttributeChange() {
  form.materialCategory = undefined
}

/** 提交按钮 */
function submitForm() {
  subItemFormRef.value.validate((valid) => {
    if (valid) {
      if (form.id != null) {
        updateBomSubItem(form).then(response => {
          ElMessage.success("修改成功")
          open.value = false
          getList()
        })
      } else {
        addBomSubItem(form).then(response => {
          ElMessage.success("新增成功")
          open.value = false
          getList()
        })
      }
    }
  })
}

/** 删除按钮操作 */
function handleDelete(row) {
  const dataIds = row.id || ids.value
  ElMessageBox.confirm('是否确认删除BOM子件编号为"' + dataIds + '"的数据项?', "警告", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning"
  }).then(function() {
    return delBomSubItem(dataIds)
  }).then(() => {
    getList()
    ElMessage.success("删除成功")
  })
}

/** 取消按钮 */
function cancel() {
  open.value = false
  reset()
}

/** 表单重置 */
function reset() {
  form.id = undefined
  form.materialAttribute = undefined
  form.materialCategory = undefined
  form.subItemNumber = undefined
  form.productName = undefined
  form.specification = undefined
  form.otherInfo = undefined
  form.unit = undefined
  form.supplier = undefined
  form.standardPrice = undefined
  form.incrementalBase = undefined
  form.minimumPurchase = undefined
}

onMounted(() => {
  getList()
})
</script>

<style scoped lang="scss">
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-title {
  font-size: 18px;
  font-weight: bold;
  color: #303133;
}
</style> 