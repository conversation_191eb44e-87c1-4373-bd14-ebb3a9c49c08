<template>
  <div class="app-container">
    <el-card>
      <template #header>
        <div class="card-header">
          <span class="card-title">客户投诉</span>
          <el-button type="primary" @click="handleAdd">新增投诉</el-button>
        </div>
      </template>

      <!-- 查询条件 -->
      <CommonSearchBar
        v-model="queryParams"
        v-show="showSearch"
        @search="handleQuery"
        @reset="resetQuery"
      >
        <el-form-item label="客户名称" prop="customerName">
          <el-select
            v-model="queryParams.customerName"
            placeholder="请选择客户名称"
            filterable
            clearable
            style="width: 200px"
            @change="handleCustomerNameChange"
          >
            <el-option
              v-for="item in customerList"
              :key="item.customerName"
              :label="item.customerName"
              :value="item.customerName"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="客户代码" prop="customerCode">
          <el-select
            v-model="queryParams.customerCode"
            placeholder="请选择客户代码"
            filterable
            clearable
            style="width: 200px"
            @change="handleCustomerCodeChange"
          >
            <el-option
              v-for="item in customerList"
              :key="item.customerCode"
              :label="item.customerCode"
              :value="item.customerCode"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="订单号" prop="orderNo">
          <el-input
            v-model="queryParams.orderNo"
            placeholder="请输入订单号"
            clearable
            style="width: 200px"
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item label="投诉批号" prop="complaintBatchNo">
          <el-input
            v-model="queryParams.complaintBatchNo"
            placeholder="请输入投诉批号"
            clearable
            style="width: 200px"
            @keyup.enter="handleQuery"
          />
        </el-form-item>
      </CommonSearchBar>

      <!-- 操作按钮 -->
      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button
            type="primary"
            plain
            icon="Plus"
            @click="handleAdd"
            v-hasPermi="['marketing:complaint:add']"
          >新增</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="success"
            plain
            icon="Edit"
            :disabled="single"
            @click="handleUpdate"
            v-hasPermi="['marketing:complaint:edit']"
          >修改</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="danger"
            plain
            icon="Delete"
            :disabled="multiple"
            @click="handleDelete"
            v-hasPermi="['marketing:complaint:remove']"
          >删除</el-button>
        </el-col>
        <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
      </el-row>

      <!-- 数据表格 -->
      <CommonTable
        :columns="columns"
        :data="complaintList"
        :loading="loading"
        :total="total"
        :page-num="queryParams.pageNum"
        :page-size="queryParams.pageSize"
        @selection-change="handleSelectionChange"
        @update:page-num="val => queryParams.pageNum = val"
        @update:page-size="val => queryParams.pageSize = val"
        @pagination="getList"
      >
        <template #operate="{ row }">
          <el-button
            size="small"
            type="text"
            icon="Edit"
            @click="handleUpdate(row)"
            v-hasPermi="['marketing:complaint:edit']"
          >修改</el-button>
          <el-button
            size="small"
            type="text"
            icon="ChatDotRound"
            @click="handleFeedback(row)"
            v-hasPermi="['marketing:complaint:feedback']"
          >投诉反馈</el-button>
          <el-button
            size="small"
            type="text"
            icon="Delete"
            @click="handleDelete(row)"
            v-hasPermi="['marketing:complaint:remove']"
          >删除</el-button>
        </template>
      </CommonTable>
    </el-card>

    <!-- 添加或修改投诉对话框 -->
    <el-dialog :title="title" v-model="open" width="800px" append-to-body>
      <el-form ref="complaintFormRef" :model="form" :rules="rules" label-width="120px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="客户代码" prop="customerCode">
              <el-select
                v-model="form.customerCode"
                placeholder="请选择客户代码"
                filterable
                style="width: 100%"
                @change="handleFormCustomerCodeChange"
              >
                <el-option
                  v-for="item in customerList"
                  :key="item.customerCode"
                  :label="item.customerCode"
                  :value="item.customerCode"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="客户名称" prop="customerName">
              <el-select
                v-model="form.customerName"
                placeholder="请选择客户名称"
                filterable
                style="width: 100%"
                @change="handleFormCustomerNameChange"
              >
                <el-option
                  v-for="item in customerList"
                  :key="item.customerName"
                  :label="item.customerName"
                  :value="item.customerName"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="投诉日期" prop="complaintDate">
              <el-date-picker
                v-model="form.complaintDate"
                type="date"
                placeholder="选择投诉日期"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="订单日期" prop="orderDate">
              <el-date-picker
                v-model="form.orderDate"
                type="date"
                placeholder="选择订单日期"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="订单号" prop="orderNo">
              <el-input v-model="form.orderNo" placeholder="请输入订单号" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="投诉批号" prop="complaintBatchNo">
              <el-input v-model="form.complaintBatchNo" placeholder="请输入投诉批号" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="投诉描述" prop="complaintDescription">
              <el-input v-model="form.complaintDescription" type="textarea" :rows="4" placeholder="请输入投诉描述" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="投诉反馈" prop="complaintFeedback">
              <el-input v-model="form.complaintFeedback" type="textarea" :rows="4" placeholder="请输入投诉反馈" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 投诉反馈对话框 -->
    <el-dialog title="投诉反馈" v-model="feedbackOpen" width="600px" append-to-body>
      <el-form ref="feedbackFormRef" :model="feedbackForm" :rules="feedbackRules" label-width="100px">
        <el-form-item label="投诉批号">
          <el-input v-model="feedbackForm.complaintBatchNo" readonly />
        </el-form-item>
        <el-form-item label="客户名称">
          <el-input v-model="feedbackForm.customerName" readonly />
        </el-form-item>
        <el-form-item label="投诉描述">
          <el-input v-model="feedbackForm.complaintDescription" type="textarea" :rows="3" readonly />
        </el-form-item>
        <el-form-item label="投诉反馈" prop="complaintFeedback">
          <el-input v-model="feedbackForm.complaintFeedback" type="textarea" :rows="4" placeholder="请输入投诉反馈内容" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitFeedback">确 定</el-button>
          <el-button @click="feedbackOpen = false">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Complaint">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import CommonSearchBar from '@/components/CommonSearchBar/index.vue'
import CommonTable from '@/components/CommonTable/index.vue'
import { listComplaint, addComplaint, updateComplaint, delComplaint, getComplaint, submitComplaintFeedback } from '@/api/marketing/complaint'
import { listCustomer } from '@/api/marketing/customer'

// 遮罩层
const loading = ref(true)
// 选中数组
const ids = ref([])
// 非单个禁用
const single = ref(true)
// 非多个禁用
const multiple = ref(true)
// 显示搜索条件
const showSearch = ref(true)
// 总条数
const total = ref(0)
// 投诉表格数据
const complaintList = ref([])
// 弹出层标题
const title = ref("")
// 是否显示弹出层
const open = ref(false)
// 是否显示反馈对话框
const feedbackOpen = ref(false)
// 客户列表
const customerList = ref([])

// 表格列配置
const columns = ref([
  { label: '客户代码', prop: 'customerCode', align: 'center', width: '120' },
  { label: '客户名称', prop: 'customerName', align: 'center' },
  { label: '投诉日期', prop: 'complaintDate', align: 'center', width: '120' },
  { label: '订单日期', prop: 'orderDate', align: 'center', width: '120' },
  { label: '订单号', prop: 'orderNo', align: 'center', width: '150' },
  { label: '投诉批号', prop: 'complaintBatchNo', align: 'center', width: '150' },
  { label: '投诉描述', prop: 'complaintDescription', align: 'center', showOverflowTooltip: true },
  { label: '投诉反馈', prop: 'complaintFeedback', align: 'center', showOverflowTooltip: true }
])

// 查询参数
const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  customerName: null,
  customerCode: null,
  orderNo: null,
  complaintBatchNo: null
})

// 表单参数
const form = reactive({
  id: null,
  customerCode: null,
  customerName: null,
  complaintDate: null,
  orderDate: null,
  orderNo: null,
  complaintBatchNo: null,
  complaintDescription: null,
  complaintFeedback: null
})

// 反馈表单参数
const feedbackForm = reactive({
  id: null,
  complaintBatchNo: null,
  customerName: null,
  complaintDescription: null,
  complaintFeedback: null
})

// 表单校验规则
const rules = reactive({
  customerCode: [
    { required: true, message: "客户代码不能为空", trigger: "blur" }
  ],
  customerName: [
    { required: true, message: "客户名称不能为空", trigger: "blur" }
  ],
  complaintDate: [
    { required: true, message: "投诉日期不能为空", trigger: "change" }
  ],
  orderNo: [
    { required: true, message: "订单号不能为空", trigger: "blur" }
  ],
  complaintBatchNo: [
    { required: true, message: "投诉批号不能为空", trigger: "blur" }
  ],
  complaintDescription: [
    { required: true, message: "投诉描述不能为空", trigger: "blur" }
  ]
})

// 反馈表单校验规则
const feedbackRules = reactive({
  complaintFeedback: [
    { required: true, message: "投诉反馈不能为空", trigger: "blur" }
  ]
})

const complaintFormRef = ref()
const feedbackFormRef = ref()

/** 查询投诉列表 */
function getList() {
  loading.value = true
  listComplaint(queryParams).then(response => {
    complaintList.value = response.rows
    total.value = response.total
    loading.value = false
  }).catch(() => {
    loading.value = false
  })
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.pageNum = 1
  getList()
}

/** 重置按钮操作 */
function resetQuery() {
  queryParams.customerName = null
  queryParams.customerCode = null
  queryParams.orderNo = null
  queryParams.complaintBatchNo = null
  handleQuery()
}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.id)
  single.value = selection.length !== 1
  multiple.value = !selection.length
}

/** 新增按钮操作 */
function handleAdd() {
  reset()
  open.value = true
  title.value = "添加投诉"
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset()
  const id = row.id || ids.value[0]
  getComplaint(id).then(response => {
    Object.assign(form, response.data)
    open.value = true
    title.value = "修改投诉"
  })
}

/** 投诉反馈按钮操作 */
function handleFeedback(row) {
  feedbackForm.id = row.id
  feedbackForm.complaintBatchNo = row.complaintBatchNo
  feedbackForm.customerName = row.customerName
  feedbackForm.complaintDescription = row.complaintDescription
  feedbackForm.complaintFeedback = row.complaintFeedback || ''
  feedbackOpen.value = true
}

/** 提交按钮 */
function submitForm() {
  complaintFormRef.value.validate((valid) => {
    if (valid) {
      if (form.id != null) {
        updateComplaint(form).then(response => {
          ElMessage.success("修改成功")
          open.value = false
          getList()
        })
      } else {
        addComplaint(form).then(response => {
          ElMessage.success("新增成功")
          open.value = false
          getList()
        })
      }
    }
  })
}

/** 提交反馈 */
function submitFeedback() {
  feedbackFormRef.value.validate((valid) => {
    if (valid) {
      submitComplaintFeedback(feedbackForm.id, feedbackForm.complaintFeedback).then(response => {
        ElMessage.success("反馈提交成功")
        feedbackOpen.value = false
        getList()
      })
    }
  })
}

/** 删除按钮操作 */
function handleDelete(row) {
  const complaintIds = row.id || ids.value
  ElMessageBox.confirm('是否确认删除投诉编号为"' + complaintIds + '"的数据项?', "警告", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning"
  }).then(function() {
    return delComplaint(complaintIds)
  }).then(() => {
    ElMessage.success("删除成功")
    getList()
  })
}

/** 取消按钮 */
function cancel() {
  open.value = false
  reset()
}

/** 表单重置 */
function reset() {
  form.id = null
  form.customerCode = null
  form.customerName = null
  form.complaintDate = null
  form.orderDate = null
  form.orderNo = null
  form.complaintBatchNo = null
  form.complaintDescription = null
  form.complaintFeedback = null
}

/** 获取客户列表 */
function getCustomerList() {
  listCustomer({ pageSize: 1000 }).then(res => {
    customerList.value = res.rows || []
  })
}

function handleCustomerNameChange(val) {
  const customer = customerList.value.find(item => item.customerName === val)
  if (customer) {
    queryParams.customerCode = customer.customerCode
  } else {
    queryParams.customerCode = null
  }
}

function handleCustomerCodeChange(val) {
  const customer = customerList.value.find(item => item.customerCode === val)
  if (customer) {
    queryParams.customerName = customer.customerName
  } else {
    queryParams.customerName = null
  }
}

function handleFormCustomerCodeChange(val) {
  const customer = customerList.value.find(item => item.customerCode === val)
  if (customer) {
    form.customerName = customer.customerName
  } else {
    form.customerName = null
  }
}

function handleFormCustomerNameChange(val) {
  const customer = customerList.value.find(item => item.customerName === val)
  if (customer) {
    form.customerCode = customer.customerCode
  } else {
    form.customerCode = null
  }
}

onMounted(() => {
  getList()
  getCustomerList()
})
</script>

<style scoped lang="scss">
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-title {
  font-size: 18px;
  font-weight: bold;
  color: #303133;
}

.mb8 {
  margin-bottom: 8px;
}

.dialog-footer {
  text-align: right;
}
</style> 