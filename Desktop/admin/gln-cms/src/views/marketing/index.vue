<template>
  <div class="app-container">
    <el-row :gutter="20" style="margin-top: 0;">
      <el-col :span="6">
        <el-card class="feature-card" shadow="hover">
          <template #header>
            <div class="feature-header">
              <el-icon><UserFilled /></el-icon>
              <span>客户信息</span>
            </div>
          </template>
          <div class="feature-content">
            <p>客户档案管理、客户状态管理、客户信息维护等工作</p>
            <el-button type="primary" size="small" @click="goToModule('customer')">进入模块</el-button>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="feature-card" shadow="hover">
          <template #header>
            <div class="feature-header">
              <el-icon><Document /></el-icon>
              <span>订单管理</span>
            </div>
          </template>
          <div class="feature-content">
            <p>订单处理、订单跟踪、订单状态管理等订单相关工作</p>
            <el-button type="primary" size="small" @click="goToModule('order')">进入模块</el-button>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="feature-card" shadow="hover">
          <template #header>
            <div class="feature-header">
              <el-icon><Money /></el-icon>
              <span>客户财务</span>
            </div>
          </template>
          <div class="feature-content">
            <p>客户财务信息、付款记录、发票管理等财务相关工作</p>
            <el-button type="primary" size="small" @click="goToModule('finance')">进入模块</el-button>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="feature-card" shadow="hover">
          <template #header>
            <div class="feature-header">
              <el-icon><Warning /></el-icon>
              <span>客户投诉</span>
            </div>
          </template>
          <div class="feature-content">
            <p>客户投诉处理、投诉反馈、投诉跟踪等投诉管理工作</p>
            <el-button type="primary" size="small" @click="goToModule('complaint')">进入模块</el-button>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup name="Marketing">
import { UserFilled, Document, Money, Warning } from '@element-plus/icons-vue'
import { useRouter } from 'vue-router'

const router = useRouter()

const goToModule = (module) => {
  switch(module) {
    case 'customer':
      router.push('/marketing/customer')
      break
    case 'order':
      router.push('/marketing/order')
      break
    case 'finance':
      router.push('/marketing/finance')
      break
    case 'complaint':
      router.push('/marketing/complaint')
      break
    default:
      break
  }
}
</script>

<style scoped lang="scss">
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-title {
  font-size: 18px;
  font-weight: bold;
  color: #303133;
}

.department-intro {
  margin-bottom: 20px;
  
  h3 {
    color: #409EFF;
    margin-bottom: 10px;
  }
  
  p {
    color: #606266;
    line-height: 1.6;
  }
}

.feature-card {
  margin-bottom: 20px;
  transition: all 0.3s ease;
  
  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
  }
  
  .feature-header {
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: bold;
    color: #409EFF;
  }
  
  .feature-content {
    text-align: center;
    
    p {
      color: #606266;
      margin-bottom: 15px;
      line-height: 1.5;
      font-size: 14px;
    }
  }
}
</style> 