<template>
  <div class="app-container">
    <el-card>
      <template #header>
        <div class="card-header">
          <span class="card-title">客户财务</span>
        </div>
      </template>

      <!-- 查询条件 -->
      <CommonSearchBar
        v-model="queryParams"
        v-show="showSearch"
        @search="handleQuery"
        @reset="resetQuery"
      >
        <el-form-item label="时间查询" prop="timeRange">
          <el-date-picker
            v-model="queryParams.timeRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            style="width: 240px"
          />
        </el-form-item>
        <el-form-item label="客户名称" prop="customerName">
          <el-select
            v-model="queryParams.customerName"
            placeholder="请选择客户名称"
            filterable
            clearable
            style="width: 200px"
            @change="handleCustomerNameChange"
          >
            <el-option
              v-for="item in customerList"
              :key="item.customerName"
              :label="item.customerName"
              :value="item.customerName"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="客户代码" prop="customerCode">
          <el-select
            v-model="queryParams.customerCode"
            placeholder="请选择客户代码"
            filterable
            clearable
            style="width: 200px"
            @change="handleCustomerCodeChange"
          >
            <el-option
              v-for="item in customerList"
              :key="item.customerCode"
              :label="item.customerCode"
              :value="item.customerCode"
            />
          </el-select>
        </el-form-item>
      </CommonSearchBar>

      <!-- 操作按钮 -->
      <el-row :gutter="10" class="mb8">
        <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
      </el-row>

      <!-- 数据表格 -->
      <CommonTable
        :columns="columns"
        :data="financeList"
        :loading="loading"
        :total="total"
        :page-num="queryParams.pageNum"
        :page-size="queryParams.pageSize"
        @selection-change="handleSelectionChange"
        @update:page-num="val => queryParams.pageNum = val"
        @update:page-size="val => queryParams.pageSize = val"
        @pagination="getList"
      >
        <template #totalOrderAmount="{ row }">
          ¥{{ row.totalOrderAmount }}
        </template>
        <template #invoicedAmount="{ row }">
          ¥{{ row.invoicedAmount }}
        </template>
        <template #operate="{ row }">
          <el-button
            size="small"
            type="text"
            icon="View"
            @click="handleViewDetail(row)"
            v-hasPermi="['marketing:finance:view']"
          >查看详情</el-button>
        </template>
      </CommonTable>
    </el-card>

    <!-- 客户财务详情对话框 -->
    <el-dialog :title="detailTitle" v-model="detailOpen" width="1200px" append-to-body>
      <div class="detail-container">
        <!-- 客户基本信息 -->
        <el-card class="detail-card">
          <template #header>
            <div class="detail-header">
              <span>客户基本信息</span>
            </div>
          </template>
          <el-descriptions :column="3" border>
            <el-descriptions-item label="客户名称">{{ detailData.customerName }}</el-descriptions-item>
            <el-descriptions-item label="客户代码">{{ detailData.customerCode }}</el-descriptions-item>
            <el-descriptions-item label="订单总数量">{{ detailData.totalOrderCount }}</el-descriptions-item>
            <el-descriptions-item label="客户总订单金额">¥{{ detailData.totalOrderAmount }}</el-descriptions-item>
            <el-descriptions-item label="已开发票金额">¥{{ detailData.invoicedAmount }}</el-descriptions-item>
            <el-descriptions-item label="未开票金额">¥{{ detailData.uninvoicedAmount }}</el-descriptions-item>
          </el-descriptions>
        </el-card>

        <!-- 付款记录 -->
        <el-card class="detail-card">
          <template #header>
            <div class="detail-header">
              <span>付款记录</span>
            </div>
          </template>
          <el-table :data="detailData.paymentRecords" border>
            <el-table-column label="付款日期" align="center" prop="paymentDate" width="120" />
            <el-table-column label="付款金额" align="center" prop="paymentAmount" width="120">
              <template #default="scope">
                ¥{{ scope.row.paymentAmount }}
              </template>
            </el-table-column>
            <el-table-column label="付款方式" align="center" prop="paymentMethod" width="120" />
            <el-table-column label="付款备注" align="center" prop="paymentRemark" />
            <el-table-column label="操作人" align="center" prop="operator" width="100" />
          </el-table>
        </el-card>

        <!-- 发票记录 -->
        <el-card class="detail-card">
          <template #header>
            <div class="detail-header">
              <span>发票记录</span>
            </div>
          </template>
          <el-table :data="detailData.invoiceRecords" border>
            <el-table-column label="开票日期" align="center" prop="invoiceDate" width="120" />
            <el-table-column label="发票号" align="center" prop="invoiceNumber" width="150" />
            <el-table-column label="发票金额" align="center" prop="invoiceAmount" width="120">
              <template #default="scope">
                ¥{{ scope.row.invoiceAmount }}
              </template>
            </el-table-column>
            <el-table-column label="发票类型" align="center" prop="invoiceType" width="120" />
            <el-table-column label="开票备注" align="center" prop="invoiceRemark" />
          </el-table>
        </el-card>

        <!-- 订单记录 -->
        <el-card class="detail-card">
          <template #header>
            <div class="detail-header">
              <span>订单记录</span>
            </div>
          </template>
          <el-table :data="detailData.orderRecords" border>
            <el-table-column label="订单日期" align="center" prop="orderDate" width="120" />
            <el-table-column label="订单号" align="center" prop="orderNo" width="150" />
            <el-table-column label="订单金额" align="center" prop="orderAmount" width="120">
              <template #default="scope">
                ¥{{ scope.row.orderAmount }}
              </template>
            </el-table-column>
            <el-table-column label="订单状态" align="center" prop="orderStatus" width="100">
              <template #default="scope">
                <el-tag :type="getOrderStatusType(scope.row.orderStatus)">
                  {{ scope.row.orderStatus }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column label="产品名称" align="center" prop="productName" />
            <el-table-column label="数量" align="center" prop="quantity" width="80" />
            <el-table-column label="交货日期" align="center" prop="deliveryDate" width="120" />
          </el-table>
        </el-card>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="detailOpen = false">关 闭</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Finance">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import CommonSearchBar from '@/components/CommonSearchBar/index.vue'
import CommonTable from '@/components/CommonTable/index.vue'
import { listFinance, getFinance, getPaymentRecords, getInvoiceRecords, getOrderRecords } from '@/api/marketing/finance'
import { listCustomer } from '@/api/marketing/customer'

// 遮罩层
const loading = ref(true)
// 选中数组
const ids = ref([])
// 非单个禁用
const single = ref(true)
// 非多个禁用
const multiple = ref(true)
// 显示搜索条件
const showSearch = ref(true)
// 总条数
const total = ref(0)
// 客户财务表格数据
const financeList = ref([])
// 客户列表
const customerList = ref([])
// 详情对话框标题
const detailTitle = ref("")
// 是否显示详情对话框
const detailOpen = ref(false)
// 详情数据
const detailData = ref({})

// 表格列配置
const columns = ref([
  { label: '客户名称', prop: 'customerName', align: 'center' },
  { label: '客户代码', prop: 'customerCode', align: 'center' },
  { label: '订单总数量', prop: 'totalOrderCount', align: 'center', width: '120' },
  { label: '客户总订单金额', prop: 'totalOrderAmount', align: 'center', width: '150', slot: 'totalOrderAmount' },
  { label: '客户付款信息', prop: 'paymentInfo', align: 'center', showOverflowTooltip: true },
  { label: '客户欠款信息', prop: 'debtInfo', align: 'center', showOverflowTooltip: true },
  { label: '客户开票时间信息', prop: 'invoiceTimeInfo', align: 'center', showOverflowTooltip: true },
  { label: '已开发票金额', prop: 'invoicedAmount', align: 'center', width: '150', slot: 'invoicedAmount' },
  { label: '发票号', prop: 'invoiceNumbers', align: 'center', showOverflowTooltip: true }
])

// 查询参数
const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  timeType: 'month',
  timeRange: null,
  customerName: null,
  customerCode: null
})

/** 获取日期选择器类型 */
function getDatePickerType() {
  switch (queryParams.timeType) {
    case 'month':
      return 'monthrange'
    case 'quarter':
      return 'monthrange'
    case 'year':
      return 'yearrange'
    default:
      return 'daterange'
  }
}

/** 获取订单状态类型 */
function getOrderStatusType(status) {
  const statusMap = {
    '待生产': 'info',
    '生产中': 'warning',
    '待检验': 'warning',
    '待发货': 'primary',
    '已发货': 'success',
    '已签收': 'success'
  }
  return statusMap[status] || 'info'
}

/** 查询客户财务列表 */
function getList(params = queryParams) {
  loading.value = true
  listFinance(params).then(response => {
    financeList.value = response.rows
    total.value = response.total
    loading.value = false
  }).catch(() => {
    loading.value = false
  })
}

/** 搜索按钮操作 */
function handleQuery() {
  // 构造新的查询参数，timeRange转字符串
  const params = { ...queryParams };
  if (Array.isArray(params.timeRange) && params.timeRange.length === 2) {
    params.timeRange = params.timeRange.join(',');
  } else {
    params.timeRange = null;
  }
  params.pageNum = 1;
  getList(params);
}

/** 重置按钮操作 */
function resetQuery() {
  queryParams.timeType = 'month'
  queryParams.timeRange = null
  queryParams.customerName = null
  queryParams.customerCode = null
  handleQuery()
}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.id)
  single.value = selection.length !== 1
  multiple.value = !selection.length
}

/** 获取客户列表 */
function getCustomerList() {
  listCustomer({ pageSize: 1000 }).then(res => {
    customerList.value = res.rows || []
  })
}

/** 客户名称变化时触发 */
function handleCustomerNameChange(val) {
  const customer = customerList.value.find(item => item.customerName === val)
  if (customer) {
    queryParams.customerCode = customer.customerCode
  } else {
    queryParams.customerCode = null
  }
}

/** 客户代码变化时触发 */
function handleCustomerCodeChange(val) {
  const customer = customerList.value.find(item => item.customerCode === val)
  if (customer) {
    queryParams.customerName = customer.customerName
  } else {
    queryParams.customerName = null
  }
}

/** 查看详情按钮操作 */
function handleViewDetail(row) {
  const customerId = row.id
  Promise.all([
    getFinance(customerId),
    getPaymentRecords(customerId),
    getInvoiceRecords(customerId),
    getOrderRecords(customerId)
  ]).then(([financeRes, paymentRes, invoiceRes, orderRes]) => {
    detailData.value = {
      customerName: row.customerName,
      customerCode: row.customerCode,
      totalOrderCount: row.totalOrderCount,
      totalOrderAmount: row.totalOrderAmount,
      invoicedAmount: row.invoicedAmount,
      uninvoicedAmount: (row.totalOrderAmount - row.invoicedAmount).toFixed(2),
      paymentRecords: paymentRes.rows || [],
      invoiceRecords: invoiceRes.rows || [],
      orderRecords: orderRes.rows || []
    }
    detailTitle.value = `${row.customerName} - 财务详情`
    detailOpen.value = true
  }).catch(() => {
    ElMessage.error('获取详情失败')
  })
}

onMounted(() => {
  getList()
  getCustomerList()
})
</script>

<style scoped lang="scss">
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-title {
  font-size: 18px;
  font-weight: bold;
  color: #303133;
}

.mb8 {
  margin-bottom: 8px;
}

.dialog-footer {
  text-align: right;
}

.detail-container {
  .detail-card {
    margin-bottom: 20px;
    
    .detail-header {
      font-weight: bold;
      color: #409EFF;
    }
  }
}
</style> 