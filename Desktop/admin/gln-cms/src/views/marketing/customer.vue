<template>
  <div class="app-container">
    <el-card>
      <template #header>
        <div class="card-header">
          <span class="card-title">客户信息管理</span>
          <el-button type="primary" @click="handleAdd">新增客户</el-button>
        </div>
      </template>

      <!-- 查询条件 -->
      <CommonSearchBar
        v-model="queryParams"
        v-show="showSearch"
        @search="handleQuery"
        @reset="resetQuery"
      >
        <el-form-item label="客户名称" prop="customerName">
          <el-input
            v-model="queryParams.customerName"
            placeholder="请输入客户名称"
            clearable
            style="width: 200px"
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item label="客户代码" prop="customerCode">
          <el-input
            v-model="queryParams.customerCode"
            placeholder="请输入客户代码"
            clearable
            style="width: 200px"
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item label="联系人" prop="contactPerson">
          <el-input
            v-model="queryParams.contactPerson"
            placeholder="请输入联系人"
            clearable
            style="width: 200px"
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item label="客户状态" prop="status">
          <el-select v-model="queryParams.status" placeholder="请选择客户状态" clearable style="width: 200px">
            <el-option label="正常" value="正常" />
            <el-option label="冻结" value="冻结" />
          </el-select>
        </el-form-item>
      </CommonSearchBar>

      <!-- 操作按钮 -->
      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button
            type="primary"
            plain
            icon="Plus"
            @click="handleAdd"
            v-hasPermi="['marketing:customer:add']"
          >新增</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="success"
            plain
            icon="Edit"
            :disabled="single"
            @click="handleUpdate"
            v-hasPermi="['marketing:customer:edit']"
          >修改</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="danger"
            plain
            icon="Delete"
            :disabled="multiple"
            @click="handleDelete"
            v-hasPermi="['marketing:customer:remove']"
          >删除</el-button>
        </el-col>
        <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
      </el-row>

      <!-- 数据表格 -->
      <CommonTable
        :columns="columns"
        :data="customerList"
        :loading="loading"
        :total="total"
        :page-num="queryParams.pageNum"
        :page-size="queryParams.pageSize"
        @selection-change="handleSelectionChange"
        @update:page-num="val => queryParams.pageNum = val"
        @update:page-size="val => queryParams.pageSize = val"
        @pagination="getList"
      >
        <template #status="{ row }">
          <el-tag :type="row.status === '正常' ? 'success' : 'danger'">
            {{ row.status }}
          </el-tag>
        </template>
        <template #operate="{ row }">
          <el-button
            size="small"
            type="text"
            icon="Edit"
            @click="handleUpdate(row)"
            v-hasPermi="['marketing:customer:edit']"
          >修改</el-button>
          <el-button
            size="small"
            type="text"
            :type="row.status === '正常' ? 'danger' : 'success'"
            :icon="row.status === '正常' ? 'Lock' : 'Unlock'"
            @click="handleToggleStatus(row)"
            v-hasPermi="['marketing:customer:edit']"
          >{{ row.status === '正常' ? '冻结' : '解除' }}</el-button>
        </template>
      </CommonTable>
    </el-card>

    <!-- 添加或修改客户信息对话框 -->
    <el-dialog :title="title" v-model="open" width="800px" append-to-body>
      <el-form ref="customerFormRef" :model="form" :rules="rules" label-width="120px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="客户代码" prop="customerCode">
              <el-input v-model="form.customerCode" placeholder="请输入客户代码" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="客户名称" prop="customerName">
              <el-input v-model="form.customerName" placeholder="请输入客户名称" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="客户电话" prop="customerPhone">
              <el-input v-model="form.customerPhone" placeholder="请输入客户电话" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="客户联系人" prop="contactPerson">
              <el-input v-model="form.contactPerson" placeholder="请输入客户联系人" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="客户地址" prop="customerAddress">
              <el-input v-model="form.customerAddress" type="textarea" placeholder="请输入客户地址" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="客户信用代码" prop="creditCode">
              <el-input v-model="form.creditCode" placeholder="请输入客户信用代码" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="客户状态" prop="status">
              <el-select v-model="form.status" placeholder="请选择客户状态">
                <el-option label="正常" value="正常" />
                <el-option label="冻结" value="冻结" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="客户开票信息" prop="invoiceInfo">
              <el-input v-model="form.invoiceInfo" type="textarea" placeholder="请输入客户开票信息" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Customer">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import CommonSearchBar from '@/components/CommonSearchBar/index.vue'
import CommonTable from '@/components/CommonTable/index.vue'
import { listCustomer, addCustomer, updateCustomer, delCustomer, getCustomer, toggleCustomerStatus } from '@/api/marketing/customer'

// 遮罩层
const loading = ref(true)
// 选中数组
const ids = ref([])
// 非单个禁用
const single = ref(true)
// 非多个禁用
const multiple = ref(true)
// 显示搜索条件
const showSearch = ref(true)
// 总条数
const total = ref(0)
// 客户信息表格数据
const customerList = ref([])
// 弹出层标题
const title = ref("")
// 是否显示弹出层
const open = ref(false)

// 表格列配置
const columns = ref([
  { label: '客户代码', prop: 'customerCode', align: 'center' },
  { label: '客户名称', prop: 'customerName', align: 'center' },
  { label: '客户电话', prop: 'customerPhone', align: 'center' },
  { label: '客户联系人', prop: 'contactPerson', align: 'center' },
  { label: '客户地址', prop: 'customerAddress', align: 'center', showOverflowTooltip: true },
  { label: '客户信用代码', prop: 'creditCode', align: 'center' },
  { label: '客户开票信息', prop: 'invoiceInfo', align: 'center', showOverflowTooltip: true },
  { label: '客户状态', prop: 'status', align: 'center', slot: 'status' }
])

// 查询参数
const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  customerName: null,
  customerCode: null,
  contactPerson: null,
  status: null
})

// 表单参数
const form = reactive({
  id: null,
  customerCode: null,
  customerName: null,
  customerPhone: null,
  contactPerson: null,
  customerAddress: null,
  creditCode: null,
  invoiceInfo: null,
  status: '正常'
})

// 表单校验规则
const rules = reactive({
  customerCode: [
    { required: true, message: "客户代码不能为空", trigger: "blur" }
  ],
  customerName: [
    { required: true, message: "客户名称不能为空", trigger: "blur" }
  ],
  customerPhone: [
    { required: true, message: "客户电话不能为空", trigger: "blur" }
  ],
  contactPerson: [
    { required: true, message: "客户联系人不能为空", trigger: "blur" }
  ]
})

const customerFormRef = ref()

/** 查询客户信息列表 */
function getList() {
  loading.value = true
  listCustomer(queryParams).then(response => {
    customerList.value = response.rows
    total.value = response.total
    loading.value = false
  }).catch(() => {
    loading.value = false
  })
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.pageNum = 1
  getList()
}

/** 重置按钮操作 */
function resetQuery() {
  queryParams.customerName = null
  queryParams.customerCode = null
  queryParams.contactPerson = null
  queryParams.status = null
  handleQuery()
}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.id)
  single.value = selection.length !== 1
  multiple.value = !selection.length
}

/** 新增按钮操作 */
function handleAdd() {
  reset()
  open.value = true
  title.value = "添加客户信息"
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset()
  const id = row.id || ids.value[0]
  getCustomer(id).then(response => {
    Object.assign(form, response.data)
    open.value = true
    title.value = "修改客户信息"
  })
}

/** 提交按钮 */
function submitForm() {
  customerFormRef.value.validate((valid) => {
    if (valid) {
      if (form.id != null) {
        updateCustomer(form).then(response => {
          ElMessage.success("修改成功")
          open.value = false
          getList()
        })
      } else {
        addCustomer(form).then(response => {
          ElMessage.success("新增成功")
          open.value = false
          getList()
        })
      }
    }
  })
}

/** 删除按钮操作 */
function handleDelete(row) {
  const customerIds = row.id || ids.value
  ElMessageBox.confirm('是否确认删除客户信息编号为"' + customerIds + '"的数据项?', "警告", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning"
  }).then(function() {
    return delCustomer(customerIds)
  }).then(() => {
    ElMessage.success("删除成功")
    getList()
  })
}

/** 冻结/解除操作 */
function handleToggleStatus(row) {
  const action = row.status === '正常' ? '冻结' : '解除'
  const newStatus = row.status === '正常' ? '冻结' : '正常'
  ElMessageBox.confirm(`是否确认${action}客户"${row.customerName}"?`, "警告", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning"
  }).then(function() {
    return toggleCustomerStatus(row.id, newStatus)
  }).then(() => {
    ElMessage.success(`${action}成功`)
    getList()
  })
}

/** 取消按钮 */
function cancel() {
  open.value = false
  reset()
}

/** 表单重置 */
function reset() {
  form.id = null
  form.customerCode = null
  form.customerName = null
  form.customerPhone = null
  form.contactPerson = null
  form.customerAddress = null
  form.creditCode = null
  form.invoiceInfo = null
  form.status = '正常'
}

onMounted(() => {
  getList()
})
</script>

<style scoped lang="scss">
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-title {
  font-size: 18px;
  font-weight: bold;
  color: #303133;
}

.mb8 {
  margin-bottom: 8px;
}

.dialog-footer {
  text-align: right;
}
</style> 