<template>
  <div class="app-container">
    <el-card>
      <template #header>
        <div class="card-header">
          <span class="card-title">订单管理</span>
          <el-button type="primary" @click="handleAdd">新增订单</el-button>
        </div>
      </template>

      <!-- 查询条件 -->
      <CommonSearchBar
        v-model="queryParams"
        v-show="showSearch"
        @search="handleQuery"
        @reset="resetQuery"
      >
        <el-form-item label="订单日期" prop="orderDateRange">
          <el-date-picker
            v-model="queryParams.orderDateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            style="width: 240px"
          />
        </el-form-item>
        <el-form-item label="订单号" prop="orderNo">
          <el-input
            v-model="queryParams.orderNo"
            placeholder="请输入订单号"
            clearable
            style="width: 200px"
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item label="客户代码" prop="customerCode">
          <el-select
            v-model="queryParams.customerCode"
            placeholder="请选择客户代码"
            clearable
            filterable
            style="width: 200px"
            @change="handleCustomerCodeChange"
          >
            <el-option
              v-for="item in customerList"
              :key="item.customerCode"
              :label="item.customerCode"
              :value="item.customerCode"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="客户名称" prop="customerName">
          <el-select
            v-model="queryParams.customerName"
            placeholder="请选择客户名称"
            clearable
            filterable
            style="width: 200px"
            @change="handleCustomerNameChange"
          >
            <el-option
              v-for="item in customerList"
              :key="item.customerName"
              :label="item.customerName"
              :value="item.customerName"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="产品名称" prop="productName">
          <el-input
            v-model="queryParams.productName"
            placeholder="请输入产品名称"
            clearable
            style="width: 200px"
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item label="产品型号" prop="productModel">
          <el-input
            v-model="queryParams.productModel"
            placeholder="请输入产品型号"
            clearable
            style="width: 200px"
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item label="交货日期" prop="deliveryDateRange">
          <el-date-picker
            v-model="queryParams.deliveryDateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            style="width: 240px"
          />
        </el-form-item>
        <el-form-item label="发货日期" prop="shipDateRange">
          <el-date-picker
            v-model="queryParams.shipDateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            style="width: 240px"
          />
        </el-form-item>
      </CommonSearchBar>

      <!-- 操作按钮 -->
      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button
            type="primary"
            plain
            icon="Plus"
            @click="handleAdd"
            v-hasPermi="['marketing:order:add']"
          >新增</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="success"
            plain
            icon="Edit"
            :disabled="single"
            @click="handleUpdate"
            v-hasPermi="['marketing:order:edit']"
          >修改</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="danger"
            plain
            icon="Delete"
            :disabled="multiple"
            @click="handleDelete"
            v-hasPermi="['marketing:order:remove']"
          >删除</el-button>
        </el-col>
        <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
      </el-row>

      <!-- 数据表格 -->
      <CommonTable
        :columns="columns"
        :data="orderList"
        :loading="loading"
        :total="total"
        :page-num="queryParams.pageNum"
        :page-size="queryParams.pageSize"
        @selection-change="handleSelectionChange"
        @update:page-num="val => queryParams.pageNum = val"
        @update:page-size="val => queryParams.pageSize = val"
        @pagination="getList"
      >
        <template #orderStatus="{ row }">
          <el-tag :type="getStatusType(row.orderStatus)">
            {{ row.orderStatus }}
          </el-tag>
        </template>
        <template #unitPrice="{ row }">
          ¥{{ row.unitPrice }}
        </template>
        <template #totalAmount="{ row }">
          ¥{{ row.totalAmount }}
        </template>
        <template #operate="{ row }">
          <el-button
            size="small"
            type="text"
            icon="Edit"
            @click="handleUpdate(row)"
            v-hasPermi="['marketing:order:edit']"
          >修改</el-button>
          <el-button
            size="small"
            type="text"
            icon="Delete"
            @click="handleDelete(row)"
            v-hasPermi="['marketing:order:remove']"
          >删除</el-button>
        </template>
      </CommonTable>
    </el-card>

    <!-- 添加或修改订单对话框 -->
    <el-dialog :title="title" v-model="open" width="1000px" append-to-body>
      <el-form ref="orderFormRef" :model="form" :rules="rules" label-width="120px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="订单日期" prop="orderDate">
              <el-date-picker
                v-model="form.orderDate"
                type="date"
                placeholder="选择订单日期"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="订单合同" prop="orderContract">
              <el-input v-model="form.orderContract" placeholder="请输入订单合同" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="订单号" prop="orderNo">
              <el-input v-model="form.orderNo" placeholder="请输入订单号" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="订单状态" prop="orderStatus">
              <el-select v-model="form.orderStatus" placeholder="请选择订单状态" style="width: 100%">
                <el-option label="待生产" value="待生产" />
                <el-option label="生产中" value="生产中" />
                <el-option label="待检验" value="待检验" />
                <el-option label="待发货" value="待发货" />
                <el-option label="已发货" value="已发货" />
                <el-option label="已签收" value="已签收" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="客户代码" prop="customerCode">
              <el-select
                v-model="form.customerCode"
                placeholder="请选择客户代码"
                filterable
                style="width: 100%"
                @change="handleFormCustomerCodeChange"
              >
                <el-option
                  v-for="item in customerList"
                  :key="item.customerCode"
                  :label="item.customerCode"
                  :value="item.customerCode"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="客户名称" prop="customerName">
              <el-select
                v-model="form.customerName"
                placeholder="请选择客户名称"
                filterable
                style="width: 100%"
                @change="handleFormCustomerNameChange"
              >
                <el-option
                  v-for="item in customerList"
                  :key="item.customerName"
                  :label="item.customerName"
                  :value="item.customerName"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="产品名称" prop="productName">
              <el-input v-model="form.productName" placeholder="请输入产品名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="产品型号" prop="productModel">
              <el-input v-model="form.productModel" placeholder="请输入产品型号" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8">
            <el-form-item label="数量" prop="quantity">
              <el-input-number v-model="form.quantity" :min="1" style="width: 100%" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="单价" prop="unitPrice">
              <el-input-number v-model="form.unitPrice" :min="0" :precision="2" style="width: 100%" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="总金额" prop="totalAmount">
              <el-input v-model="form.totalAmount" placeholder="自动计算" readonly />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="交货日期" prop="deliveryDate">
              <el-date-picker
                v-model="form.deliveryDate"
                type="date"
                placeholder="选择交货日期"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="发货日期" prop="shipDate">
              <el-date-picker
                v-model="form.shipDate"
                type="date"
                placeholder="选择发货日期"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="发货地址" prop="shipAddress">
              <el-input v-model="form.shipAddress" type="textarea" placeholder="请输入发货地址" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Order">
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import CommonSearchBar from '@/components/CommonSearchBar/index.vue'
import CommonTable from '@/components/CommonTable/index.vue'
import { listOrder, addOrder, updateOrder, delOrder, getOrder, updateOrderStatus } from '@/api/marketing/order'
import { listCustomer } from '@/api/marketing/customer'

// 遮罩层
const loading = ref(true)
// 选中数组
const ids = ref([])
// 非单个禁用
const single = ref(true)
// 非多个禁用
const multiple = ref(true)
// 显示搜索条件
const showSearch = ref(true)
// 总条数
const total = ref(0)
// 订单表格数据
const orderList = ref([])
// 客户列表数据
const customerList = ref([])
// 弹出层标题
const title = ref("")
// 是否显示弹出层
const open = ref(false)

// 表格列配置
const columns = ref([
  { label: '订单日期', prop: 'orderDate', align: 'center', width: '120' },
  { label: '订单合同', prop: 'orderContract', align: 'center', width: '120' },
  { label: '订单号', prop: 'orderNo', align: 'center', width: '150' },
  { label: '客户代码', prop: 'customerCode', align: 'center', width: '120' },
  { label: '客户名称', prop: 'customerName', align: 'center', width: '150' },
  { label: '订单状态', prop: 'orderStatus', align: 'center', width: '100', slot: 'orderStatus' },
  { label: '产品名称', prop: 'productName', align: 'center' },
  { label: '产品型号', prop: 'productModel', align: 'center', width: '120' },
  { label: '数量', prop: 'quantity', align: 'center', width: '80' },
  { label: '单价', prop: 'unitPrice', align: 'center', width: '100', slot: 'unitPrice' },
  { label: '总金额', prop: 'totalAmount', align: 'center', width: '120', slot: 'totalAmount' },
  { label: '交货日期', prop: 'deliveryDate', align: 'center', width: '120' },
  { label: '发货日期', prop: 'shipDate', align: 'center', width: '120' },
  { label: '发货地址', prop: 'shipAddress', align: 'center', showOverflowTooltip: true },
  { label: '发货数量', prop: 'shipQuantity', align: 'center', width: '100' },
  { label: '库存数量', prop: 'stockQuantity', align: 'center', width: '100' },
  { label: '物流信息', prop: 'logisticsInfo', align: 'center', showOverflowTooltip: true }
])

// 查询参数
const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  orderDateRange: null,
  orderNo: null,
  customerCode: null,
  customerName: null,
  productName: null,
  productModel: null,
  deliveryDateRange: null,
  shipDateRange: null
})

// 表单参数
const form = reactive({
  id: null,
  orderDate: null,
  orderContract: null,
  orderNo: null,
  customerCode: null,
  customerName: null,
  orderStatus: '待生产',
  productName: null,
  productModel: null,
  quantity: 1,
  unitPrice: 0,
  totalAmount: 0,
  deliveryDate: null,
  shipDate: null,
  shipAddress: null
})

// 表单校验规则
const rules = reactive({
  orderDate: [
    { required: true, message: "订单日期不能为空", trigger: "change" }
  ],
  orderNo: [
    { required: true, message: "订单号不能为空", trigger: "blur" }
  ],
  productName: [
    { required: true, message: "产品名称不能为空", trigger: "blur" }
  ],
  productModel: [
    { required: true, message: "产品型号不能为空", trigger: "blur" }
  ],
  quantity: [
    { required: true, message: "数量不能为空", trigger: "blur" }
  ],
  unitPrice: [
    { required: true, message: "单价不能为空", trigger: "blur" }
  ]
})

const orderFormRef = ref()

// 计算总金额
const calculatedTotalAmount = computed(() => {
  return (form.quantity * form.unitPrice).toFixed(2)
})

// 监听数量和单价变化，自动计算总金额
const watchForm = () => {
  form.totalAmount = calculatedTotalAmount.value
}

/** 获取状态类型 */
function getStatusType(status) {
  const statusMap = {
    '待生产': 'info',
    '生产中': 'warning',
    '待检验': 'warning',
    '待发货': 'primary',
    '已发货': 'success',
    '已签收': 'success'
  }
  return statusMap[status] || 'info'
}

/** 查询订单列表 */
function getList() {
  loading.value = true
  listOrder(queryParams).then(response => {
    orderList.value = response.rows
    total.value = response.total
    loading.value = false
  }).catch(() => {
    loading.value = false
  })
}

/** 获取客户列表 */
function getCustomerList() {
  listCustomer({ pageSize: 1000 }).then(response => {
    customerList.value = response.rows
  })
}

/** 查询条件客户代码变化 */
function handleCustomerCodeChange(value) {
  if (value) {
    const customer = customerList.value.find(item => item.customerCode === value)
    if (customer) {
      queryParams.customerName = customer.customerName
    }
  } else {
    queryParams.customerName = null
  }
}

/** 查询条件客户名称变化 */
function handleCustomerNameChange(value) {
  if (value) {
    const customer = customerList.value.find(item => item.customerName === value)
    if (customer) {
      queryParams.customerCode = customer.customerCode
    }
  } else {
    queryParams.customerCode = null
  }
}

/** 表单客户代码变化 */
function handleFormCustomerCodeChange(value) {
  if (value) {
    const customer = customerList.value.find(item => item.customerCode === value)
    if (customer) {
      form.customerName = customer.customerName
    }
  } else {
    form.customerName = null
  }
}

/** 表单客户名称变化 */
function handleFormCustomerNameChange(value) {
  if (value) {
    const customer = customerList.value.find(item => item.customerName === value)
    if (customer) {
      form.customerCode = customer.customerCode
    }
  } else {
    form.customerCode = null
  }
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.pageNum = 1
  getList()
}

/** 重置按钮操作 */
function resetQuery() {
  queryParams.orderDateRange = null
  queryParams.orderNo = null
  queryParams.customerCode = null
  queryParams.customerName = null
  queryParams.productName = null
  queryParams.productModel = null
  queryParams.deliveryDateRange = null
  queryParams.shipDateRange = null
  handleQuery()
}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.id)
  single.value = selection.length !== 1
  multiple.value = !selection.length
}

/** 新增按钮操作 */
function handleAdd() {
  reset()
  open.value = true
  title.value = "添加订单"
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset()
  const id = row.id || ids.value[0]
  getOrder(id).then(response => {
    Object.assign(form, response.data)
    open.value = true
    title.value = "修改订单"
  })
}

/** 提交按钮 */
function submitForm() {
  orderFormRef.value.validate((valid) => {
    if (valid) {
      // 计算总金额
      form.totalAmount = calculatedTotalAmount.value
      
      if (form.id != null) {
        updateOrder(form).then(response => {
          ElMessage.success("修改成功")
          open.value = false
          getList()
        })
      } else {
        addOrder(form).then(response => {
          ElMessage.success("新增成功")
          open.value = false
          getList()
        })
      }
    }
  })
}

/** 删除按钮操作 */
function handleDelete(row) {
  const orderIds = row.id || ids.value
  ElMessageBox.confirm('是否确认删除订单编号为"' + orderIds + '"的数据项?', "警告", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning"
  }).then(function() {
    return delOrder(orderIds)
  }).then(() => {
    ElMessage.success("删除成功")
    getList()
  })
}

/** 取消按钮 */
function cancel() {
  open.value = false
  reset()
}

/** 表单重置 */
function reset() {
  form.id = null
  form.orderDate = null
  form.orderContract = null
  form.orderNo = null
  form.customerCode = null
  form.customerName = null
  form.orderStatus = '待生产'
  form.productName = null
  form.productModel = null
  form.quantity = 1
  form.unitPrice = 0
  form.totalAmount = 0
  form.deliveryDate = null
  form.shipDate = null
  form.shipAddress = null
}

onMounted(() => {
  getList()
  getCustomerList()
})
</script>

<style scoped lang="scss">
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-title {
  font-size: 18px;
  font-weight: bold;
  color: #303133;
}

.mb8 {
  margin-bottom: 8px;
}

.dialog-footer {
  text-align: right;
}
</style> 